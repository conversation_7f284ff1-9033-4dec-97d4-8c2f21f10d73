using Hangfire;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.MultiTenancyServer.Core;
using RJO.OrderService.Common;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.MultiTenancyServer;
using RJO.OrderService.Domain.Settings;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Services.DTO.Contract;
using RJO.OrderService.Services.ErpIntegration.AgrisServices.Model;
using RJO.OrderService.Services.Helper;
using RJO.OrderService.Services.Jobs.Jobs;
using System.Globalization;
using System.Text.RegularExpressions;

namespace RJO.OrderService.Services.ErpIntegration.AgrisServices.Helpers;

public class AgrisContractHelper
{
	readonly IBackgroundJobClient _backgroundJobClient;
	readonly CommodityRepository _commodityRepository;
	readonly LocationRepository _locationRepository;
	readonly CustomerRepository _customerRepository;
	readonly TransactionTypeRepository _transactionTypeRepository;
	readonly ContractTypeRepository _contractTypeRepository;
	readonly EmployeeRepository _employeeRepository;
	readonly ContractOfferValidationHelper _contractValidationHelper;
	readonly ITenancyContext<ApplicationTenant> _tenancyContext;
	readonly AppDbContext _dbContext;
	public AgrisContractHelper(IBackgroundJobClient backgroundJobClient, CommodityRepository commodityRepository, LocationRepository locationRepository, ContractOfferValidationHelper contractValidationHelper,
		CustomerRepository customerRepository, TransactionTypeRepository transactionTypeRepository, ContractTypeRepository contractTypeRepository, ITenancyContext<ApplicationTenant> tenancyContext,
		EmployeeRepository employeeRepository, AppDbContext dbContext)
	{
		_backgroundJobClient = backgroundJobClient;
		_commodityRepository = commodityRepository;
		_locationRepository = locationRepository;
		_customerRepository = customerRepository;
		_transactionTypeRepository = transactionTypeRepository;
		_contractTypeRepository = contractTypeRepository;
		_employeeRepository = employeeRepository;
		_contractValidationHelper = contractValidationHelper;
		_tenancyContext = tenancyContext;
		_dbContext = dbContext;
	}

	static string GetContractNumber(string transactionTypeId, string documentNumber)
	{
		switch (transactionTypeId)
		{
			case "F":
				return TradeTypePrefix.Overfill + documentNumber.Substring(1);

			case "C":
				return TradeTypePrefix.Underfill + documentNumber.Substring(1);

			default:
				return TradeTypePrefix.SpotTrade + documentNumber.Substring(1);
		}
	}

	static string GetBuySellId(string transactionTypeId, string purchaseSalesCode, decimal units)
	{
		// Purchase / Sale
		switch (transactionTypeId)
		{
			case "C":
			case "F":
				return units < 0 ? "S" : "P";

			default:
				if (purchaseSalesCode == "S")
					return units < 0 ? "S" : "P";
				// purchaseSalesCode == P
				return units > 0 ? "P" : "S";	
		}
	}

	static bool ValidateValue(List<string> errors, string name, string value)
	{
		if (!string.IsNullOrEmpty(value))
			return true;
		errors.Add(name + " is required");
		return false;
	}

	public async Task<string> CreateSpotTrade(AgrisSpotTradeDto request, string email)
	{
		// Assert to verify data
		var errors = new List<string>();

		if (request.IsDelete)
			errors.Add("Delete is not implemented");

		var dateReg = new Regex(@"\d{4}-(0[1-9]|1[0-2])"); //Verify whether date entered in yyyy-MM format.
		if (ValidateValue(errors, "ContractFuturesMonth", request.ContractFuturesMonth) && !dateReg.IsMatch(request.ContractFuturesMonth))
			errors.Add("MonthYear Not a valid format yyyy-MM");

		Commodity commodity = null;
		if (ValidateValue(errors, "Commodity", request.Commodity))
		{
			commodity = await _commodityRepository.GetSingleOrDefault(x => x.Number == request.Commodity);
			if (commodity == null)
				errors.Add("Bad Commodity");
		}

		var buySellId = GetBuySellId(request.TransactionTypeId, request.PurchaseSalesCode, request.Units ?? 0);

		Customer customer = null;
		if (ValidateValue(errors, "NameId", request.NameId))
		{
			customer = await _customerRepository.GetSingleOrDefault(x => x.Number == request.NameId);
			if (customer == null)
				errors.Add("Bad Customer");
		}

		Employee employee = null;
		if (ValidateValue(errors, "Email", email))
		{
			employee = await _employeeRepository.GetSingleOrDefault(x => x.Email == email);
			if (employee == null)
				errors.Add($"Invalid email: {email}");
		}

		Location destinationLocation = null;
		GroupedLocation groupedLocation = null;
		if (ValidateValue(errors, "DocumentLocation", request.DocumentLocation))
		{
			groupedLocation = (await _dbContext.SpotToGroupedLocationMaps.Include(a => a.GroupedLocation).ThenInclude(a => a.DestinationLocation).FirstOrDefaultAsync(a => a.DocumentLocation == request.DocumentLocation && a.IsActive))?.GroupedLocation;
			if (groupedLocation == null || groupedLocation.Id == Guid.Empty)
				errors.Add("Region not found for the document location");
			else if (!groupedLocation.IsActive)
				errors.Add("Grouped location is inactive");
			else
			{
				destinationLocation = groupedLocation.DestinationLocation;
				if (!destinationLocation.IsActive)
					errors.Add("The document location is inactive");
			}
		}

		Location location = null;
		if (ValidateValue(errors, "Location", request.Location))
		{
			location = await _locationRepository.GetSingleOrDefault(x => x.Number == request.Location);
			if (location == null)
				errors.Add("Bad Delivery Location");
			else if (!location.IsActive)
				errors.Add("The delivery location is inactive");
		}

		DateTime positionDate;
		if (!DateTime.TryParseExact(request.PositionDateTime, "MM/dd/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out positionDate))
			errors.Add("PositionDateTime Not a valid format MM/dd/yyyy");

		// No, we can create multi contracts with same Agris Numbers
		var erpNumber = GetContractNumber(request.TransactionTypeId, request.DocumentNumber);

		if (errors.Count > 0)
		{
			var errorStr = string.Join(';', errors);
			throw new BusinessException(errorStr);
		}

		var commodityId = commodity.Id;
		var cropYear = commodity.CropStartYear;
		var isSell = buySellId == "S";
		var locationId = location.Id;
		var deliveryLocationId = destinationLocation.Id;
		var quantity = Convert.ToDecimal(Math.Abs(request.Units ?? 0));
		await _contractValidationHelper.ValidateLimits(isSell, deliveryLocationId, quantity, groupedLocation?.Id);

		var month = int.Parse(request.ContractFuturesMonth.Substring(5, 2));
		var monthCode = FutureMonthsHelper.GetMonthCodeFromMonthNumber(month);
		var yearCode = request.ContractFuturesMonth.Substring(2, 2);
		var transactionType = await _transactionTypeRepository.GetSingleOrDefault(x => x.Name == TransactionTypeName.CashContract);
		var contractType = await _contractTypeRepository.GetSingleOrDefault(x => x.Name == ContractTypeName.FlatPrice);

		var contractDto = new ContractDto();
		{
			contractDto.Id = Guid.NewGuid();
			contractDto.CommodityId = commodityId;
			contractDto.CropYear = cropYear;
			contractDto.CustomerId = customer.Id;
			contractDto.EmployeeId = employee.Id;
			contractDto.LocationId = locationId;
			contractDto.DeliveryLocationId = deliveryLocationId;

			contractDto.Number = erpNumber;
			contractDto.IsSell = isSell;
			contractDto.DeliveryStartDate = positionDate;
			contractDto.DeliveryEndDate = positionDate;
			contractDto.Price = request.ContractPrice;
			contractDto.FuturesPrice = request.FuturesPrice;
			contractDto.PostedBasis = request.BasisPrice;
			contractDto.PushBasis = 0;
			contractDto.Quantity = quantity;
			contractDto.InternalCode = ContractIdentityGenerator.NewContractNumber(contractType.Id, isSell);
			contractDto.TransactionTypeId = transactionType.Id;
			contractDto.ContractTypeId = contractType.Id;
			contractDto.FuturesMonth = monthCode + yearCode;
			contractDto.Comments = "";
			contractDto.RegionId = groupedLocation.RegionId;
		}

		_backgroundJobClient.Enqueue<SpotTradeJob>(x => x.Perform(new()
		{
			Contract = contractDto,
			TenantId = _tenancyContext.Tenant.Id
		}, null));

		return contractDto.InternalCode;
	}
}
