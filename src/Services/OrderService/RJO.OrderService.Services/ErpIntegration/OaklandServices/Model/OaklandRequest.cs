namespace RJO.OrderService.Services.ErpIntegration.OaklandServices.Model;

public class OaklandQuery
{
	public string property { get; set; } = "modified";
	public string type { get; set; } = "date";
	public long lowerBound { get; set; }
	public long upperBound { get; set; } = DateTime.Now.ToUnixTimeSeconds() * 1000;
}

public class OaklandRequest
{
	public int start { get; set; }
	public int show { get; set; }
	public string after_key { get; set; }
	public OaklandQuery? query { get; set; }

	public OaklandRequest(int start, int show, long lowerBound = 0)
	{
		this.start = start;
		this.show = show;
		if (lowerBound > 0)
		{
			query = new();
			query.lowerBound = lowerBound * 1000; //  millisec
		}
	}

	public void Next(string key) => start += show;
	// after_key = key;
}
