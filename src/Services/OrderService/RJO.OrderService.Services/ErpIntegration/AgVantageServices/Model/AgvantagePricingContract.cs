//// Request / response messages between Hrvyst and AgVantage
////
using System.Text.Json.Serialization;

namespace RJO.OrderService.Services.ErpIntegration.AgVantageServices.Model;

public class AgvantagePricingContractRequest
{
	[JsonPropertyName("buyerID")]
	public int BuyerID { get; set; }

	[JsonPropertyName("date")]
	public string Date { get; set; }

	[JsonPropertyName("pricedBushels")]
	public decimal PricedBushels { get; set; }

	[JsonPropertyName("finalPrice")]
	public decimal FinalPrice { get; set; }

	[JsonPropertyName("basisAmount")]
	public decimal BasisAmount { get; set; }

	[JsonPropertyName("futuresPrice")]
	public decimal FuturesPrice { get; set; }
	[JsonPropertyName("futuresYear")]
	public int? FuturesYear { get; set; }

	[JsonPropertyName("futuresMonth")]
	public int? FuturesMonth { get; set; }

#pragma warning disable CA1720
	[JsonPropertyName("signed")]
	public bool Signed { get; set; }
#pragma warning restore CA1720

	[JsonPropertyName("comments")]
	public ICollection<string> Comments { get; set; } = new HashSet<string>();
}


// response
public class AgvantagePricingSuccessResponse 
{
	[JsonPropertyName("id")]
	public string Id { get; set; }

	[JsonPropertyName("type")]
	public int Type { get; set; }

	[JsonPropertyName("buyerID")]
	public int BuyerId { get; set; }

	[JsonPropertyName("cropID")]
	public string CropId { get; set; }

	[JsonPropertyName("controlDate")]
	public string ControlDate { get; set; }

	[JsonPropertyName("controlNumber")]
	public long ControlNumber { get; set; }

	[JsonPropertyName("customerID")]
	public int CustomerId { get; set; }

	[JsonPropertyName("deliveryBasis")]
	public string DeliveryBasis { get; set; }

	[JsonPropertyName("deliveryTerms")]
	public string DeliveryTerms { get; set; }

	[JsonPropertyName("deliveryStartDate")]
	public string DeliveryStartDate { get; set; }

	[JsonPropertyName("deliveryEndDate")]
	public string DeliveryEndDate { get; set; }

	[JsonPropertyName("locationID")]
	public int LocationId { get; set; }

	[JsonPropertyName("cashPrice")]
	public decimal CashPrice { get; set; }

	[JsonPropertyName("basisAmount")]
	public decimal BasisAmount { get; set; }

	[JsonPropertyName("futuresPrice")]
	public decimal FuturesPrice { get; set; }

	[JsonPropertyName("futuresMonth")]
	public int? FuturesMonth { get; set; }

	[JsonPropertyName("futuresYear")]
	public int? FuturesYear { get; set; }

	[JsonPropertyName("contractedBushels")]
	public decimal ContractedBushels { get; set; }

	[JsonPropertyName("adjustments")]
	public List<PricingResponseAdjustment> Adjustments { get; set; }

	[JsonPropertyName("prices")]
	public List<AgvantageResponsePrice> Prices { get; set; }

	/* //Fields in AgvantageNewContractRequest but not here
	[JsonPropertyName("grade")]
	public int Grade { get; set; }
	[JsonPropertyName("finalPricingDate")]
	public string FinalPricingDate { get; set; }
	*/

	[JsonPropertyName("isContract")]
	public bool IsContract { get; set; }

#pragma warning disable CA1720
	[JsonPropertyName("signed")]
	public bool Signed { get; set; }
#pragma warning restore CA1720

	[JsonPropertyName("buyer")]
	public string Buyer { get; set; }

	[JsonPropertyName("crop")]
	public string Crop { get; set; }

	[JsonPropertyName("cropCode")]
	public string CropCode { get; set; }

	[JsonPropertyName("comment")]
	public string Comment { get; set; }

	[JsonPropertyName("created")]
	public string Created { get; set; }

	[JsonPropertyName("description")]
	public string Description { get; set; }

	[JsonPropertyName("pricedBushels")]
	public int PricedBushels { get; set; }

	[JsonPropertyName("receivedBushels")]
	public int ReceivedBushels { get; set; }

	[JsonPropertyName("settledBushels")]
	public int SettledBushels { get; set; }

	[JsonPropertyName("comments")]
	public List<string> Comments { get; set; }

	[JsonPropertyName("scaletickets")]
	public List<PricingResponseScaleticket> Scaletickets { get; set; }

	//[JsonPropertyName("terms")]
	//public Collection<string> Terms { get; set; }    // ignore
}

public class PricingResponseScaleticket
{
	// Define properties for Scaleticket if available
}
