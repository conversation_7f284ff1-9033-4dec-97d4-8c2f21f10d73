// Request / response messages between Hrvyst and AgVantage
//
using System.Collections.ObjectModel;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace RJO.OrderService.Services.ErpIntegration.AgVantageServices.Model;

// Request
public class AgvantageNewContractRequest
{
	[JsonPropertyName("type")]
	public int Type { get; set; }

	[JsonPropertyName("buyerID")]
	public int BuyerId { get; set; }

	[JsonPropertyName("cropID")]
	public string CropId { get; set; }

	[JsonPropertyName("grade")]
	public int Grade { get; set; }

	[JsonPropertyName("controlDate")]
	public string ControlDate { get; set; } 

	[JsonPropertyName("controlNumber")]
	public long ControlNumber { get; set; }

	[JsonPropertyName("customerID")]
	public int CustomerId { get; set; }

	[JsonPropertyName("deliveryBasis")]
	public string DeliveryBasis { get; set; }

	[JsonPropertyName("deliveryTerms")]
	public string DeliveryTerms { get; set; }

	[JsonPropertyName("deliveryStartDate")]
	public string DeliveryStartDate { get; set; } 

	[JsonPropertyName("deliveryEndDate")]
	public string DeliveryEndDate { get; set; }

	[JsonPropertyName("locationID")]
	public int LocationId { get; set; }

	[JsonPropertyName("cashPrice")]
	public decimal CashPrice { get; set; }

	[JsonPropertyName("basisAmount")]
	public decimal BasisAmount { get; set; }

	[JsonPropertyName("futuresPrice")]
	public decimal FuturesPrice { get; set; }

	[JsonPropertyName("futuresMonth")]
	public int? FuturesMonth { get; set; }

	[JsonPropertyName("futuresYear")]
	public int? FuturesYear { get; set; }

	[JsonPropertyName("finalPricingDate")]
	public string FinalPricingDate { get; set; }
	
	[JsonPropertyName("contractedBushels")]
	public decimal ContractedBushels { get; set; }

	[JsonPropertyName("adjustments")]
	public ICollection<NewContractAdjustment> Adjustments { get; set; } = new HashSet<NewContractAdjustment>();

	//public decimal? StrikePrice { get; set; }
	//public decimal? MinimumPrice { get; set; }
	//public int? OptionsMonth { get; set; }
	//public int? OptionsYear { get; set; }
	//public int? CoveredBy { get; set; }
	//public string LastExerciseDate { get; set; }  

	[JsonExtensionData]
	public Dictionary<string, JsonElement> CustomProperties { get; set; } = new();
}

public class NewContractAdjustment
{
	[JsonPropertyName("id")]
	public string Id { get; set; }

	[JsonPropertyName("rate")]
	public decimal Rate { get; set; }

	[JsonPropertyName("amount")]
	public decimal Amount { get; set; }
}


// Response
public class AgvantageNewSuccessResponse
{
	[JsonPropertyName("id")]
	public string Id { get; set; }
}
