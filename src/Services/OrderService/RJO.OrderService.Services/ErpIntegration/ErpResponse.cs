using RJO.OrderService.Domain;

namespace RJO.OrderService.Services.ErpIntegration;

/// <summary>
/// API Response
/// </summary>
public class ErpResponse
{
	ErpStatus _erpStatus;

	public bool Success { get; set; }

	public ErpStatus ErpStatus
	{
		get => Success && _erpStatus != ErpStatus.None ? (string.IsNullOrEmpty(ContractNumber) ? ErpStatus.Pending : ErpStatus.Success) : _erpStatus;
		set => _erpStatus = value;
	}

	public int HttpStatusCode { get; set; }
	public string ContractNumber { get; set; }
	public string ScheduleNumber { get; set; }
	public string RequestPayload { get; set; }
	public string ResponsePayload { get; set; }
	public List<string> ErrorList { get; set; }

	public ErpResponse()
	{
		Success = true;
		ErrorList = new();
	}

	public void AddError(string errMsg)
	{
		if (Success)
			Success = false;
		ErrorList.Add(errMsg);
	}

	public string ErrorMessage()
	{
		if (ErrorList == null || ErrorList.Count == 0)
			return string.Empty;
		return string.Join(';', ErrorList);
	}

	public static ErpResponse NewOK(string contractNumber = "")
	{
		var erpResponse = new ErpResponse();
		erpResponse.Success = true;
		erpResponse.HttpStatusCode = (int)System.Net.HttpStatusCode.OK;
		if (!string.IsNullOrEmpty(contractNumber))
			erpResponse.ContractNumber = contractNumber;
		return erpResponse;
	}

	public static ErpResponse NewNone(string message = "")
	{
		var erpResponse = new ErpResponse();
		erpResponse.ErpStatus = ErpStatus.None;
		erpResponse.Success = true;
		erpResponse.HttpStatusCode = 0;
		if (!string.IsNullOrEmpty(message))
			erpResponse.ErrorList.Add(message);

		return erpResponse;
	}

	public static ErpResponse NewError(string errMsg) => NewError(0, errMsg);

	public static ErpResponse NewError(int statusCode, string errMsg)
	{
		var erpResponse = new ErpResponse();
		erpResponse.Success = false;
		erpResponse.HttpStatusCode = statusCode;
		if (!string.IsNullOrEmpty(errMsg))
			erpResponse.ErrorList.Add(errMsg);

		return erpResponse;
	}

	public static ErpResponse NewErrors(int statusCode, List<string> errorList)
	{
		var erpResponse = new ErpResponse();
		erpResponse.Success = false;
		erpResponse.HttpStatusCode = statusCode;
		erpResponse.ErpStatus = ErpStatus.Fail;
		if (errorList != null)
			erpResponse.ErrorList.AddRange(errorList);
		return erpResponse;
	}
}
