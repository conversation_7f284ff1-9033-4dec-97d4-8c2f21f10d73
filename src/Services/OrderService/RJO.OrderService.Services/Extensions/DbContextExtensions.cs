using Microsoft.EntityFrameworkCore;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.DTO.Catalogs.FutureMonths;

namespace RJO.OrderService.Services.Extensions;

public static class DbContextExtensions
{
	public static async Task<EContractState> GetStatusContractWasBornInAsync(this AppDbContext dbContext, Guid parentContractId) =>
		await dbContext.HistoricalContracts
			.AsNoTracking()
			.Where(x => x.ContractId == parentContractId && x.Event == EContractEvent.Create)
			.Select(x => x.Status)
			.FirstOrDefaultAsync();

	public static async Task<List<FutureMonthsItemDto>> ToFutureMonthsItemDtoListAsync(this IQueryable<FutureMonthsItemDto> query, bool excludeExpiredMonths = false, CancellationToken cancellationToken = default)
	{
		var currentYear = DateTime.Now.Year;
		var currentMonth = DateTime.Now.Month;
		var result = query
			.Distinct()
			.Select(x => new FutureMonthsItemDto
			{
				Name = x.Name,
				Year = x.Year,
				Month = x.Month,
				IsExpired = x.Year < currentYear || (x.Year == currentYear && x.Month < currentMonth)
			});
		
		if (excludeExpiredMonths)
		{
			result = result.Where(x => !x.IsExpired);
		}

		return await result
			.OrderBy(x => x.IsExpired)
			.ThenBy(x => x.Year)
			.ThenBy(x => x.Month)
			.ToListAsync(cancellationToken);
	}
}
