using Hangfire;
using Hangfire.Server;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.Resources;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.MultiTenancyServer;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Persistence.Repositories.Logs;
using RJO.OrderService.Services.Services.OrderProcess;

namespace RJO.OrderService.Services.Jobs;

[Queue("default")]
[AutomaticRetry(Attempts = 3, DelaysInSeconds = [30, 60, 120])]
public class ProcessErpContractJob
{
    readonly IServiceProvider _serviceProvider;
    readonly ILogger<ProcessErpContractJob> _logger;

    public ProcessErpContractJob(IServiceProvider serviceProvider, ILogger<ProcessErpContractJob> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task Perform(ProcessErpContractJobOptions jobOptions, PerformContext performContext)
    {
        _logger.LogInformation("ProcessErpContractJob started for Contract {ContractId} in Tenant {TenantId}", 
            jobOptions.ContractId, jobOptions.TenantId);

        AssertionConcern.ArgumentIsNotNull(jobOptions, GeneralResources.RequiredValueIsNotPresent);
        
        using var scope = _serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var contractRepository = scope.ServiceProvider.GetRequiredService<ContractRepository>();
        var logWorkflow = scope.ServiceProvider.GetRequiredService<LogWorkflowRepository>();
        var erpIntegrationAgent = scope.ServiceProvider.GetRequiredService<ErpIntegrationAgent>();

        // Set tenant context
        dbContext.TenancyContext.Tenant = new ApplicationTenant
        {
            Id = jobOptions.TenantId,
            DisplayName = "ProcessErpContractJob"
        };

        try
        {
            // Get the contract
            var contract = await contractRepository.GetById(jobOptions.ContractId);
            if (contract == null)
            {
                _logger.LogWarning("Contract {ContractId} not found for ERP processing", jobOptions.ContractId);
                return;
            }

            // Check if contract should still be sent to ERP
            if (!contract.ShouldSendFillErp())
            {
                _logger.LogInformation("Contract {ContractId} no longer needs ERP processing", jobOptions.ContractId);
                return;
            }

            logWorkflow.SetContract(contract.Id);
            await logWorkflow.AddLog("ProcessErpContractJob", "Starting background ERP processing");

            // Process the contract based on event type
            if (string.Equals(jobOptions.ContractEvent, EContractEvent.Price.ToString(), StringComparison.Ordinal))
            {
                await logWorkflow.AddLog("ProcessErpContractJob", "Processing pricing in background");
                await erpIntegrationAgent.ProcessPricing(contract);
            }
            else
            {
                await logWorkflow.AddLog("ProcessErpContractJob", "Processing creation in background");
                await erpIntegrationAgent.ProcessCreation(contract);
            }

            await logWorkflow.AddLog("ProcessErpContractJob", "Background ERP processing completed successfully");
            _logger.LogInformation("ProcessErpContractJob completed successfully for Contract {ContractId}", jobOptions.ContractId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ProcessErpContractJob failed for Contract {ContractId} in Tenant {TenantId}", 
                jobOptions.ContractId, jobOptions.TenantId);
            
            try
            {
                await logWorkflow.AddLog("ProcessErpContractJob", $"Background ERP processing failed: {ex.Message}");
            }
            catch
            {
                // Ignore logging errors to prevent infinite loops
            }
            
            throw; // Re-throw to trigger Hangfire retry mechanism
        }
    }
}
