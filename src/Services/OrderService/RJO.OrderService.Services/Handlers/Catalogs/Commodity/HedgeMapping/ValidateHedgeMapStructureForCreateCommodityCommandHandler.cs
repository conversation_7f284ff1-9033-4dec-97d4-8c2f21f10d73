using LaunchDarkly.Sdk;
using LaunchDarkly.Sdk.Server;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.DTO.Catalogs.HedgeMapping;
using RJO.OrderService.Services.Helper;

namespace RJO.OrderService.Services.Handlers.Catalogs.Commodity.HedgeMapping;

public record ValidateHedgeMapStructureForCreateCommodityCommand : IRequest<HedgeMapValidationResponseDto>, ITransactionalRequest
{
	public HedgeMapValidationForSingleTemplateDto HedgeValidation { get; init; }
	public Context FlagContext { get; init; }
}

public sealed class ValidateHedgeMapStructureForCreateCommodityCommandHandler : IRequestHandler<ValidateHedgeMapStructureForCreateCommodityCommand, HedgeMapValidationResponseDto>
{
	readonly HedgeMappingHelper _hedgeMappingHelper;
	readonly AppDbContext _dbContext;
	readonly LdClient _ldClient;

	public ValidateHedgeMapStructureForCreateCommodityCommandHandler(AppDbContext dbContext, HedgeMappingHelper hedgeMappingHelper, LdClient ldClient)
	{
		_hedgeMappingHelper = hedgeMappingHelper;
		_dbContext = dbContext;
		_ldClient = ldClient;
	}

	public async Task<HedgeMapValidationResponseDto> Handle(ValidateHedgeMapStructureForCreateCommodityCommand request, CancellationToken cancellationToken)
	{
		var validationResponseDto = new HedgeMapValidationResponseDto();
		var regions = await _dbContext.Regions
			.AsNoTracking()
			.Where(x => x.IsEnabled)
			.Select(x => x.Name)
			.ToListAsync(cancellationToken);
		var count = regions.Count;

		for (var i = 1; i <= count; i++)
		{
			var result = await _hedgeMappingHelper.ValidateAndPersistExcelDocument(new()
			{
				EndDate = request.HedgeValidation.EndDate,
				File = request.HedgeValidation.File,
				HasFourthCrop = request.HedgeValidation.HasFourthCrop,
				Months = request.HedgeValidation.Months,
				Name = request.HedgeValidation.Name,
				NumberOfCropYears = request.HedgeValidation.NumberOfCropYears,
				ProductId = request.HedgeValidation.ProductId,
				StartDate = request.HedgeValidation.StartDate,
				RegionNames = regions,
				ExcelIndex = i,
				SingleTemplate = false
			}, request.FlagContext, cancellationToken);

			if (!validationResponseDto.HasErrors)
				validationResponseDto.HasErrors = result.HasErrors;
			validationResponseDto.FileId = result.FileId;

			// Initialize the Errors list before adding elements
			validationResponseDto.Errors ??= new();

			if(result.HasErrors)
				foreach (var error in result.Errors)
				{
					validationResponseDto.Errors.Add("In Sheet" + i + " " + error);
				}
		}

		return validationResponseDto;
	}
}
