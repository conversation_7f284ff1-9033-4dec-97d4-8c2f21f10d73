using MediatR;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Persistence;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Services.DTO.Catalogs.Commodity;

namespace RJO.OrderService.Services.Handlers.Catalogs.Commodity;

public class ChangeDnhCommand : IRequest<Unit>, ITransactionalRequest
{
	public IList<CommodityDnhChangesDto> Changes { get; set; }
}

public class ChangeDnhCommandHandler : IRequestHandler<ChangeDnhCommand, Unit>
{
	readonly DoNotHedgeLogRepository _doNotHedgeLogRepository;
	readonly CommodityRepository _commodityRepository;
	readonly UnitOfWork _unitOfWork;

	public ChangeDnhCommandHandler(UnitOfWork unitOfWork,
		DoNotHedgeLogRepository doNotHedgeLogRepository, CommodityRepository commodityRepository)
	{
		_doNotHedgeLogRepository = doNotHedgeLogRepository;
		_unitOfWork = unitOfWork;
		_commodityRepository = commodityRepository;
	}

	public async Task<Unit> Handle(ChangeDnhCommand request, CancellationToken cancellationToken)
	{
		AssertionConcern.ArgumentIsNotNull(request, "Request data is required");
		AssertionConcern.ArgumentIsNotNull(request.Changes, "Request data is required");
		AssertionConcern.ArgumentIsBiggerThan(request.Changes.Count, 0, "Request data is required");
		for (var i = 0; i < request.Changes.Count; i++)
		{
			var change = request.Changes[i];
			AssertionConcern.ArgumentIsNotNull(change, $"Request element [{i}] is inccorrect");
			AssertionConcern.ArgumentIsTrue(await _commodityRepository.Exists(change.CommodityId), $"Request element [{i}] has an invalid commodity");
			if (change.ActivateDnh)
			{
				if (!await _doNotHedgeLogRepository.IsActiveLog(change.CommodityId, change.CropYear, change.RegionId))
				{
					await _doNotHedgeLogRepository.Activate(change.CommodityId, change.CropYear, change.RegionId);
				}
			}
			else
			{
				if (await _doNotHedgeLogRepository.IsActiveLog(change.CommodityId, change.CropYear, change.RegionId))
				{
					await _doNotHedgeLogRepository.Inactivate(change.CommodityId, change.CropYear, change.RegionId);
				}
			}
		}

		await _unitOfWork.CommitAsync();
		return Unit.Value;
	}
}
