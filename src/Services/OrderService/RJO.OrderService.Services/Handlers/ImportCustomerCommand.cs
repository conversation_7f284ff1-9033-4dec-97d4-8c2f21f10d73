using ClosedXML.Excel;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Domain;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.DTO.Import.Contract;
using RJO.OrderService.Services.DTO.Import.Customer;

namespace RJO.OrderService.Services.Handlers;

public class ImportCustomerCommand : IRequest<ImportCustomerResultDto>, ITransactionalRequest
{
	public ImportDto ImportInformation { get; set; }
}

public sealed class ImportCustomerCommandHandler(AppDbContext dbContext) : IRequestHandler<ImportCustomerCommand, ImportCustomerResultDto>
{
	const string CustomerToImport = "CustomerToLoad";
	const int MaxErrorsAllowed = 100; 
	const int MaxFileSizeMb = 25; 
	const string DefaultCountry = "USA"; 

	// Column indices
	const int ErpNumber = 1;
	const int FirstName = 2;
	const int LastName = 3;
	const int Address = 4;
	const int City = 5;
	const int State = 6;
	const int Zip = 7;
	const int WorkPhone = 8;
	const int PhoneNumber = 9;
	const int Email = 10;
	const int IsMobilAppEnabled = 11;
	const int MobileAppLimit = 12;
	const int MaxColumns = MobileAppLimit;

	public async Task<ImportCustomerResultDto> Handle(ImportCustomerCommand request, CancellationToken cancellationToken)
	{
		using var workbook = ReadFileToWorkbook(request);
		var ws = workbook.Worksheets.FirstOrDefault(s => s.Name == CustomerToImport);
		AssertionConcern.ArgumentIsNotNull(ws, $"Tab {CustomerToImport} is not found");

		var data = new ImportCustomerResultDto();
		var rows = ws.RowsUsed();
		if (rows.Count() < 2)
			return data;

		var customerDict = await dbContext.Customers.ToDictionaryAsync(x => x.Number, x => x, cancellationToken);
		var customers = new List<Customer>(rows.Count()-1);
		foreach (var row in rows.Skip(1))
		{
			try
			{
				if (IsEmptyRow(row)) 
					continue; 

				await ProcessCustomerRecord(customerDict, row, cancellationToken);
			}
			catch (Exception ed)
			{
				data.Errors.Add(new()
				{
					Error = ed.Message,
					Data = string.Join(",", row.Cells().Select(c => c.GetValue<string>()))
				});
			}
			if (data.Errors.Count > MaxErrorsAllowed)
				throw new BusinessException("Process is stopped due to too many data errors");
		}

		await dbContext.SaveChangesAsync(cancellationToken);
		return data;
	}

	static XLWorkbook ReadFileToWorkbook(ImportCustomerCommand fileInfo)
	{
		AssertionConcern.ArgumentIsNotNull(fileInfo.ImportInformation, "Information is missing");
		AssertionConcern.ArgumentIsNotNull(fileInfo.ImportInformation.File, "Import file not found");
		AssertionConcern.ArgumentIsNotTrue(fileInfo.ImportInformation.File == null || fileInfo.ImportInformation.File.Length <= 0, "File is empty");
		AssertionConcern.ArgumentIsNotTrue(Math.Round((decimal)fileInfo.ImportInformation.File.Length / 1048576) > MaxFileSizeMb, "File size exceeds the expected 25Mb");
		
		XLWorkbook workbook;
		try
		{
			using (var stream = new MemoryStream())
			{
				fileInfo.ImportInformation.File.CopyTo(stream);
				workbook = new XLWorkbook(stream);
			}
		}
		catch (Exception ed)
		{
			throw new BusinessException("File could not be loaded", ed);
		}

		return workbook;
	}

	static bool IsEmptyRow(IXLRow row) =>
		Enumerable.Range(1, MaxColumns).All(col => string.IsNullOrEmpty(row.Cell(col).GetString()));

	async Task<Customer> ProcessCustomerRecord(Dictionary<string, Customer> customerDict, IXLRow row, CancellationToken cancellationToken)
	{
		var number = row.Cell(ErpNumber).GetString().Trim(); 
		var firstName = row.Cell(FirstName).GetString().Trim(); 
		var lastName = row.Cell(LastName).GetString().Trim();
		var workPhoneNumber = row.Cell(WorkPhone).GetString().Trim();
		var phoneNumber = row.Cell(PhoneNumber).GetString().Trim();
		var street = row.Cell(Address).GetString().Trim();
		var city = row.Cell(City).GetString().Trim();
		var state = row.Cell(State).GetString().Trim();
		var zipCode = row.Cell(Zip).GetString().Trim();
		var country = DefaultCountry;
		var email = row.Cell(Email).GetString().Trim();

		var isMobileAppEnabled = false;
		var cellValue = row.Cell(IsMobilAppEnabled).GetString().Trim();
		if (!string.IsNullOrEmpty(cellValue))
		{
			AssertionConcern.ArgumentIsTrue(bool.TryParse(cellValue, out isMobileAppEnabled), "Invalid value for IsMobileAppEnabled");
		}

		int? mobileAppLimit = null;
		cellValue = row.Cell(MobileAppLimit).GetString().Trim();
		if (!string.IsNullOrEmpty(cellValue))
		{
			AssertionConcern.ArgumentIsTrue(int.TryParse(cellValue, out var limit), "Invalid Number for MobileAppLimit");
			mobileAppLimit = limit;
		}

		if (!customerDict.TryGetValue(number, out var customer))
		{
			var newCustomer = new Customer(number, firstName, lastName, workPhoneNumber, phoneNumber,
				street, city, state, zipCode, country, email, isMobileAppEnabled, mobileAppLimit);
			newCustomer.ChangeAllowSms(true); 
			await dbContext.Customers.AddAsync(newCustomer, cancellationToken);
			return newCustomer;
		}

		if (customer.FirstName != firstName)
			customer.ChangeFirstName(firstName);
		if (customer.LastName != lastName)
			customer.ChangeLastName(lastName);
		if (customer.WorkPhoneNumber != workPhoneNumber)
			customer.ChangeWorkPhone(workPhoneNumber);
		if (customer.PhoneNumber != phoneNumber)
			customer.ChangePhone(phoneNumber);
		if (customer.Street != street)
			customer.ChangeAddressStreet(street);
		if (customer.City != city)
			customer.ChangeAddressCity(city);
		if (customer.State != state)
			customer.ChangeAddressState(state);
		if (customer.ZipCode != zipCode)
			customer.ChangeAddressZipCode(zipCode);
		if (customer.Email != email)
			customer.ChangeEmail(email);
		if (customer.IsMobileAppEnable != isMobileAppEnabled && (!isMobileAppEnabled || customer.MobileAppLimit != mobileAppLimit))
			customer.ChangeMobile(isMobileAppEnabled, mobileAppLimit);

		return customer;
	}
}
