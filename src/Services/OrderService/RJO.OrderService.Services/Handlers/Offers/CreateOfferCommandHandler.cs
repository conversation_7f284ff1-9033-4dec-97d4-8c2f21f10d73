using LaunchDarkly.Sdk;
using LaunchDarkly.Sdk.Server;
using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.Settings;
using RJO.OrderService.Persistence;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Extensions;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Persistence.Repositories.Logs;
using RJO.OrderService.Services.DTO.Bidsheet;
using RJO.OrderService.Services.DTO.Offer;
using RJO.OrderService.Services.Features.Notifications;
using RJO.OrderService.Services.Features.Notifications.AutoRefresh;
using RJO.OrderService.Services.Features.Notifications.NotificationGroups.Commands;
using RJO.OrderService.Services.Features.Notifications.Notifications.Commands;
using RJO.OrderService.Services.Helper;
using RJO.OrderService.Services.Services;
using RJO.OrderService.Services.Services.OfferProcess;
using RJO.OrderService.Services.Services.OrderProcess;

namespace RJO.OrderService.Services.Handlers.Offers;

public sealed record CreateOfferCommand(OfferCreateDto Offer, bool IsMobile, Context FlagContext) : IRequest<string>, ITransactionalRequest;

public sealed class CreateOfferCommandHandler : IRequestHandler<CreateOfferCommand, string>
{
	readonly UnitOfWork _unitOfWork;
	readonly OfferRepository _offerRepository;
	readonly OfferWorkflowContext _offerWorkflowContext;
	readonly BidSheetHelper _bidSheetHelper;
	readonly ContractOfferValidationHelper _contractOfferValidationHelper;
	readonly LogWorkflowRepository _logWorkflow;
	readonly ErpIntegrationAgent _erpIntegrationAgent;
	readonly EmployeeRepository _employeeRepository;
	readonly MetadataDomainService _metadataDomainService;
	readonly IMediator _mediator;
	readonly LdClient _ldClient;
	readonly AppDbContext _dbContext;
	readonly CurrentUser _currentUser;

	public CreateOfferCommandHandler(UnitOfWork unitOfWork, OfferRepository offerRepository, OfferWorkflowContext offerWorkflowContext, BidSheetHelper bidSheetHelper,
		ContractOfferValidationHelper contractOfferValidationHelper, LogWorkflowRepository logWorkflow, ErpIntegrationAgent erpIntegrationAgent,
		EmployeeRepository employeeRepository, MetadataDomainService metadataDomainService,
		IMediator mediator, LdClient ldClient, AppDbContext dbContext, CurrentUser currentUser)
	{
		_unitOfWork = unitOfWork;
		_offerRepository = offerRepository;
		_offerWorkflowContext = offerWorkflowContext;
		_bidSheetHelper = bidSheetHelper;
		_contractOfferValidationHelper = contractOfferValidationHelper;
		_logWorkflow = logWorkflow;
		_erpIntegrationAgent = erpIntegrationAgent;
		_employeeRepository = employeeRepository;
		_metadataDomainService = metadataDomainService;
		_mediator = mediator;
		_ldClient = ldClient;
		_dbContext = dbContext;
		_currentUser = currentUser;
	}

	public async Task<string> Handle(CreateOfferCommand request, CancellationToken cancellationToken)
	{
		BidSheetItemDto bidSheetInformation = null;

		var customerId = request.Offer.CustomerId;
		var customer = await _dbContext.Customers.FirstOrDefaultAsync(x => x.Id == customerId, cancellationToken);
		if (customer == null && !string.IsNullOrEmpty(request.Offer.CustomerNumber))
		{
			customer = await _dbContext.Customers.FirstOrDefaultAsync(x => x.Number == request.Offer.CustomerNumber, cancellationToken);
			AssertionConcern.ArgumentIsNotNull(customer, "Customer not found for that Apply Name Id.");
			customerId = customer.Id;
		}
		GroupedLocation groupedLocation = null;
		Employee employee = null;
		if (request.IsMobile)
		{
			employee = await _dbContext.Employees.FirstOrDefaultAsync(a => a.Email == _currentUser.Email, cancellationToken);
		}
		else
		{
			employee = await _dbContext.Employees.FirstOrDefaultAsync(a => a.Id == request.Offer.EmployeeId, cancellationToken);
		}
		var deliveryLocationId = request.Offer.DeliveryLocationId;
		var locationId = request.Offer.LocationId;
		var regionId = request.Offer.RegionId;
		if (employee != null)
		{
			if (request.IsMobile)
			{
				var commodity = await _dbContext.Commodities.FirstOrDefaultAsync(x => x.Id == request.Offer.CommodityId, cancellationToken);
				AssertionConcern.ArgumentIsSmallerOrEqualThan(request.Offer.Quantity, customer?.MobileAppLimit ?? commodity.LotFactor, "Offer Quantity is above bushel limit");
				groupedLocation = await GetMyGrowerGroupedLocation(request.Offer.DeliveryLocationId, cancellationToken);
				AssertionConcern.ArgumentIsNotNull(groupedLocation, "Grouped location for the mobile user could not be found");
				AssertionConcern.ArgumentIsTrue(groupedLocation.IsActive, "Grouped location for the mobile user is inactive");
				deliveryLocationId = groupedLocation.DestinationLocationId!.Value;
				AssertionConcern.ArgumentIsTrue(
					await IsDeliveryLocationActive(_dbContext, deliveryLocationId), 
					"Delivery location in Grouped Location is inactive");
				regionId = groupedLocation.RegionId;
				locationId = groupedLocation.ContractLocationId!.Value;
				AssertionConcern.ArgumentIsTrue(
					await IsContractLocationActive(_dbContext, locationId), 
					"Contract location in Grouped Location is inactive");
			}
		}

		// HTA offers are purely futures-based and should not undergo bidsheet validation.
		if (request.Offer.ContractTypeId != ContractTypeDictionary.HTA)
		{
			if (request.Offer.ContractTypeId == ContractTypeDictionary.FlatPrice)
			{
				var bidsheet = await _bidSheetHelper.GetInformationFromBidSheetExactValues(request.Offer.CommodityId, deliveryLocationId, request.Offer.CropYear, request.Offer.DeliveryStartDate, request.Offer.FuturesMonth,
					request.Offer.DeliveryEndDate);
				if (bidsheet == null)
				{
					bidSheetInformation = await _bidSheetHelper.GetInformationFromBidSheetApproximateValues(request.Offer.CommodityId, deliveryLocationId, request.Offer.CropYear, request.Offer.DeliveryStartDate,
						request.Offer.DeliveryEndDate);
					AssertionConcern.ArgumentIsNotNull(bidSheetInformation, ErrorCodes.BidsheetNotFound);
					AssertionConcern.ArgumentIsTrue(request.Offer.FuturesMonth == bidSheetInformation.FuturesMonth, "Futures month is not valid.");
				}
				else
				{
					bidSheetInformation = new()
					{
						FuturesMonth = bidsheet.FutureMonth,
						PostedBasis = bidsheet.Basis
					};
				}

				AssertionConcern.ArgumentIsTrue(request.Offer.PostedBasis == bidSheetInformation.PostedBasis, "Basis has changed, please re-input offer");
			}
			else
			{
				var bidsheet = await _bidSheetHelper.GetInformationFromBidSheetExactValues(request.Offer.CommodityId, deliveryLocationId, request.Offer.CropYear, request.Offer.DeliveryStartDate, null,
					request.Offer.DeliveryEndDate);
				AssertionConcern.ArgumentIsNotNull(bidsheet, ErrorCodes.BidsheetNotFound);
				bidSheetInformation = new()
				{
					FuturesMonth = bidsheet.FutureMonth,
					PostedBasis = bidsheet.Basis
				};
			}

			request.Offer.FuturesMonth = bidSheetInformation.FuturesMonth;
		}

		groupedLocation ??= await GetGroupedLocation(request, cancellationToken);

		if (groupedLocation == null)
		{
			throw new BusinessException("Region not found for location");
		}

		await _contractOfferValidationHelper.ValidateLimits(request.Offer.IsSell, deliveryLocationId, request.Offer.Quantity, groupedLocation.Id);

		if (!request.Offer.Gtc)
		{
			ContractOfferValidationHelper.ExpirationDateValidations(request.Offer.Expiration);
			var closeTimeText = "13:15:00";
			var closeTime = TimeSpan.Parse(closeTimeText);
			request.Offer.Expiration = request.Offer.Expiration.Value.Date.Add(closeTime);
		}

		regionId = regionId ?? groupedLocation.RegionId;

		var offer = new Offer(
			request.Offer.TransactionTypeId,
			request.Offer.ContractTypeId,
			request.Offer.IsSell,
			request.Offer.IsDeliveryDatesCustom,
			request.Offer.CommodityId,
			locationId,
			request.Offer.DeliveryLocationId,
			request.Offer.DeliveryStartDate,
			request.Offer.DeliveryEndDate,
			request.Offer.CropYear,
			customerId,
			request.IsMobile ? employee.Id : request.Offer.EmployeeId,
			request.Offer.FuturesMonth,
			request.Offer.FuturesPrice,
			request.Offer.PostedBasis,
			request.Offer.PushBasis,
			request.Offer.NetBasis,
			request.Offer.FreightPrice,
			request.Offer.Fees1,
			request.Offer.Fees2,
			request.Offer.Price,
			request.Offer.Quantity,
			request.Offer.Comments,
			request.Offer.Gtc,
			request.Offer.Expiration,
			request.Offer.CashSettlement,
			regionId
		);
		offer.AssignTheirContract(request.Offer.TheirContract);
		offer.OfferMetadata = await _metadataDomainService.CreateOfferFields(offer.Id, request.Offer.CustomFields);
		await _offerRepository.InsertAsync(offer, cancellationToken);
		var fillOffer = await _offerWorkflowContext.ProcessOffer(offer, bidSheetInformation);
		Contract contract2erp = null;
		if (fillOffer)
		{
			var resultFromFill = await _offerWorkflowContext.FillLocalMonitoredOffer(offer);
			contract2erp = resultFromFill.ChildContract;
			foreach (var transaction in resultFromFill.Transactions)
			{
				transaction.ProcessEvent();
			}
		}

		await _unitOfWork.CommitAsync();

		if (_ldClient.BoolVariation(FeatureFlags.EnableNotifications, request.FlagContext))
		{
			await _mediator.Send(new SetOfferGroupsCommand(offer.Id, request.Offer.Groups), cancellationToken);
		}

		if (_ldClient.BoolVariation(FeatureFlags.EnableAutoRefresh, request.FlagContext))
		{
			var offerInfo = await _mediator.Send(new GetOfferByIdQuery(offer.Id, request.FlagContext), cancellationToken);
			await _mediator.Send(new SendPushNotificationCommand<OfferInfoDto>(Constants.OfferChannel, new(ActionType.Insert, offerInfo)), cancellationToken);
			await _mediator.Send(new SendPushNotificationCommand<string>(Constants.ReviewAndReleaseChannel, new(ActionType.Insert, "")), cancellationToken);
		}

		await _logWorkflow.AddLog("CreateOfferCommand", "Check ERP5 / OfferCommand");
		if (contract2erp != null)
		{
			_logWorkflow.SetContract(contract2erp.Id);
			await _logWorkflow.AddLog("CreateOfferCommand", "Check ERP5 / OfferCommand, Not null");
			if (contract2erp.ShouldSendFillErp())
			{
				// Check if ERP should be processed asynchronously (currently for Agvantage)
				var shouldProcessAsync = await _erpIntegrationAgent.ShouldProcessErpAsynchronously();
				if (shouldProcessAsync)
				{
					await _logWorkflow.AddLog("CreateOfferCommand", "ERP, Requesting async processing via domain event");
					// Use domain event for async processing to avoid blocking the UI
					contract2erp.RequestErpProcessing(contract2erp.Event);
				}
				else
				{
					await _logWorkflow.AddLog("CreateOfferCommand", "ERP, Sending fill to ERP synchronously");
					// Process synchronously for other ERP systems
					if (string.Equals(contract2erp.Event, EContractEvent.Price.ToString(), StringComparison.Ordinal))
						await _erpIntegrationAgent.ProcessPricing(contract2erp);
					else
						await _erpIntegrationAgent.ProcessCreation(contract2erp);
				}
			}
		}
		
		await _unitOfWork.CommitAsync();

		return offer.Number;
	}

	async Task<GroupedLocation> GetMyGrowerGroupedLocation(Guid deliveryLocationId, CancellationToken cancellationToken) =>
		await _dbContext.MobileIntegrationLocationMapping
				.AsNoTracking()
				.Where(x => x.DestinationLocationId == deliveryLocationId)
				.Select(x => x.GroupedLocation)
				.FirstOrDefaultAsync(cancellationToken);

	async Task<GroupedLocation> GetGroupedLocation(CreateOfferCommand request, CancellationToken cancellationToken) =>
		await _dbContext.GroupedLocations
			.AsNoTracking()
			.Where(x => x.DestinationLocationId == request.Offer.DeliveryLocationId
					 && x.ContractLocationId == request.Offer.LocationId
					 && x.IsActive)
			.WhereIf(request.Offer.RegionId.HasValue, x => x.RegionId == request.Offer.RegionId.Value)
			.FirstOrDefaultAsync(cancellationToken);

	static async Task<bool> IsContractLocationActive(AppDbContext dbContext, Guid locationId)
	{
		var location = await dbContext.Locations.AsNoTracking().FirstOrDefaultAsync(x => x.IsLocation && x.Id == locationId);
		return location != null && location.IsActive;
	}

	static async Task<bool> IsDeliveryLocationActive(AppDbContext dbContext, Guid locationId)
	{
		var location = await dbContext.Locations.AsNoTracking().FirstOrDefaultAsync(x => x.IsDestination && x.Id == locationId);
		return location != null && location.IsActive;
	}
}
