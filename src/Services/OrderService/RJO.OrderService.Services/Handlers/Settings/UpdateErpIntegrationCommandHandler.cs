using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.Common.MediatorBehavior;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.CustomExceptions.Resources;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Domain.ERP;
using RJO.OrderService.Persistence;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Services.DTO.Settings;
using RJO.OrderService.Services.Services;

namespace RJO.OrderService.Services.Handlers.Settings;

public class UpdateErpIntegrationCommand : IRequest<Unit>, ITransactionalRequest
{
	public ErpIntegrationDto ErpIntegration { get; set; }
}

public class UpdateErpIntegrationCommandHandler : IRequestHandler<UpdateErpIntegrationCommand, Unit>
{
	readonly ILogger<UpdateErpIntegrationCommandHandler> _logger;
	readonly TenantSettingRepository _tenantSettingRepository;
	readonly AgrisSettingRepository _agrisSettingRepository;
	readonly AgtraxSettingRepository _agtraxSettingRepository;
	readonly OaklandSettingRepository _oaklandSettingRepository;
	readonly AppDbContext _dbContext;
	readonly UnitOfWork _unitOfWork;

	public UpdateErpIntegrationCommandHandler(ILogger<UpdateErpIntegrationCommandHandler> logger, AppDbContext dbContext
		, SettingRepository settingRepository, UnitOfWork unitOfWork, TenantSettingRepository tenantSettingRepository
		, AgrisSettingRepository agrisSettingRepository, AgtraxSettingRepository agtraxSettingRepository, OaklandSettingRepository oaklandSettingRepository)
	{
		_logger = logger;
		_dbContext = dbContext;
		_unitOfWork = unitOfWork;
		_tenantSettingRepository = tenantSettingRepository;
		_agrisSettingRepository = agrisSettingRepository;
		_agtraxSettingRepository = agtraxSettingRepository;
		_oaklandSettingRepository = oaklandSettingRepository;
	}

	public async Task<Unit> Handle(UpdateErpIntegrationCommand request, CancellationToken cancellationToken)
	{
		AssertionConcern.ArgumentIsNotNull(request, GeneralResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(request.ErpIntegration, GeneralResources.RequiredValueIsNotPresent);
		AssertionConcern.ArgumentIsNotNull(request.ErpIntegration.ErpType, GeneralResources.RequiredValueIsNotPresent);

		var isValidErpType = request.ErpIntegration.ErpType.IsValidEnum<ErpIntegrationTypeEnum>();

		AssertionConcern.ArgumentIsTrue(isValidErpType, "Erp type is not valid");

		var erpType = request.ErpIntegration.ErpType.TryConvertToEnum<ErpIntegrationTypeEnum>();

		var tenantSetting = await _tenantSettingRepository.CreateOrUpdateSetting(
			request.ErpIntegration.IsActive,
			request.ErpIntegration.ErpType
		);

		switch (erpType)
		{
			case ErpIntegrationTypeEnum.Agris:
				AssertionConcern.ArgumentIsNotNull(request.ErpIntegration.SystemUrl, GeneralResources.RequiredValueIsNotPresent);
				AssertionConcern.ArgumentIsNotNull(request.ErpIntegration.SystemUsername, GeneralResources.RequiredValueIsNotPresent);
				AssertionConcern.ArgumentIsNotNull(request.ErpIntegration.SystemPassword, GeneralResources.RequiredValueIsNotPresent);
				await _agrisSettingRepository.CreateOrUpdateSetting(
					request.ErpIntegration.IsActive,
					request.ErpIntegration.SystemUrl,
					string.Empty,
					string.Empty,
					request.ErpIntegration.SystemUsername,
					request.ErpIntegration.SystemPassword,
					string.Empty,
					string.Empty,
					string.Empty,
					string.Empty,
					request.ErpIntegration.CommentsLength
				);
				break;

			case ErpIntegrationTypeEnum.Agtrax:
				AssertionConcern.ArgumentIsNotNull(request.ErpIntegration.SystemUrl, GeneralResources.RequiredValueIsNotPresent);
				AssertionConcern.ArgumentIsNotNull(request.ErpIntegration.SystemUsername, GeneralResources.RequiredValueIsNotPresent);
				AssertionConcern.ArgumentIsNotNull(request.ErpIntegration.SystemPassword, GeneralResources.RequiredValueIsNotPresent);
				await _agtraxSettingRepository.CreateOrUpdateSetting(
					request.ErpIntegration.IsActive,
					request.ErpIntegration.SystemUrl,
					request.ErpIntegration.SystemUsername,
					request.ErpIntegration.SystemPassword,
					string.Empty,
					string.Empty,
					string.Empty,
					request.ErpIntegration.CommentsLength
				);
				break;

			case ErpIntegrationTypeEnum.Oakland:
				AssertionConcern.ArgumentIsNotNull(request.ErpIntegration.SystemUrl, GeneralResources.RequiredValueIsNotPresent);
				await _oaklandSettingRepository.CreateOrUpdateSetting(request.ErpIntegration.IsActive);
				break;

			case ErpIntegrationTypeEnum.Agvantage:
				var agvantageSetting = await _dbContext.Set<AgvantageSetting>().FirstOrDefaultAsync(cancellationToken);
				if (agvantageSetting == null)
					throw new NotSupportedException("Please contact Hrvyst to set up connection.");
				agvantageSetting.Update(request.ErpIntegration.IsActive);
				break;

			default:
				AssertionConcern.ArgumentIsTrue(isValidErpType, "ERP type is not valid.");
				break;
		}

		await _unitOfWork.CommitAsync();
		return Unit.Value;
	}
}
