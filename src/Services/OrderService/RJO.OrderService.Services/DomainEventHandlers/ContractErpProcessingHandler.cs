using MediatR;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.OrderService.Domain.Events;
using RJO.OrderService.Domain.Enumeration;
using RJO.OrderService.Persistence.Repositories;
using RJO.OrderService.Persistence.Repositories.Logs;
using RJO.OrderService.Services.Services.OrderProcess;

namespace RJO.OrderService.Services.DomainEventHandlers;

public sealed class ContractErpProcessingHandler(
	ContractRepository contractRepository,
	ErpIntegrationAgent erpIntegrationAgent,
	LogWorkflowRepository logWorkflow,
	ILogger<ContractErpProcessingHandler> logger)
	: INotificationHandler<ContractErpProcessingRequested>
{
	public async Task Handle(ContractErpProcessingRequested notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("ContractErpProcessingHandler started for Contract {ContractId}", notification.ContractId);

        try
        {
            // Get the contract
            var contract = await contractRepository.GetById(notification.ContractId);
            if (contract == null)
            {
                logger.LogWarning("Contract {ContractId} not found for ERP processing", notification.ContractId);
                return;
            }

            // Check if contract should still be sent to ERP
            if (!contract.ShouldSendFillErp())
            {
                logger.LogInformation("Contract {ContractId} no longer needs ERP processing", notification.ContractId);
                return;
            }

            logWorkflow.SetContract(contract.Id);
            await logWorkflow.AddLog("ContractErpProcessingHandler", "Starting background ERP processing via domain event");

            // Process the contract based on event type
            if (string.Equals(notification.ContractEvent, EContractEvent.Price.ToString(), StringComparison.Ordinal))
            {
                await logWorkflow.AddLog("ContractErpProcessingHandler", "Processing pricing in background");
                await erpIntegrationAgent.ProcessPricing(contract);
            }
            else
            {
                await logWorkflow.AddLog("ContractErpProcessingHandler", "Processing creation in background");
                await erpIntegrationAgent.ProcessCreation(contract);
            }

            await logWorkflow.AddLog("ContractErpProcessingHandler", "Background ERP processing completed successfully");
            logger.LogInformation("ContractErpProcessingHandler completed successfully for Contract {ContractId}", notification.ContractId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "ContractErpProcessingHandler failed for Contract {ContractId}", notification.ContractId);
            
            try
            {
                await logWorkflow.AddLog("ContractErpProcessingHandler", $"Background ERP processing failed: {ex.Message}");
            }
            catch
            {
                // Ignore logging errors to prevent infinite loops
            }
            
            // Don't re-throw - let the background task channel handle retries if needed
        }
    }
}
