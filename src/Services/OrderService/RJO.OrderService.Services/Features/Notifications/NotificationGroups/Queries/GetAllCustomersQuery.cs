using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.OrderService.Persistence.Database;
using RJO.OrderService.Services.Features.Notifications.NotificationGroups.Contracts.Responses;
using RJO.OrderService.Services.Features.Notifications.NotificationGroups.Mapping;

namespace RJO.OrderService.Services.Features.Notifications.NotificationGroups.Queries;

public sealed record GetAllCustomersQuery : IRequest<IReadOnlyCollection<CustomerResponse>>;

public sealed class GetAllCustomersQueryHandler : IRequestHandler<GetAllCustomersQuery, IReadOnlyCollection<CustomerResponse>>
{
	readonly AppDbContext _dbContext;

	public GetAllCustomersQueryHandler(AppDbContext dbContext) => _dbContext = dbContext;

	public async Task<IReadOnlyCollection<CustomerResponse>> <PERSON>le(GetAllCustomersQuery request, CancellationToken cancellationToken) 
		=> await _dbContext.Customers
			.AsNoTracking()
			.Select(x => x.MapToResponse())
			.ToListAsync(cancellationToken);
}
