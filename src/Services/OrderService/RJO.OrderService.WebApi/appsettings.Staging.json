{"Sentry": {"Environment": "Staging"}, "Azure": {"AppConfiguration": {"Endpoint": "https://st-eastus-appconfiguration.azconfig.io"}, "KeyVault": {"Endpoint": "https://steastushrvystkv02.vault.azure.net/"}, "CommunicationServices": {"Endpoint": "https://st-communicationservices.communication.azure.com/", "FromEmail": "<EMAIL>", "FromPhoneNumber": "+18447313947"}}, "CQG": {"WebPath": "https://st-eastus-cqgmd-app.azurewebsites.net"}, "RJO": {"WebPath": "https://uattrade.rjobrien.com"}, "HangfireAuth": {"ClientId": "95fe032e-027d-48eb-9608-a5b0abc9f5a0", "TenantId": "fa6d831d-dc2b-41a9-b180-55610cc31345", "ObjectId": "1abbec1f-09b7-41bb-b776-358405a54ce9", "Instance": "https://login.microsoftonline.com/", "Domain": "https://st-eastus-hrvystapi-app.azurewebsites.net", "CallbackPath": "/signin-oidc"}, "Cors": {"Origins": ["https://st-eastus-hrvyst-app.azurewebsites.net", "https://hedgestg.hrvyst.com"], "Headers": ["Content-Type", "Authorization", "X-Requested-With"], "Methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowCredentials": true}}