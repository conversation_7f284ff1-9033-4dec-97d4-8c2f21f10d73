{"Sentry": {"Environment": "Local"}, "ClockSynchronization": {"Disabled": true}, "Azure": {"AppConfiguration": {"Endpoint": "https://dv-eastus-appconfiguration.azconfig.io"}, "KeyVault": {"Endpoint": "https://dv-eastus-rjohrvyst-kv.vault.azure.net/"}, "CommunicationServices": {"Endpoint": "https://dv-communicationservices.communication.azure.com/", "FromEmail": "<EMAIL>", "FromPhoneNumber": "+18335588428"}}, "CQG": {"WebPath": "https://dv-eastus-cqgmd-app.azurewebsites.net"}, "RJO": {"WebPath": "https://uattrade.rjobrien.com"}, "HangfireAuth": {"ClientId": "2a3f721c-205f-44fc-854d-6441e5abda61", "TenantId": "fa6d831d-dc2b-41a9-b180-55610cc31345", "ObjectId": "ef789643-6105-46f9-b9f0-c040107f2797", "Instance": "https://login.microsoftonline.com/", "Domain": "https://dv-rjohrvystdemoapi-app.azurewebsites.net", "CallbackPath": "/signin-oidc"}, "Cors": {"Origins": ["http://localhost:3000"], "Headers": ["Content-Type", "Authorization", "X-Requested-With"], "Methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowCredentials": true}}