{"Sentry": {"Environment": "QA"}, "Azure": {"AppConfiguration": {"Endpoint": "https://qas-eastus-appconfiguration.azconfig.io"}, "KeyVault": {"Endpoint": "https://qas-eastus-rjohrvyst-kv.vault.azure.net/"}, "CommunicationServices": {"Endpoint": "https://qas-communicationservices.communication.azure.com/", "FromEmail": "<EMAIL>", "FromPhoneNumber": "+18336752637"}}, "CQG": {"WebPath": "https://qas-eastus-cqgmd-app.azurewebsites.net"}, "RJO": {"WebPath": "https://uattrade.rjobrien.com"}, "HangfireAuth": {"ClientId": "16371a06-4e39-414c-8b50-9f5871d17879", "TenantId": "fa6d831d-dc2b-41a9-b180-55610cc31345", "ObjectId": "dccc85f1-ae21-4a39-8c37-c857eb606670", "Instance": "https://login.microsoftonline.com/", "Domain": "https://qas-eastus-hrvystapi-app.azurewebsites.net", "CallbackPath": "/signin-oidc"}, "Cors": {"Origins": ["https://qas-eastus-hrvyst-app.azurewebsites.net"], "Headers": ["Content-Type", "Authorization", "X-Requested-With"], "Methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowCredentials": true}}