{"Sentry": {"Environment": "Production"}, "Azure": {"AppConfiguration": {"Endpoint": "https://pr-eastus-appconfiguration.azconfig.io"}, "KeyVault": {"Endpoint": "https://preastushrvystkv.vault.azure.net/"}, "CommunicationServices": {"Endpoint": "https://pr-communicationservices.communication.azure.com/", "FromEmail": "<EMAIL>", "FromPhoneNumber": "+18442301333"}}, "CQG": {"WebPath": "https://pr-eastus-cqgmd-app.azurewebsites.net"}, "RJO": {"WebPath": "https://trade.rjobrien.com"}, "HangfireAuth": {"ClientId": "e01910cb-89ce-4a04-87b9-e2b4861281de", "TenantId": "fa6d831d-dc2b-41a9-b180-55610cc31345", "ObjectId": "5225b640-b4a8-44da-a69b-dabb20ed4b15", "Instance": "https://login.microsoftonline.com/", "Domain": "https://pr-eastus-hrvystapi-app.azurewebsites.net", "CallbackPath": "/signin-oidc"}, "Cors": {"Origins": ["https://pr-eastus-hrvyst-app.azurewebsites.net", "https://hedge.hrvyst.com"], "Headers": ["Content-Type", "Authorization", "X-Requested-With"], "Methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowCredentials": true}}