using MediatR;
using Microsoft.AspNetCore.Mvc;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.WebCommon;
using RJO.BuildingBlocks.WebCommon.Authorization;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.OrderService.Services.DTO.Catalogs.Location;
using RJO.OrderService.Services.DTO.Common;
using RJO.OrderService.Services.Handlers.Catalogs.Location;

namespace RJO.OrderService.WebApi.Controllers;

[Produces("application/json")]
[Route("api/locations")]
[ApiController]
public class LocationController : BaseController
{
	readonly IMediator _mediator;

	public LocationController(IMediator mediator) => _mediator = mediator;

	/// <summary> 
	///     Action to retrieve all Location.
	/// </summary>
	/// <returns>Returns a list of Location elements or an empty list</returns>
	/// <response code="200">Returned if the list was retrieved</response>
	/// <response code="400">Returned if the elements could not be retrieved</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpGet]
	public async Task<ActionResult<Result<LocationItemResultToSelectDto>>> GetAll([FromQuery] LocationFilterToSelectDto filter) => Result(await _mediator.Send(new GetAllLocationToSelectQuery { Filter = filter, FlagContext = User.GetFlagContext() }));

	/// <summary> 
	///     Action to retrieve all Location.
	/// </summary>
	/// <returns>Returns a list of Location elements or an empty list</returns>
	/// <response code="200">Returned if the list was retrieved</response>
	/// <response code="400">Returned if the elements could not be retrieved</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpGet("setting")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<LocationItemResultToSelectDto>>> GetSettingsAll() => Result(await _mediator.Send(new GetAllLocationToSettingsQuery { FlagContext = User.GetFlagContext() }));

	/// <summary> 
	///     Action to retrieve Locations filtered by certain criteria.
	/// </summary>
	/// <returns>Returns a list of all Locations or an empty list</returns>
	/// <response code="200">Returned if the list of Locations was retrieved</response>
	/// <response code="400">Returned if the Locations could not be retrieved</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPost]
	[Route("filter")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<ListDto<LocationItemDto>>>> Filters([FromBody] LocationFilterDto filter) => Result(await _mediator.Send(new GetAllLocationQuery(filter, User.GetFlagContext())));

	/// <summary> 
	///     Action to retrieve Locations filtered by certain criteria.
	/// </summary>
	/// <returns>Returns a list of all Locations or an empty list</returns>
	/// <response code="200">Returned if the list of Locations was retrieved</response>
	/// <response code="400">Returned if the Locations could not be retrieved</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPost]
	[Route("legacy/filter")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<ListDto<LegacyLocationItemDto>>>> LegacyFilters([FromBody] LocationFilterDto filter) => Result(await _mediator.Send(new GetAllLegacyLocationQuery { Filter = filter }));

	// GET api/<LocationController>/5
	/// <summary>
	///     Action to retrieve a Grouped location pair in detail with related entities.
	/// </summary>
	/// <param name="id"></param>
	/// <returns>Returns a grouped location pair</returns>
	/// <response code="200">Returned if the location exists</response>
	/// <response code="404">Returned if the location does not exists</response>
	[HttpGet("{id}")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<GroupedLocationIdsDto>>> Get(Guid id) => Result(await _mediator.Send(new GetGroupedLocationByIdQuery { Id = id }));

	// GET api/<LocationController>/legacy/5
	/// <summary>
	///     Action to retrieve a Grouped location pair in detail with related entities.
	/// </summary>
	/// <param name="id"></param>
	/// <returns>Returns a grouped location pair</returns>
	/// <response code="200">Returned if the location exists</response>
	/// <response code="404">Returned if the location does not exists</response>
	[HttpGet("legacy/{id}")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<LocationDto>>> GetLegacy(Guid id) => Result(await _mediator.Send(new GetLocationByIdQuery { Id = id }));

	[HttpGet("searchWithNameOrNumber")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	[ProducesResponseType(typeof(Result<IReadOnlyCollection<LocationSearchDto>>), StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	public async Task<ActionResult<Result<IReadOnlyCollection<LocationSearchDto>>>> GetLocationWithNameOrNumber(string name = "", string number = "", bool isDestination = true) => Result(await _mediator.Send(new GetLocationWithNameOrNumberQuery { Name = name, Number = number, IsDestination = isDestination }));

	[HttpGet("groupedLocation")]
	[ProducesResponseType(typeof(Result<IReadOnlyCollection<GroupedLocationWithMergedDataDto>>), StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	public async Task<ActionResult<Result<IReadOnlyCollection<GroupedLocationWithMergedDataDto>>>> GetAllGroupedLocations() => Result(await _mediator.Send(new GetAllGroupedLocationsQuery(User.GetFlagContext())));

	/// <summary>
	///     Action to create a new Location in the database.
	/// </summary>
	/// <remarks>
	/// Sample request:
	/// 
	///     POST 
	/// {
	///		DestinationLocationNumber : "E24F"
	///		DestinationLocationName : "Chicago"
	///		ContractLocationNumber : "CF25"
	///		ContractLocationName : "LakeFalls
	/// }
	/// 
	/// </remarks>
	/// <param name="location">Model to create a new Location</param>
	/// <returns>Returns the created Location</returns>
	/// <response code="201">Returned if the Location was created</response>
	/// <response code="400">Returned if the model couldn't be parsed or saved</response>
	/// <response code="422">Returned when the validation failed</response>
	[ProducesResponseType(StatusCodes.Status201Created)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[ProducesResponseType(StatusCodes.Status422UnprocessableEntity)]
	[HttpPost("legacy")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Guid>>> CreateLocation([FromBody] LocationNewDto location) => Result(await _mediator.Send(new CreateLocationCommand { Location = location }));

	/// <summary>
	///     Action to create a new Location in the database.
	/// </summary>
	/// <remarks>
	/// Sample request:
	/// 
	///     POST 
	/// {
	///		DestinationLocationNumber : "E24F"
	///		DestinationLocationName : "Chicago"
	///		ContractLocationNumber : "CF25"
	///		ContractLocationName : "LakeFalls
	/// }
	/// 
	/// </remarks>
	/// <param name="groupedLocation">Model to create a new Location</param>
	/// <returns>Returns the created Location</returns>
	/// <response code="201">Returned if the Location was created</response>
	/// <response code="400">Returned if the model couldn't be parsed or saved</response>
	/// <response code="422">Returned when the validation failed</response>
	[ProducesResponseType(StatusCodes.Status201Created)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[ProducesResponseType(StatusCodes.Status422UnprocessableEntity)]
	[HttpPost]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Guid>>> CreateGroupedLocation([FromBody] GroupedLocationNewDto groupedLocation) => Result(await _mediator.Send(new CreateGroupedLocationCommand(groupedLocation, User.GetFlagContext())));

	/// <param name="id">The guid of the Location to be updated</param>
	/// <param name="groupedLocation">Information of the Location to be updated</param>
	/// <returns>Returns the updated contract</returns>
	/// <response code="200">Returned if the Location was updated</response>
	/// <response code="400">Returned if the Location could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Guid>>> UpdateGroupedLocation(Guid id, [FromBody] GroupedLocationDto groupedLocation) => Result(await _mediator.Send(new UpdateGroupedLocationCommand
	{
		LocationId = id,
		GroupedLocation = groupedLocation,
		FlagContext = User.GetFlagContext()
	}));

	/// <param name="id">The guid of the Location to be updated manually</param>
	/// <param name="groupedLocation">Information of the Location to be updated</param>
	/// <returns>Returns the updated contract</returns>
	/// <response code="200">Returned if the Location was updated</response>
	/// <response code="400">Returned if the Location could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("manual")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Guid>>> UpdateNameOrNumberOfEachLocationOfGroupedLocation(Guid id, [FromBody] GroupedLocationDataDto groupedLocation) => Result(await _mediator.Send(new AdjustGroupedLocationDetailsCommand(Id: id, GroupedLocation: groupedLocation, FlagContext: User.GetFlagContext())));

	/// <param name="id">The guid of the Location to be updated</param>
	/// <param name="location">Information of the Location to be updated</param>
	/// <returns>Returns the updated contract</returns>
	/// <response code="200">Returned if the Location was updated</response>
	/// <response code="400">Returned if the Location could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("legacy")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Guid>>> UpdateLocation(Guid id, [FromBody] LocationDto location) => Result(await _mediator.Send(new UpdateLocationCommand
	{
		LocationId = id,
		Location = location
	}));

	/// <summary>
	///     Action to Activate a Location.
	/// </summary>
	/// <param name="id">The guid of the Location to be updated</param>
	/// <returns>Returns the updated contract</returns>
	/// <response code="200">Returned if the Location was updated</response>
	/// <response code="400">Returned if the Location could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("Activate")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Guid>>> ActivateGroupedLocation([FromQuery] Guid id) => Result(await _mediator.Send(new ActivateGroupedLocationCommand { GroupedLocationId = id }));

	/// <summary>
	///     Action to Deactivate a Location.
	/// </summary>
	/// <param name="id">The guid of the Location to be updated</param>
	/// <returns>Returns the updated contract</returns>
	/// <response code="200">Returned if the Location was updated</response>
	/// <response code="400">Returned if the Location could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("Deactivate")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Guid>>> DeactivateGroupedLocation(Guid id) => Result(await _mediator.Send(new DeactivateGroupedLocationCommand { GroupedLocationId = id }));

	/// <summary>
	///     Action to Activate a Location.
	/// </summary>
	/// <param name="id">The guid of the Location to be updated</param>
	/// <returns>Returns the updated contract</returns>
	/// <response code="200">Returned if the Location was updated</response>
	/// <response code="400">Returned if the Location could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("Legacy/Activate")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Guid>>> ActivateLocation([FromQuery] Guid id) => Result(await _mediator.Send(new ActivateLocationCommand { LocationId = id }));

	/// <summary>
	///     Action to Deactivate a Location.
	/// </summary>
	/// <param name="id">The guid of the Location to be updated</param>
	/// <returns>Returns the updated contract</returns>
	/// <response code="200">Returned if the Location was updated</response>
	/// <response code="400">Returned if the Location could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("Legacy/Deactivate")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Guid>>> DeactivateLocation(Guid id) => Result(await _mediator.Send(new DeactivateLocationCommand { LocationId = id }));

	/// <summary>
	///     Action to set up locations. (Doubt)
	/// </summary>
	/// <param name="locationRole">Information of the Location Role to be updated</param>
	/// <returns>Returns response information updated</returns>
	/// <response code="200">Returned if the Location Role  was updated</response>
	/// <response code="400">Returned if the Location Role could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("Setting")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Unit>>> Setting(LocationRoleDto locationRole) => Result(await _mediator.Send(new SettingLocationRoleCommand { LocationRole = locationRole }));

	/// <summary>
	///     Action to get the setting locations.
	/// </summary>
	/// <param name="roleId">The guid of the role to be requested</param>
	/// <returns>Returns response information updated</returns>
	/// <response code="200">Returned if the Location Role  was updated</response>
	/// <response code="400">Returned if the Location Role could not be found with the provided role id</response>
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	[HttpGet("SettingByRole/{roleId}")]
	public async Task<ActionResult<Result<ListDto<LocationSettingItemDto>>>> GetByRole(Guid roleId) => Result(await _mediator.Send(new GetLocationSettingByRoleQuery { RoleId = roleId }));

	/// <summary>
	///     Action to get the setting locations (legacy)
	/// </summary>
	/// <param name="employeeId">The guid of the employee to be requested</param>
	/// <returns>Returns response information updated</returns>
	/// <response code="200">Returned if the Location Employee exist</response>
	/// <response code="400">Returned if the Location Employee could not be found with the provided employee id</response>
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	[HttpGet("legacy/SettingByEmployee/{employeeId}")]
	public async Task<ActionResult<Result<ListDto<LocationSettingItemDto>>>> GetByEmployee(Guid employeeId) => Result(await _mediator.Send(new GetLocationSettingByEmployeeQuery { EmployeeId = employeeId }));

	/// <summary>
	/// Action to get the setting locations.
	/// </summary>
	/// <param name="employeeId">The guid of the employee to be requested</param>
	/// <param name="regionId">The optional guid of the region from query parameters</param>
	/// <returns>Returns response information updated</returns>
	/// <response code="200">Returned if the Location Employee exists</response>
	/// <response code="400">Returned if the Location Employee could not be found with the provided employee id</response>
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	[HttpGet("SettingByEmployee/{employeeId}")]
	public async Task<ActionResult<Result<ListDto<LocationSettingItemDto>>>> GetGroupedLocationByEmployee(Guid employeeId, [FromQuery] Guid? regionId = null) => Result(await _mediator.Send(new GetGroupedLocationSettingByEmployeeQuery { EmployeeId = employeeId, RegionId = regionId }));

	/// <summary>
	///     Action to set up locations (legacy)
	/// </summary>
	/// <param name="locationEmployee">Information of the Location Role to be updated</param>
	/// <returns>Returns response information updated</returns>
	/// <response code="200">Returned if the Location Role  was updated</response>
	/// <response code="400">Returned if the Location Role could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("legacy/SettingLocationEmployee")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Unit>>> SettingLocationEmployee(LocationEmployeeDto locationEmployee) => Result(await _mediator.Send(new SettingLocationEmployeeCommand { LocationEmployee = locationEmployee }));

	/// <summary>
	///     Action to set up locations.
	/// </summary>
	/// <param name="locationEmployee">Information of the Location Role to be updated</param>
	/// <returns>Returns response information updated</returns>
	/// <response code="200">Returned if the Location Role  was updated</response>
	/// <response code="400">Returned if the Location Role could not be found with the provided id</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPut("SettingLocationEmployee")]
	[ClaimAuthorize(OperationsConstants.SettingsAdmin)]
	public async Task<ActionResult<Result<Unit>>> SettingGroupedLocationEmployee(LocationEmployeeDto locationEmployee) => Result(await _mediator.Send(new SettingGroupedLocationEmployeeCommand { LocationEmployee = locationEmployee }));
}
