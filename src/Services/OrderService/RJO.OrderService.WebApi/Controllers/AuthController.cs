using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RJO.OrderService.Services.Features.Shared;

namespace RJO.OrderService.WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
	/// <summary>
	/// Get current user information
	/// </summary>
	/// <returns>Current user information</returns>
	[HttpGet("me")]
	[Authorize]
	public ActionResult<Result<object>> GetCurrentUser()
	{
		if (!User.Identity?.IsAuthenticated ?? true)
		{
			return Unauthorized();
		}

		var userInfo = new
		{
			Id = User.FindFirst("sub")?.Value,
			Name = User.FindFirst("name")?.Value,
			Email = User.FindFirst("email")?.Value,
			Tenant = User.FindFirst("http://schemas.microsoft.com/identity/claims/tenantid")?.Value,
			TenantDisplayName = User.FindFirst("http://schemas.microsoft.com/identity/claims/tenantDisplayName")?.Value,
			Permissions = User.FindAll("Grant").Select(c => c.Value).ToArray(),
			IsAuthenticated = true
		};

		return Result(userInfo);
	}

	/// <summary>
	/// Clear authentication cookie (for proxy logout)
	/// </summary>
	/// <returns>Success result</returns>
	[HttpPost("logout")]
	[AllowAnonymous]
	public ActionResult<Result<bool>> Logout()
	{
		// Clear the authentication cookie
		var cookieOptions = new CookieOptions
		{
			HttpOnly = true,
			Secure = Request.IsHttps,
			SameSite = SameSiteMode.Lax,
			Expires = DateTimeOffset.UtcNow.AddDays(-1), // Set expiry in the past
			Path = "/",
			Domain = GetCookieDomain()
		};

		Response.Cookies.Append("hrvyst-jwt", "", cookieOptions);
		return Result(true);
	}

	/// <summary>
	/// Gets the appropriate cookie domain for cross-subdomain sharing
	/// </summary>
	/// <returns>The domain to use for the cookie, or null for same-host only</returns>
	private string GetCookieDomain()
	{
		var host = Request.Host.Host;

		// For production/staging: Enable sharing across *.hrvyst.com subdomains
		if (host.EndsWith("hrvyst.com", StringComparison.OrdinalIgnoreCase))
		{
			return ".hrvyst.com";
		}

		// For localhost: Different ports on same host work automatically
		if (host.StartsWith("localhost", StringComparison.OrdinalIgnoreCase))
		{
			return null; // No domain needed - same host, different ports
		}

		// For other environments, don't set domain (same-host only)
		return null;
	}
}
