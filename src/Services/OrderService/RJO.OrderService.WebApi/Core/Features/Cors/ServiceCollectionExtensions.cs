using Microsoft.AspNetCore.Cors.Infrastructure;

namespace RJO.OrderService.WebApi.Core.Features.Cors;

#nullable enable

public static class Extensions
{
	public static IServiceCollection AddCorsPolicy(this IServiceCollection services, IConfigurationSection configurationSection)
	{
		ArgumentNullException.ThrowIfNull(services);
		ArgumentNullException.ThrowIfNull(configurationSection);

		return services.AddCorsPolicy(configurationSection.Bind);
	}
    
	public static IServiceCollection AddCorsPolicy(this IServiceCollection services, Action<CorsOptions> configureCorsOptions)
	{
		ArgumentNullException.ThrowIfNull(services);
		ArgumentNullException.ThrowIfNull(configureCorsOptions);

		var corsOptions = new CorsOptions();
		configureCorsOptions(corsOptions);
        
		return services.AddCorsPolicy(corsOptions);
	}

	static IServiceCollection AddCorsPolicy(this IServiceCollection services, CorsOptions corsOptions)
	{
		var corsPolicyBuilder = new CorsPolicyBuilder()
			.WithOrigins(corsOptions.Origins.ToArray())
			.WithHeaders(corsOptions.Headers.ToArray())
			.WithMethods(corsOptions.Methods.ToArray());
        
		if (corsOptions.MaxAge != null)
		{
			corsPolicyBuilder.SetPreflightMaxAge(TimeSpan.FromSeconds(corsOptions.MaxAge.Value));
		}

		if (corsOptions.AllowCredentials)
		{
			corsPolicyBuilder.AllowCredentials();
		}

		services.AddCors(options =>
		{
			options.AddPolicy(CorsConstants.DefaultCorsPolicy, corsPolicyBuilder.Build());
		});

		return services;
	}
	
	public static IApplicationBuilder UseCorsPolicy(this IApplicationBuilder app)
	{
		ArgumentNullException.ThrowIfNull(app);

		return app.UseCors(CorsConstants.DefaultCorsPolicy);
	}
}
