using Microsoft.Extensions.DependencyInjection;
using RJO.MultiTenancyServer.Core;
using RJO.MultiTenancyServer.Core.Stores;

namespace RJO.OrderService.Domain.MultiTenancyServer;

public static class ServiceProviderExtensions
{
	public static async Task WithTenantAsync<T>(this IServiceProvider provider, string canonicalName, Func<T, Task> action) =>
		await provider.WithTenantAsync(canonicalName, async scopedProvider =>
		{
			var service = scopedProvider.GetRequiredService<T>();
			await action(service);
		});

	public static async Task WithTenantAsync(this IServiceProvider provider, string canonicalName, Func<IServiceProvider, Task> action)
	{
		using var scope = provider.CreateScope();
		await scope.ServiceProvider.SetTenant(canonicalName);
		await action(scope.ServiceProvider);
	}

	public static void WithTenant<T>(this IServiceProvider provider, string canonicalName, Action<T> action) =>
		provider.WithTenant(canonicalName, scopedProvider =>
		{
			var service = scopedProvider.GetRequiredService<T>();
			action(service);
		});

	public static async void WithTenant(this IServiceProvider provider, string canonicalName, Action<IServiceProvider> action)
	{
		using var scope = provider.CreateScope();
		await scope.ServiceProvider.SetTenant(canonicalName);
		action(scope.ServiceProvider);
	}

	public static async Task WithoutTenantAsync(this IServiceProvider provider, Func<IServiceProvider, Task> action)
	{
		using var scope = provider.CreateScope();
		await action(scope.ServiceProvider);
	}

	public static async Task SetTenant(this IServiceProvider provider, string canonicalName)
	{
		var tenantStore = provider.GetRequiredService<ITenantStore<ApplicationTenant>>();
		var tenancyContext = provider.GetRequiredService<ITenancyContext<ApplicationTenant>>();

		var tenant = await tenantStore.FindByCanonicalNameAsync(canonicalName);
		tenancyContext.Tenant = tenant;
	}
}
