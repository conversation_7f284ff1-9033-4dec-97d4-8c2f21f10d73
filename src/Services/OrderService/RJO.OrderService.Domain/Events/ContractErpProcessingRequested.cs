using RJO.OrderService.Domain.Contracts;
using RJO.OrderService.Domain.MultiTenancyServer;

namespace RJO.OrderService.Domain.Events;

public sealed class ContractErpProcessingRequested(Guid contractId, string contractEvent) : IEventPos, IBackgroundTask
{
	public Guid ContractId { get; } = contractId;
	public string ContractEvent { get; } = contractEvent;

	public ApplicationTenant ApplicationTenant { get; set; }
}
