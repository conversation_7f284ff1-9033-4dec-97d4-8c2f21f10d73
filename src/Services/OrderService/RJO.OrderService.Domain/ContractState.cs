using RJO.BuildingBlocks.CustomExceptions.BusinessExceptions;
using RJO.OrderService.Domain.Enumeration;

namespace RJO.OrderService.Domain;

public class ContractState : ValueObject<ContractState>
{
	public EContractState Value { get; private set; }
	ContractState() { }
	public ContractState(EContractState status) => Value = status;

	protected override bool EqualsCore(ContractState other) => other != null && other.Value == Value;
	protected override int GetHashCodeCore() => (int)Value;

	public override string ToString() => Enum.GetName(typeof(EContractState), Value);

	public ContractState Cancel()
	{
		if (Value == EContractState.Canceled)
		{
			throw new OperationNotPermittedException("This contract must have the correct status to do this action.");
		}

		return new(EContractState.Canceled);
	}

	public ContractState ChangeStatusToPriced() => new(EContractState.Priced);

	public ContractState ChangeStatusToOpen() => new(EContractState.Open);

	public ContractState ChangeStatusToConverted() => new(EContractState.Convert);

	public bool Is(EContractState status) => Value == status;
}
