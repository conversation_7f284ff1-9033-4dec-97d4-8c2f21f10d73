namespace RJO.OrderService.Domain.Tests.Helpers;

public class ContractTypeBuilder
{
	public static readonly string FlatPrice = "Flat Price";
	public static readonly string Basis = "Basis";
	public static readonly string Hta = "HTA";

	static readonly Dictionary<string, ContractType> ContractTypes = new()
	{
		{ FlatPrice, new ContractType(FlatPrice, FlatPrice) },
		{ Basis, new ContractType(Basis, Basis) },
		{ Hta, new ContractType(Hta, Hta) }
	};

	public ContractType WithHta()
	{
		ContractTypes.TryGetValue(Hta, out var contractType);
		return contractType;
	}

	public ContractType WithFlatPrice()
	{
		ContractTypes.TryGetValue(FlatPrice, out var contractType);
		return contractType;
	}

	public ContractType WithBasis()
	{
		ContractTypes.TryGetValue(Basis, out var contractType);
		return contractType;
	}

	public IEnumerable<ContractType> GetAllContractTypes() => ContractTypes.Values.ToList();

	public Dictionary<string, ContractType> GetAllContractTypesDictionary() => ContractTypes;
}
