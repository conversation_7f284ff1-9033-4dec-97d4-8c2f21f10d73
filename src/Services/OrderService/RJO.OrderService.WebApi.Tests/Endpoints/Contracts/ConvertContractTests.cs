using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common.Http;
using RJO.BuildingBlocks.Testing;
using RJO.OrderService.Domain;
using RJO.OrderService.Services.DTO.Contract;
using RJO.OrderService.WebApi.Testing;
using TestStack.BDDfy;
using TestStack.BDDfy.Xunit;
using Xunit;
using Xunit.Abstractions;
using FluentAssertions.Extensions;
using RJO.OrderService.Domain.Settings;

namespace RJO.OrderService.WebApi.Tests.Endpoints.Contracts;

public class ConvertContractTests : EndpointOutboundHttpTests
{
	Contract _originalContract;
	TenantData _tenantData;
	Commodity _commodity;
	ExtendedContractType _extendedContractType;

	public ConvertContractTests(OrderService fixture, ITestOutputHelper outputHelper) : base(fixture, outputHelper) { }

	// NTC to another NTC
	[BddfyTheory]
	[InlineData(TenantNames.PoinsettRice, CommodityNames.Corn)]
	void Scenario1(string tenant, string commodity)
	{
		_tenantData = TestData.For(tenant);
		_commodity = _tenantData.Commodities.For(commodity);
		this
			.Given(x => x.InitData(_tenantData, "Contracts_ConvertContractTests_Scenario1"))
			.And(x => x.AContractHasBeenCreatedWithQuantity(3000))
			.And(x => x.ValidInputForQuantity(200))
			.When(x => x.SendingRequest())
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.AChildContractIsCreatedForQuantityAndGrossRemainingBalance(200))
			.And(x => x.TheParentContractIsUpdatedToHaveGrossRemainingBalanceAndLastTransactionQuantity(2800, 3000))
			.And(x => x.ClearCommodity(_commodity, _tenantData))
			.BDDfy();
	}

	// NTC to HTA via EFP
	[BddfyTheory]
	[InlineData(TenantNames.PoinsettRice, CommodityNames.Corn)]
	void Scenario2(string tenant, string commodity)
	{
		_tenantData = TestData.For(tenant);
		_commodity = _tenantData.Commodities.For(commodity);
		this
			.Given(x => PreCleanup())
			.And(x => x.AContractHasBeenCreatedWithQuantity(8000))
			.And(x => x.BucketBalanceWasAlreadyCreated(2500m))
			.And(x => x.ValidInputForViaEFPConvertToHTA(6000, 5000))
			.When(x => x.SendingRequest())
			.Then(x => x.TheResponseStatusIsSuccess())
			.And(x => x.AllRecordsAreCreated(6000, 5000, 3500m))
			.And(x => x.TheParentContractIsUpdatedToHaveGrossRemainingBalanceAndLastTransactionQuantity(2000, 8000))
			.BDDfy();
	}
	async Task InitData(TenantData tenantData, string commodityName) =>
		await Fixture.WithDbContextFor(tenantData, async dbContext =>
		{
			_commodity = await dbContext.CreateCommodity(tenantData, commodityName);

			await dbContext.CreateBidsheets(_commodity);
			await dbContext.CreateBrokerMappings(_tenantData, _commodity);
			await dbContext.AddErpPositionType(_tenantData, _commodity);
			_extendedContractType = await dbContext.ExtendedContractTypes.FirstOrDefaultAsync(x => x.Name == "Ntc2");
		});

	async Task ClearCommodity(Commodity commodity, TenantData tenantData) =>
		await Fixture.WithDbContextFor(tenantData, async dbContext => await dbContext.ClearCommodity(commodity));

	Task PreCleanup() =>
		Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			dbContext.BucketBalanceContract.RemoveRange(dbContext.BucketBalanceContract);
			await dbContext.SaveChangesAsync();

			dbContext.BucketBalance.RemoveRange(dbContext.BucketBalance);
			await dbContext.SaveChangesAsync();

			dbContext.OrderFills.RemoveRange(dbContext.OrderFills);
			await dbContext.SaveChangesAsync();

			dbContext.MarketTransactions.RemoveRange(dbContext.MarketTransactions);
			await dbContext.SaveChangesAsync();

			dbContext.Contracts.RemoveRange(dbContext.Contracts);
			await dbContext.SaveChangesAsync();
		});

	async Task AContractHasBeenCreatedWithQuantity(int quantity) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			_originalContract = Create.NtcContract(_tenantData, _commodity, _tenantData.Locations.Deerfield, _tenantData.Locations.Oakbrook,
					_tenantData.Customers.RichFranz, _tenantData.Employees.Bob, 5.55m, 0.5m, quantity, false, _tenantData.Regions.Default.Id);
			_originalContract.AssignExtendedContractTypeId(_tenantData.ExtendedContractTypes.Ntc1.Id);
			_originalContract.ChangePassFill(false);
			_originalContract.ChangeErpStatus(ErpStatus.Success);
			_originalContract.AssignContractNumber("PTest001.01");
			dbContext.Set<Contract>().Add(_originalContract);
			await dbContext.SaveChangesAsync();
		});

	async Task BucketBalanceWasAlreadyCreated(decimal balance) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var bucketBalance = new BucketBalance(balance, 0, 0, 0, _commodity.Id, _tenantData.Regions.Default.Id, TestData.CurrentYear);
			dbContext.Set<BucketBalance>().Add(bucketBalance);
			await dbContext.SaveChangesAsync();
		});

	void ValidInputForQuantity(int quantity) => 
		RequestData = new ContractDto
		{
			TransactionTypeId = TestData.TransactionTypes.BushelsOnly.Id,
			ContractTypeId = TestData.ContractTypes.NTC.Id,
			ExtendedContractTypeId = _extendedContractType.Id, // changed
			CommodityId = _commodity.Id,
			CustomerId = _originalContract.CustomerId,
			LocationId = _originalContract.LocationId,
			DeliveryLocationId = _originalContract.DeliveryLocationId,
			DeliveryStartDate = _originalContract.DeliveryStartDate,
			DeliveryEndDate = _originalContract.DeliveryEndDate,
			PushBasis = _originalContract.PushBasis,
			PostedBasis = _originalContract.PostedBasis,
			FreightPrice = _originalContract.FreightPrice,
			Fees1 = _originalContract.Fees1,
			Fees2 = _originalContract.Fees2,
			Quantity = quantity,
			EmployeeId = _originalContract.EmployeeId,
			IsDeliveryDatesCustom = _originalContract.IsDeliveryDatesCustom,
			FuturesPrice = _originalContract.FuturesPrice,
			CropYear = _originalContract.CropYear,
			ExpirationDate = _originalContract.Expiration,
			FuturesMonth = _originalContract.FuturesMonth,
			Comments = "Test Convert",
		};

	void ValidInputForViaEFPConvertToHTA(int quantity, int efpQuantity) =>
		RequestData = new ContractDto
		{
			TransactionTypeId = TestData.TransactionTypes.BushelsOnly.Id,
			ContractTypeId = TestData.ContractTypes.HTA.Id,
			CommodityId = _commodity.Id,
			CustomerId = _originalContract.CustomerId,
			LocationId = _originalContract.LocationId,
			DeliveryLocationId = _originalContract.DeliveryLocationId,
			DeliveryStartDate = _originalContract.DeliveryStartDate,
			DeliveryEndDate = _originalContract.DeliveryEndDate,
			PushBasis = _originalContract.PushBasis,
			PostedBasis = _originalContract.PostedBasis,
			FreightPrice = _originalContract.FreightPrice,
			Fees1 = _originalContract.Fees1,
			Fees2 = _originalContract.Fees2,
			Quantity = quantity,
			EmployeeId = _originalContract.EmployeeId,
			IsDeliveryDatesCustom = _originalContract.IsDeliveryDatesCustom,
			FuturesPrice = _originalContract.FuturesPrice,
			CropYear = _originalContract.CropYear,
			ExpirationDate = _originalContract.Expiration,
			FuturesMonth = _originalContract.FuturesMonth,
			Comments = "Test Convert",
			EFPQuantity = efpQuantity,
		};

	async Task SendingRequest() => Response = await Client.AuthenticatedAs(_tenantData.FakeUsers.Bob).PutAsync($"/api/contracts/convert?id={_originalContract.Id}", RequestData.ToJsonContent());

	async Task AChildContractIsCreatedForQuantityAndGrossRemainingBalance(int grossRemainingBalance) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var childContract = await dbContext.Contracts.AsNoTracking().FirstOrDefaultAsync(x => x.ParentId == _originalContract.Id);
			childContract.Should().NotBeNull();
			childContract.ContractTypeId.Should().Be(TestData.ContractTypes.NTC.Id);
			childContract.ExtendedContractTypeId.Should().Be(_extendedContractType.Id);
			childContract.GrossRemainingBalance.Should().Be(grossRemainingBalance);
		});

	async Task AllRecordsAreCreated(int grossRemainingBalance, int efpQuantity, decimal newBalance) =>
		await Fixture.WithDbContextFor(_tenantData, dbContext =>
			Retry.UntilTrueOrTimeout(async () =>
			{
				var childContract = await dbContext.Contracts.AsNoTracking().FirstOrDefaultAsync(x => x.ParentId == _originalContract.Id);
				childContract.Should().NotBeNull();
				childContract.ContractTypeId.Should().Be(TestData.ContractTypes.HTA.Id);
				childContract.GrossRemainingBalance.Should().Be(grossRemainingBalance);

				var transaction = await dbContext.MarketTransactions.AsNoTracking()
					.FirstOrDefaultAsync(x => x.ContractId == childContract.Id && x.MarketId == "EFP");
				transaction.Should().NotBeNull();
				transaction.Quantity.Should().Be(efpQuantity);

				var orderFill = await dbContext.OrderFills.AsNoTracking().FirstOrDefaultAsync(x => x.MarketTransactionId == transaction.Id);
				orderFill.Should().NotBeNull();
				orderFill.TransactionId.Should().Be("EFP");
				orderFill.Amount.Should().Be(transaction.Lots);

				var bucket = await dbContext.Set<BucketBalance>()
								.AsNoTracking()
								.OrderByDescending(x => x.CreatedOn)
								.FirstOrDefaultAsync(x => x.CommodityId == _commodity.Id && x.RegionId == _tenantData.Regions.Default.Id); 
				if (bucket == null)
					return false;
				bucket.Balance().Should().Be(newBalance);
				return true;
			}, 50.Seconds()));

	async Task TheParentContractIsUpdatedToHaveGrossRemainingBalanceAndLastTransactionQuantity(int grossRemainingBalance, int lastTransactionQuantity) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var parentContract = await dbContext.Contracts.FindAsync(_originalContract.Id);
			parentContract.Should().NotBeNull();
			parentContract!.GrossRemainingBalance.Should().Be(grossRemainingBalance);
			parentContract!.LastTransactionQuantity.Should().Be(lastTransactionQuantity);
		});
}
