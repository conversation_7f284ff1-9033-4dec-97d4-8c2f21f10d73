using System.Text.Json;
using JustEat.HttpClientInterception;
using RJO.BuildingBlocks.Testing;
using RJO.OrderService.Services.ErpIntegration.AgVantageServices.Model;

namespace RJO.OrderService.WebApi.Tests;

public class AgvantageApi
{
	const string PriceUrl = "https://beast73.agvantage.com:10090/edgev81/pr/rest/graincontractprices";
	string _contractNumber;
	int _seq = 1;

	public void RespondForContractCreation(HttpClientInterceptorOptions options)
	{
		var interceptor = new Interceptor<AgvantageNewSuccessResponse>();

		options.ConfigureInterceptionFor(PostContracts(),
			x => x.WithInterceptionCallback(async request =>
			{
				var body = await request.Content!.ReadAsStringAsync();
				interceptor.Response = new()
				{
					Id = Create.ErpNumber(20)
				};
			}).WithContent(() => JsonSerializer.SerializeToUtf8Bytes(interceptor.Response)));
	}

	public void RespondForContractPricing(HttpClientInterceptorOptions options)
	{
		var interceptor = new Interceptor<AgvantagePricingSuccessResponse>();
		options.ConfigureInterceptionFor(PostContractPricing(),
			x => x.WithInterceptionCallback(async request =>
			{
				var body = await request.Content!.ReadAsStringAsync();
				var content = JsonSerializer.Deserialize<AgvantagePricingContractRequest>(body);
				interceptor.Response = new();
				interceptor.Response.Id = _contractNumber;
				interceptor.Response.Prices = new List<AgvantageResponsePrice>()
				{
					new()
					{
						Seq = Interlocked.Increment(ref _seq)
					}
				};
			}).WithContent(() => JsonSerializer.SerializeToUtf8Bytes(interceptor.Response)));
	}

	public void RespondForContractDeletePriced(HttpClientInterceptorOptions options)
	{
		var interceptor = new Interceptor<AgvantageDeletePricedContractResponse>();
		options.ConfigureInterceptionFor(DeletContractPriced(),
			x => x.WithInterceptionCallback(async request =>
			{
				var body = await request.Content!.ReadAsStringAsync();
				var content = JsonSerializer.Deserialize<AgvantageDeletePricedContractRequest>(body);
				interceptor.Response = new();
				interceptor.Response.Id = Create.ErpNumber();
				interceptor.Response.Prices = new List<AgvantageResponsePrice>();
			}).WithContent(() => JsonSerializer.SerializeToUtf8Bytes(interceptor.Response)));
	}

	static RequestMatcher PostContracts() => request =>
	{
		var uriMatches = request.RequestUri?
			.ToString()
			.StartsWith("https://beast73.agvantage.com:10090/edgev81/pr/rest/graincontracts", StringComparison.OrdinalIgnoreCase) == true;

		if (!uriMatches)
			return false;

		return request.Method == HttpMethod.Post;
	};

	RequestMatcher PostContractPricing() => request =>
	{
		var uriMatches = request.RequestUri?
			.ToString()
			.StartsWith(PriceUrl, StringComparison.OrdinalIgnoreCase) == true;

		if (!uriMatches)
			return false;

		var ret = request.Method == HttpMethod.Post;
		if (ret)
			_contractNumber = request.RequestUri.ToString().Substring(PriceUrl.Length+1);
		return ret;
	};

	RequestMatcher DeletContractPriced() => request =>
	{
		var uriMatches = request.RequestUri?
			.ToString()
			.StartsWith(PriceUrl, StringComparison.OrdinalIgnoreCase) == true;

		if (!uriMatches)
			return false;

		var ret = request.Method == HttpMethod.Delete;
		if (ret)
			_contractNumber = request.RequestUri.ToString().Substring(PriceUrl.Length+1);
		return ret;
	};
}
