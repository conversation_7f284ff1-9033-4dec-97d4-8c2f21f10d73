using Microsoft.EntityFrameworkCore.Migrations;

namespace RJO.OrderService.Persistence.Database.Migrations
{
    public partial class AddEmployeeLocationSettingField : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "CanBuy",
                schema: "Settings",
                table: "LocationEmployee",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "CanSell",
                schema: "Settings",
                table: "LocationEmployee",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<string>(
                name: "OfferNumber",
                schema: "Historical",
                table: "Contract",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CanBuy",
                schema: "Settings",
                table: "LocationEmployee");

            migrationBuilder.DropColumn(
                name: "CanSell",
                schema: "Settings",
                table: "LocationEmployee");

            migrationBuilder.AlterColumn<string>(
                name: "OfferNumber",
                schema: "Historical",
                table: "Contract",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldNullable: true);
        }
    }
}
