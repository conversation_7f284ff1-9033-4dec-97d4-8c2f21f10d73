#nullable disable

using Microsoft.EntityFrameworkCore.Migrations;

namespace RJO.OrderService.Persistence.Database.Migrations
{
    public partial class LoadOaklandCustomers : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                schema: "Settings",
                table: "TimerConfiguration",
                columns: new[] { "Id", "CreatedBy", "CreatedOn", "CronExpression", "IsActive", "Name", "UpdatedBy", "UpdatedOn" },
                values: new object[] { new Guid("dcf555e0-536d-41e1-84fe-9802697798c6"), "system", new DateTime(2021, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "1 18 ? * *", true, "LoadOaklandCustomers", null, null });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "Settings",
                table: "TimerConfiguration",
                keyColumn: "Id",
                keyValue: new Guid("dcf555e0-536d-41e1-84fe-9802697798c6"));
        }
    }
}
