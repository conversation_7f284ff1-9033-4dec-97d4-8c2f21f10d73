using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using RJO.OrderService.Domain.Audit;

namespace RJO.OrderService.Persistence.Database.Configuration.Transaction.Audit;

public class OrderFillConfiguration : IEntityTypeConfiguration<OrderFill>
{
	public void Configure(EntityTypeBuilder<OrderFill> builder)
	{
		builder.ToTable(nameof(OrderFill), SchemaName.Audit);
		
		builder.<PERSON>Key(x => x.AuditId);
		builder.Property(x => x.AuditId).ValueGeneratedOnAdd();

		builder.Property(x => x.AuditAction).IsRequired().HasMaxLength(6);
		builder.Property(x => x.AuditTime).IsRequired();
	}
}
