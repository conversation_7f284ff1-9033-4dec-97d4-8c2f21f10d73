using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RJO.BuildingBlocks.Common;
using RJO.OrderService.Domain;

namespace RJO.OrderService.Persistence.Database.Configuration.Transaction;

public class FutureMatchDetailConfiguration : IEntityTypeConfiguration<FutureMatchDetail>
{
	public void Configure(EntityTypeBuilder<FutureMatchDetail> builder)
	{
		builder.ConfigureBase();
		builder.ConfigureAuditable();
		builder.ToTable(nameof(FutureMatchDetail), SchemaName.Transaction);
		
		builder.Property(x => x.MatchId).HasMaxLength(40).IsRequired();
		builder.Property(x => x.OrderFillId).HasMaxLength(40).IsRequired();
		builder.Property(x => x.MatchedQuantity).HasColumnType(ColumnType.Decimal4).IsRequired();
		builder.Property(x => x.Number).HasMaxLength(50);
		builder.Property(x => x.ErpMessage);
		builder.Property(x => x.ErpStatus);
	}
}
