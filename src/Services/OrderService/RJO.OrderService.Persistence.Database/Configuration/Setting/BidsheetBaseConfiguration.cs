using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RJO.BuildingBlocks.Common;
using RJO.OrderService.Domain.Settings;

namespace RJO.OrderService.Persistence.Database.Configuration.Setting;

public class BidsheetBaseConfiguration<T> : IEntityTypeConfiguration<T> where T : BidsheetBase
{
	public virtual void Configure(EntityTypeBuilder<T> builder)
	{
		builder.ConfigureBase();
		builder.ConfigureAuditable();
		builder.Property(x => x.CommodityId).IsRequired();
		builder.Property(x => x.CropYear).IsRequired();
		builder.Property(x => x.DeliveryLocationId).IsRequired();
		builder.Property(x => x.FutureMonth).IsRequired().HasMaxLength(6);
		builder.Property(x => x.DeliveryStart).IsRequired();
		builder.Property(x => x.DeliveryEnd).IsRequired();
		builder.Property(x => x.Basis).HasColumnType(ColumnType.Decimal4).IsRequired();
		builder.Property(x => x.Delivery).IsRequired().HasMaxLength(6);
	}
}
