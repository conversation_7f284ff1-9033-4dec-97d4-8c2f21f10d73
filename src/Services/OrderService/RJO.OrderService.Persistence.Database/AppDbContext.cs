using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RJO.BuildingBlocks.Common;
using RJO.MultiTenancyServer.Core;
using RJO.MultiTenancyServer.Core.Options;
using RJO.MultiTenancyServer.EFCore;
using RJO.MultiTenancyServer.EFCore.Extensions;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Contracts;
using RJO.OrderService.Domain.ERP;
using RJO.OrderService.Domain.Metadata;
using RJO.OrderService.Domain.MultiTenancyServer;
using RJO.OrderService.Domain.Notifications;
using RJO.OrderService.Domain.Reports;
using RJO.OrderService.Domain.Security;
using RJO.OrderService.Domain.Settings;
using RJO.OrderService.Domain.Views;
using System.Data;
using System.Data.Common;
using System.Reflection;
using SettingsBidsheet = RJO.OrderService.Domain.Settings.Bidsheet;
using StagingBidsheet = RJO.OrderService.Domain.Staging.Bidsheet;

namespace RJO.OrderService.Persistence.Database;

public class AppDbContext : DbContext, ITenantDbContext<ApplicationTenant, Guid>
{
	static TenancyModelState<Guid> _tenancyModelState;
	readonly ILogger _logger;
	readonly string _userEmail;

	public AppDbContext(DbContextOptions<AppDbContext> options, ITenancyContext<ApplicationTenant> tenancyContext, ILogger<AppDbContext> logger, CurrentUser userEmail)
		: base(options)
	{
		TenancyContext = tenancyContext;
		_userEmail = userEmail?.Email ?? CurrentUser.InternalUser;
		_logger = logger;
	}

	public Guid TenantId => TenancyContext.Tenant.Id;

	public ITenancyContext<ApplicationTenant> TenancyContext { get; }

	public DbSet<ApplicationTenant> Tenants => Set<ApplicationTenant>();

	public DbSet<InstrumentView> InstrumentView => Set<InstrumentView>();
	public DbSet<vwNoAdminEmployees> NoAdminEmployees => Set<vwNoAdminEmployees>();
	public DbSet<TenantView> TenantView => Set<TenantView>();
	public DbSet<LocalMonitoredSymbols> LocalMonitoredSymbolsView => Set<LocalMonitoredSymbols>();
	public DbSet<GlobexSchedule> GlobexSchedules => Set<GlobexSchedule>();
	public DbSet<FutureInstrument> FutureInstruments => Set<FutureInstrument>();
	public DbSet<CQGOrderStatus> CQGOrderStatuses => Set<CQGOrderStatus>();
	public DbSet<HedgeAccount> HedgeAccounts => Set<HedgeAccount>();
	public DbSet<Product> Products => Set<Product>();
	public DbSet<MarketTransaction> MarketTransactions => Set<MarketTransaction>();
	public DbSet<Offer> Offers => Set<Offer>();
	public DbSet<SettingsBidsheet> SettingsBidsheets => Set<SettingsBidsheet>();
	public DbSet<StagingBidsheet> StagingBidsheets => Set<StagingBidsheet>();
	public DbSet<OfferMetadata> OfferMetadata => Set<OfferMetadata>();
	public DbSet<BidsheetFile> BidsheetFiles => Set<BidsheetFile>();
	public DbSet<Location> Locations => Set<Location>();
	public DbSet<LocationRole> LocationRoles => Set<LocationRole>();
	public DbSet<GroupedLocation> GroupedLocations => Set<GroupedLocation>();
	public DbSet<Commodity> Commodities => Set<Commodity>();
	public DbSet<Contract> Contracts => Set<Contract>();
	public DbSet<OrderMetadataConfiguration> OrderMetadataConfiguration => Set<OrderMetadataConfiguration>();
	public DbSet<ContractMetadata> ContractMetadata => Set<ContractMetadata>();	
	public DbSet<ContractType> ContractTypes => Set<ContractType>();
	public DbSet<Employee> Employees => Set<Employee>();
	public DbSet<LocationEmployee> LocationEmployees => Set<LocationEmployee>();
	public DbSet<GroupedLocationEmployee> GroupedLocationEmployees => Set<GroupedLocationEmployee>();
	public DbSet<Customer> Customers => Set<Customer>();
	public DbSet<ExtendedContractType> ExtendedContractTypes => Set<ExtendedContractType>();
	public DbSet<TransactionType> TransactionTypes => Set<TransactionType>();
	public DbSet<TenantSetting> TenantSettings => Set<TenantSetting>(); 
	public DbSet<PreHedgeContract> PreHedgeContracts => Set<PreHedgeContract>();
	public DbSet<OfferMonitoring> OfferMonitorings => Set<OfferMonitoring>();
	public DbSet<Region> Regions => Set<Region>();

	public DbSet<Domain.Historical.Contract> HistoricalContracts => Set<Domain.Historical.Contract>();
	public DbSet<Domain.Historical.Offer> HistoricalOffers => Set<Domain.Historical.Offer>();
	public DbSet<Domain.Historical.MarketTransaction> HistoricalMarketTransactions => Set<Domain.Historical.MarketTransaction>();

	public DbSet<Notification> Notifications => Set<Notification>();
	public DbSet<NotificationGroup> NotificationGroups => Set<NotificationGroup>();
	public DbSet<SystemNotificationGroup> SystemNotificationGroups => Set<SystemNotificationGroup>();
	public DbSet<TenantNotificationSetting> TenantNotificationSettings => Set<TenantNotificationSetting>();
	public DbSet<UserNotificationSubscription> UserNotificationSubscriptions => Set<UserNotificationSubscription>();
	public DbSet<NotificationMessage> NotificationMessages => Set<NotificationMessage>();
	public DbSet<UserDefaultValues> UserDefaultValues => Set<UserDefaultValues>();
	public DbSet<HedgeMapping> HedgeMappings => Set<HedgeMapping>();
	public DbSet<BrokerMapping> BrokerMappings => Set<BrokerMapping>();
	public DbSet<OrderFill> OrderFills => Set<OrderFill>();
	public DbSet<FutureMatchDetail> FutureMatchDetails => Set<FutureMatchDetail>();
	public DbSet<FuturesMonth> FuturesMonths => Set<FuturesMonth>();	
	public DbSet<TimerConfiguration> TimerConfigurations => Set<TimerConfiguration>();
	public DbSet<Transaction> Transactions => Set<Transaction>();
	public DbSet<BucketBalance> BucketBalance => Set<BucketBalance>();
	public DbSet<BucketBalanceContract> BucketBalanceContract => Set<BucketBalanceContract>();
	public DbSet<ErpLog> ErpLog => Set<ErpLog>();
	public DbSet<UserProfile> UserProfiles => Set<UserProfile>();
	public DbSet<SpotToGroupedLocationMap> SpotToGroupedLocationMaps => Set<SpotToGroupedLocationMap>();
	public DbSet<QuoteCounter> QuoteCounters => Set<QuoteCounter>();
	public DbSet<DoNotHedgeLog> DoNotHedgeLog => Set<DoNotHedgeLog>();
	public DbSet<RoundingRule> RoundingRules => Set<RoundingRule>();
	public DbSet<AgtraxPositionType> AgtraxPositionTypes => Set<AgtraxPositionType>();
	public DbSet<MobileIntegrationLocationMapping> MobileIntegrationLocationMapping => Set<MobileIntegrationLocationMapping>();
	public DbSet<ErpIdMapping> ErpIdMapping => Set<ErpIdMapping>();
	public DbSet<AgvantageContract> AgvantageContract => Set<AgvantageContract>();
	public DbSet<Setting> ApplicationSettings => Set<Setting>();

	protected override void OnModelCreating(ModelBuilder modelBuilder)
	{
		base.OnModelCreating(modelBuilder);

		var tenantStoreOptions = new TenantStoreOptions();
		modelBuilder.ConfigureTenantContext<ApplicationTenant, Guid>(tenantStoreOptions);

		// Add multi-tenancy support to model.
		var tenantReferenceOptions = new TenantReferenceOptions();
		modelBuilder.HasTenancy(tenantReferenceOptions, out _tenancyModelState);

		modelBuilder.Entity<ApplicationTenant>(b =>
		{
			b.Property(t => t.DisplayName).HasMaxLength(256);
		});

		modelBuilder.HasSequence<int>("ContractSequence", "Transactions")
			.StartsAt(1000)
			.IncrementsBy(1);
		modelBuilder.Entity<ApplicationTenant>().ToTable("Tenant", "Security");

		var targetEntityTypes = typeof(ITenantable).Assembly.GetTypes()
			.Where(t => typeof(ITenantable).IsAssignableFrom(t) && t is { IsClass: true, IsAbstract: false })
			.ToList();

		foreach (var entityType in targetEntityTypes)
		{
			modelBuilder.HasTenancy(entityType, () => TenancyContext.Tenant.Id, _tenancyModelState, hasIndex: false);
		}

		modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
	}

	public override int SaveChanges(bool acceptAllChangesOnSuccess)
	{
		// Ensure multi-tenancy for all tenantable entities.
		this.EnsureTenancy(TenancyContext?.Tenant?.Id, _tenancyModelState, _logger);
		return base.SaveChanges(acceptAllChangesOnSuccess);
	}

	void SaveAuthInfo()
	{
		// get entries that are being Added or Updated to add the created and updated information
		var modifiedEntries = ChangeTracker.Entries().Where(x => x.State == EntityState.Added || x.State == EntityState.Modified).ToList();
		for (var i = modifiedEntries.Count - 1; i >= 0; i--)
		{
			var entry = modifiedEntries[i];
			if (entry.Entity is not Entity entity) continue;
			if (entry.Entity is not IAuditable)
			{
				if (entry.State == EntityState.Added)
				{
					entity.CreateEntity(_userEmail);
				}

				continue;
			}

			if (entry.State == EntityState.Added)
			{
				entity.CreateEntity(_userEmail);
			}
			else if (entry.State == EntityState.Modified)
			{
				entity.UpdateEntity(_userEmail);
			}
		}
	}

	public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default)
	{
		SaveAuthInfo();
		// Ensure multi-tenancy for all tenantable entities.
		this.EnsureTenancy(TenancyContext?.Tenant?.Id, _tenancyModelState, _logger);
		return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
	}

	public int SaveChangesWithoutTenancyCheck() 
	{
		SaveAuthInfo();
		return base.SaveChanges(true);
	}

	public Task<int> SaveChangesWithoutTenancyAsync()
	{
		SaveAuthInfo();
		return base.SaveChangesAsync(true);
	}

	public override int SaveChanges()
	{
		SaveAuthInfo();
		return base.SaveChanges();
	}

	public DbConnection GetDbConnection() => Database.GetDbConnection();

	public async Task ExecuteSpAndRead(string sql, IList<IDataParameter> parameters, Action<IDataReader> fnItem)
	{
		var dbConnection = Database.GetDbConnection();
		await dbConnection.OpenAsync();

		using IDbCommand command = dbConnection.CreateCommand();
		command.CommandText = sql;
		command.CommandType = CommandType.StoredProcedure;
		if (parameters != null)
		{
			foreach (var param in parameters)
			{
				command.Parameters.Add(param);
			}
		}

		using var reader = command.ExecuteReader();
		while (reader.Read())
		{
			fnItem(reader);
		}
	}

	public async Task ExecuteQueryAndRead(string sql, IList<IDataParameter> parameters, Action<IDataReader> fnItem)
	{
		var dbConnection = Database.GetDbConnection();
		await dbConnection.OpenAsync();

		using IDbCommand command = dbConnection.CreateCommand();
		command.CommandText = sql;
		command.CommandType = CommandType.Text;
		if (parameters != null)
		{
			foreach (var param in parameters)
			{
				command.Parameters.Add(param);
			}
		}

		using var reader = command.ExecuteReader();
		while (reader.Read())
		{
			fnItem(reader);
		}
	}

	public async Task ExecuteQuery(string sql, IList<IDataParameter> parameters)
	{
		await using var dbConnection = Database.GetDbConnection();
		await dbConnection.OpenAsync();

		using IDbCommand command = dbConnection.CreateCommand();
		command.CommandText = sql;
		command.CommandType = CommandType.Text;
		if (parameters != null)
		{
			foreach (var param in parameters)
			{
				command.Parameters.Add(param);
			}
		}

		command.ExecuteNonQuery();
	}
}
