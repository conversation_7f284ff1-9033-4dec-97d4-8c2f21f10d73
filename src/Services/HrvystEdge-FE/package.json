{"name": "HrvystEdge-FE", "version": "1.0.0", "description": "", "scripts": {"build": "pnpm -r build", "dev": "powershell -File ./scripts/dev.ps1", "lint": "pnpm -r --parallel lint", "prepare": "husky && node ./scripts/patch-react-hook-form.mjs && node ./scripts/patch-global-styles.mjs", "test": "pnpm -r test"}, "packageManager": "pnpm@10.4.1", "pnpm": {"onlyBuiltDependencies": ["@sentry/cli", "@swc/core", "core-js-pure", "esbuild"]}, "devDependencies": {"husky": "^9.1.7"}}