parameters:
  environment: ''
  environmentVariableGroup: ''
  azureSubscription: ''
  webAppName: ''
  resourceGroupName: ''

jobs:
  - deployment:
    pool:
      vmImage: windows-2022
    displayName: Deploy to ${{parameters.environment}}
    environment: ${{parameters.environment}}
    variables:
      - group: ${{parameters.environmentVariableGroup}}
    strategy:
      runOnce:
        deploy:
          steps:
            - download: current
              artifact: frontend

            - powershell: |
                (Get-Content 'runtime-config.${{parameters.environment}}.js').Replace('ELASTIC_APM_SERVER_URL', $env:ELASTIC_APM_SERVER_URL) | Set-Content 'runtime-config.js'
                Remove-Item runtime-config.*.js
                Remove-Item **/*.map
              workingDirectory: '$(Pipeline.Workspace)/frontend'
              displayName: 'Set runtime-config, remove unused configs, and remove sourcemaps'

            - task: AzureRmWebAppDeployment@4
              displayName: 'Azure App Service Deploy - frontend'
              inputs:
                azureSubscription: '${{parameters.azureSubscription}}'
                webAppName: '${{parameters.webAppName}}'
                package: '$(Pipeline.Workspace)/frontend'
                enableCustomDeployment: true
                resourceGroupName: '${{parameters.resourceGroupName}}'
                removeAdditionalFilesFlag: true
