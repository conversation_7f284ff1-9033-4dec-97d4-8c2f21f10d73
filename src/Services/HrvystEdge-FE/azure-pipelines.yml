trigger:
  branches:
    include:
      - 'master'
      - 'develop'
      - 'development'
      - 'merge/**'
      - 'release/**'

variables:
  - group: rjo.global

stages:
  - stage: build

    displayName: build Artifact
    jobs:
      - job: test_react_shards
        strategy:
          parallel: 4
        pool:
          vmImage: ubuntu-latest
        steps:
          # setup pnpm
          - task: UseNode@1
            inputs:
              version: '20.x'
          - task: Npm@1
            displayName: 'Setup pnpm'
            inputs:
              command: 'custom'
              customCommand: 'install -g pnpm'
          - script: pnpm i
            displayName: 'pnpm install'
          # test shard
          - powershell: pnpm run test --coverage --shard=$($env:SYSTEM_JOBPOSITIONINPHASE + "/" + $env:SYSTEM_TOTALJOBSINPHASE) --reporter=blob --outputFile=../.vitest-reports/coverage-shard-$($env:SYSTEM_JOBPOSITIONINPHASE).json
            workingDirectory: ./packages/hrvyst-react
            displayName: 'pnpm test'
          # publish shard report
          - publish: ./packages/hrvyst-react/.vitest-reports
            artifact: coverage-shard-$(System.JobPositionInPhase)

      - job: test_merge_react_shards
        dependsOn: test_react_shards
        pool:
          vmImage: ubuntu-latest
        steps:
          # setup pnpm
          - task: UseNode@1
            inputs:
              version: '20.x'
          - task: Npm@1
            displayName: 'Setup pnpm'
            inputs:
              command: 'custom'
              customCommand: 'install -g pnpm'
          - script: pnpm i
            displayName: 'pnpm install'
          # download shard test reports
          - download: current
            patterns: '**/coverage-shard-*.json'
          # collect the shards into the folder expected by vitest
          - script: node ./scripts/collect-test-shards.mjs
            displayName: 'collect test shards'
          # merge the shards into a single report
          - script: pnpm run test --coverage --merge-reports --coverage.enabled=true --coverage.thresholds.lines=50 --coverage.thresholds.functions=50 --coverage.thresholds.branches=50 --coverage.thresholds.statements=5
            workingDirectory: ./packages/hrvyst-react
            displayName: 'merge reports'

      - job: test
        pool:
          vmImage: ubuntu-latest
        steps:
          # setup pnpm
          - task: UseNode@1
            inputs:
              version: '20.x'
          - task: Npm@1
            displayName: 'Setup pnpm'
            inputs:
              command: 'custom'
              customCommand: 'install -g pnpm'
          - script: pnpm i
            displayName: 'pnpm install'
          # test hrvyst-app
          - script: pnpm test
            workingDirectory: ./packages/hrvyst-app
            displayName: 'pnpm test'

      - job: build
        pool:
          vmImage: ubuntu-latest
        steps:
          # setup pnpm
          - task: UseNode@1
            inputs:
              version: '20.x'
          - task: Npm@1
            displayName: 'Setup pnpm'
            inputs:
              command: 'custom'
              customCommand: 'install -g pnpm'
          - script: pnpm i
            displayName: 'pnpm install'
          # build hrvyst-react (this needs to happen before lint, so the types are generated)
          - script: pnpm --filter hrvyst-react build
            displayName: 'build hryvst-react'
          # lint
          - script: pnpm lint
            displayName: 'pnpm lint'
          # build hrvyst-app
          - script: pnpm --filter hrvyst-app build
            displayName: 'build hryvst-app'
            env:
              SENTRY_AUTH_TOKEN: $(SENTRY_AUTH_TOKEN)
          # add web.config to build
          - script: node ./scripts/create-web-config.mjs
            displayName: 'create web.config'
          # publish
          - publish: ./packages/hrvyst-app/build
            artifact: frontend

  ## Development
  - stage: dev
    dependsOn: build
    condition: and(succeeded(), or(eq(variables['build.sourceBranch'], 'refs/heads/master'), eq(variables['build.sourceBranch'], 'refs/heads/develop'), eq(variables['build.sourceBranch'],'refs/heads/development')))
    displayName: Deploy to Development
    jobs:
      - job: UploadSourceMapsDev
        pool:
          vmImage: windows-2022
        variables:
          - group: rjo.dev

        steps:
          - download: current
            artifact: frontend

          - script: node ./scripts/upload-sourcemaps.mjs
            displayName: 'Upload Source Maps to Elastic (DEV)'

      - template: deployment-template.yaml
        parameters:
          environment: Development
          environmentVariableGroup: rjo.dev
          azureSubscription: 'dv-eastus-hrvyst-fe'
          webAppName: 'dv-rjohrvystdemo-app'
          resourceGroupName: 'dv-eastus-rjohrvystdemoapp-rg'

  ## QA
  - stage: QA
    dependsOn: build
    condition: and(succeeded(), eq(variables['build.sourceBranch'], 'refs/heads/master'))
    displayName: Deploy to Qa
    jobs:
      - job: UploadSourceMapsQA
        pool:
          vmImage: windows-2022
        variables:
          - group: rjo.qas

        steps:
          - download: current
            artifact: frontend

          - script: node ./scripts/upload-sourcemaps.mjs
            displayName: 'Upload Source Maps to Elastic (QA)'

      - template: deployment-template.yaml
        parameters:
          environment: QA
          environmentVariableGroup: rjo.qas
          azureSubscription: 'qa-eastus-hrvyst-fe'
          webAppName: 'qas-eastus-hrvyst-app'
          resourceGroupName: 'qas-eastus-hrvyst-rg'

  ## Staging
  - stage: Staging
    dependsOn: build
    condition: and(succeeded(), startsWith(variables['build.sourceBranch'], 'refs/heads/release'))
    displayName: Deploy to Staging
    jobs:
      - job: UploadSourceMapsStaging
        pool:
          vmImage: windows-2022
        variables:
          - group: rjo.st

        steps:
          - download: current
            artifact: frontend

          - script: node ./scripts/upload-sourcemaps.mjs
            displayName: 'Upload Source Maps to Elastic (STAGING)'

      - template: deployment-template.yaml
        parameters:
          environment: Staging
          environmentVariableGroup: rjo.st
          azureSubscription: 'st-eastus-hrvyst-fe'
          webAppName: 'st-eastus-hrvyst-app'
          resourceGroupName: 'st-eastus-hrvyst-rg'

  ## Production
  - stage: Production
    dependsOn:
      - build
      - Staging
    condition: and(succeeded(), startsWith(variables['build.sourceBranch'], 'refs/heads/release'))
    displayName: Deploy to Production
    jobs:
      - job: UploadSourceMapsProd
        pool:
          vmImage: windows-2022
        variables:
          - group: rjo.pr

        steps:
          - download: current
            artifact: frontend

          - script: node ./scripts/upload-sourcemaps.mjs
            displayName: 'Upload Source Maps to Elastic (PROD)'

      - template: deployment-template.yaml
        parameters:
          environment: Production
          environmentVariableGroup: rjo.pr
          azureSubscription: 'pr-eastus-hrvyst-fe'
          webAppName: 'pr-eastus-hrvyst-app'
          resourceGroupName: 'pr-eastus-hrvyst-rg'
