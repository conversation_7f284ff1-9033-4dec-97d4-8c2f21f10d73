lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

catalogs:
  default:
    '@eslint-community/eslint-plugin-eslint-comments':
      specifier: ^4.4.1
      version: 4.4.1
    '@eslint/compat':
      specifier: ^1.2.5
      version: 1.2.6
    '@eslint/js':
      specifier: ^9.18.0
      version: 9.19.0
    '@testing-library/jest-dom':
      specifier: ^6.6.3
      version: 6.6.3
    eslint:
      specifier: ^9.18.0
      version: 9.19.0
    eslint-config-prettier:
      specifier: ^10.0.1
      version: 10.0.1
    eslint-plugin-simple-import-sort:
      specifier: ^12.1.1
      version: 12.1.1
    globals:
      specifier: ^16.0.0
      version: 16.0.0
    jsdom:
      specifier: ^26.0.0
      version: 26.0.0
    knip:
      specifier: ^5.50.2
      version: 5.50.2
    prettier:
      specifier: ^3.5.1
      version: 3.5.1
    typescript:
      specifier: ^5.5.2
      version: 5.5.2
    typescript-eslint:
      specifier: ^8.20.0
      version: 8.23.0
    vite:
      specifier: ^6.0.0
      version: 6.1.0
    vitest:
      specifier: ^3.0.0
      version: 3.0.5
    zod:
      specifier: ^3.24.2
      version: 3.24.2

importers:

  .:
    devDependencies:
      husky:
        specifier: ^9.1.7
        version: 9.1.7

  packages/eslint-plugin-hrvyst-react: {}

  packages/hrvyst-app:
    devDependencies:
      '@elastic/apm-rum':
        specifier: ^5.16.0
        version: 5.16.0
      '@eslint-community/eslint-plugin-eslint-comments':
        specifier: 'catalog:'
        version: 4.4.1(eslint@9.19.0(jiti@2.4.2))
      '@eslint/compat':
        specifier: 'catalog:'
        version: 1.2.6(eslint@9.19.0(jiti@2.4.2))
      '@eslint/js':
        specifier: 'catalog:'
        version: 9.19.0
      '@floating-ui/dom':
        specifier: ^1.7.0
        version: 1.7.0
      '@lucide/svelte':
        specifier: ^0.486.0
        version: 0.486.0(svelte@5.32.0)
      '@sentry/browser':
        specifier: ^9.9.0
        version: 9.9.0
      '@sentry/vite-plugin':
        specifier: ^3.2.2
        version: 3.2.2
      '@skeletonlabs/skeleton':
        specifier: 3.1.1
        version: 3.1.1(tailwindcss@4.0.9)
      '@skeletonlabs/skeleton-svelte':
        specifier: 1.0.0
        version: 1.0.0(@skeletonlabs/skeleton@3.1.1(tailwindcss@4.0.9))(svelte@5.32.0)
      '@sveltejs/adapter-node':
        specifier: ^5.2.12
        version: 5.2.12(@sveltejs/kit@2.21.1(@sveltejs/vite-plugin-svelte@5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))
      '@sveltejs/adapter-static':
        specifier: ^3.0.8
        version: 3.0.8(@sveltejs/kit@2.21.1(@sveltejs/vite-plugin-svelte@5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))
      '@sveltejs/kit':
        specifier: ^2.21.1
        version: 2.21.1(@sveltejs/vite-plugin-svelte@5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      '@sveltejs/vite-plugin-svelte':
        specifier: ^5.0.3
        version: 5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      '@tailwindcss/forms':
        specifier: ^0.5.9
        version: 0.5.10(tailwindcss@4.0.9)
      '@tailwindcss/typography':
        specifier: ^0.5.15
        version: 0.5.16(tailwindcss@4.0.9)
      '@tailwindcss/vite':
        specifier: ^4.0.0
        version: 4.0.9(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      '@testing-library/jest-dom':
        specifier: 'catalog:'
        version: 6.6.3
      '@testing-library/svelte':
        specifier: ^5.2.4
        version: 5.2.7(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))(vitest@3.0.5(@types/node@20.14.8)(jiti@2.4.2)(jsdom@26.0.0)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      bits-ui:
        specifier: ^1.5.3
        version: 1.5.3(svelte@5.32.0)
      eslint:
        specifier: 'catalog:'
        version: 9.19.0(jiti@2.4.2)
      eslint-config-prettier:
        specifier: 'catalog:'
        version: 10.0.1(eslint@9.19.0(jiti@2.4.2))
      eslint-plugin-simple-import-sort:
        specifier: 'catalog:'
        version: 12.1.1(eslint@9.19.0(jiti@2.4.2))
      eslint-plugin-svelte:
        specifier: ^3.8.2
        version: 3.8.2(eslint@9.19.0(jiti@2.4.2))(svelte@5.32.0)
      globals:
        specifier: 'catalog:'
        version: 16.0.0
      hrvyst-react:
        specifier: workspace:^
        version: link:../hrvyst-react
      intl-messageformat:
        specifier: ^10.7.15
        version: 10.7.15
      jsdom:
        specifier: 'catalog:'
        version: 26.0.0
      jwt-decode:
        specifier: ^4.0.0
        version: 4.0.0
      knip:
        specifier: 'catalog:'
        version: 5.50.2(@types/node@20.14.8)(typescript@5.5.2)
      launchdarkly-js-client-sdk:
        specifier: ^3.5.0
        version: 3.5.0
      posthog-js:
        specifier: ^1.245.1
        version: 1.245.1
      prettier:
        specifier: 'catalog:'
        version: 3.5.1
      prettier-plugin-svelte:
        specifier: ^3.4.0
        version: 3.4.0(prettier@3.5.1)(svelte@5.32.0)
      prettier-plugin-tailwindcss:
        specifier: ^0.6.11
        version: 0.6.11(prettier-plugin-svelte@3.4.0(prettier@3.5.1)(svelte@5.32.0))(prettier@3.5.1)
      spare-bones:
        specifier: ^0.2.0
        version: 0.2.0(svelte@5.32.0)
      svelte:
        specifier: ^5.32.0
        version: 5.32.0
      svelte-check:
        specifier: ^4.2.1
        version: 4.2.1(picomatch@4.0.2)(svelte@5.32.0)(typescript@5.5.2)
      tailwindcss:
        specifier: ^4.0.0
        version: 4.0.9
      typescript:
        specifier: 'catalog:'
        version: 5.5.2
      typescript-eslint:
        specifier: 'catalog:'
        version: 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      vite:
        specifier: 'catalog:'
        version: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
      vite-plugin-devtools-json:
        specifier: ^0.1.0
        version: 0.1.0(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      vitest:
        specifier: 'catalog:'
        version: 3.0.5(@types/node@20.14.8)(jiti@2.4.2)(jsdom@26.0.0)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)

  packages/hrvyst-gen:
    dependencies:
      zod:
        specifier: 'catalog:'
        version: 3.24.2
    devDependencies:
      json5:
        specifier: ^2.2.3
        version: 2.2.3
      prettier:
        specifier: 'catalog:'
        version: 3.5.1

  packages/hrvyst-react:
    dependencies:
      '@ant-design/icons':
        specifier: 4.8.3
        version: 4.8.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@hookform/devtools':
        specifier: 2.2.1
        version: 2.2.1(react-dom@16.14.0(react@16.14.0))(react-hook-form@6.15.8(react@16.14.0))(react@16.14.0)
      '@hookform/resolvers':
        specifier: ^1.3.8
        version: 1.3.8(react-hook-form@6.15.8(react@16.14.0))
      '@microsoft/signalr':
        specifier: ^7.0.3
        version: 7.0.14
      '@reduxjs/toolkit':
        specifier: 1.3.2
        version: 1.3.2
      antd:
        specifier: '4.6'
        version: 4.6.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      axios:
        specifier: ^1.7.2
        version: 1.7.2
      clsx:
        specifier: ^2.1.1
        version: 2.1.1
      esm-env:
        specifier: ^1.2.2
        version: 1.2.2
      formik:
        specifier: ^2.4.6
        version: 2.4.6(react@16.14.0)
      formik-antd:
        specifier: ^2.0.4
        version: 2.0.4(antd@4.6.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(formik@2.4.6(react@16.14.0))(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      hrvyst-gen:
        specifier: workspace:*
        version: link:../hrvyst-gen
      i18next:
        specifier: 19.3.4
        version: 19.3.4
      i18next-browser-languagedetector:
        specifier: 4.0.2
        version: 4.0.2
      immer:
        specifier: ^6.0.9
        version: 6.0.9
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      moment:
        specifier: ^2.30.1
        version: 2.30.1
      moment-timezone:
        specifier: ^0.5.45
        version: 0.5.45
      numbro:
        specifier: 2.3.1
        version: 2.3.1
      rc-resize-observer:
        specifier: ^1.4.0
        version: 1.4.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react:
        specifier: ^16.14.0
        version: 16.14.0
      react-abac:
        specifier: 0.0.7
        version: 0.0.7(react@16.14.0)
      react-dom:
        specifier: ^16.14.0
        version: 16.14.0(react@16.14.0)
      react-hook-form:
        specifier: 6.15.8
        version: 6.15.8(react@16.14.0)
      react-i18next:
        specifier: 11.6.0
        version: 11.6.0(i18next@19.3.4)(react@16.14.0)
      react-intl:
        specifier: workspace:*
        version: link:../react-intl
      react-redux:
        specifier: 7.2.0
        version: 7.2.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)(redux@4.2.1)
      react-split:
        specifier: ^2.0.14
        version: 2.0.14(react@16.14.0)
      redux-injectors:
        specifier: 1.3.0
        version: 1.3.0(react-dom@16.14.0(react@16.14.0))(react-redux@7.2.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)(redux@4.2.1))(react@16.14.0)
      redux-saga:
        specifier: 1.1.3
        version: 1.1.3
      sanitize.css:
        specifier: 11.0.0
        version: 11.0.0
      typed-redux-saga:
        specifier: ^1.5.0
        version: 1.5.0(redux-saga@1.1.3)
      yup:
        specifier: 0.29.1
        version: 0.29.1
      zod:
        specifier: 'catalog:'
        version: 3.24.2
    devDependencies:
      '@eslint-community/eslint-plugin-eslint-comments':
        specifier: 'catalog:'
        version: 4.4.1(eslint@9.19.0(jiti@2.4.2))
      '@eslint-react/eslint-plugin':
        specifier: ^1.26.2
        version: 1.26.2(eslint@9.19.0(jiti@2.4.2))(ts-api-utils@2.0.1(typescript@5.5.2))(typescript@5.5.2)
      '@eslint/compat':
        specifier: 'catalog:'
        version: 1.2.6(eslint@9.19.0(jiti@2.4.2))
      '@eslint/js':
        specifier: 'catalog:'
        version: 9.19.0
      '@jambit/eslint-plugin-typed-redux-saga':
        specifier: ^0.4.0
        version: 0.4.0
      '@testing-library/dom':
        specifier: 7.26.7
        version: 7.26.7
      '@testing-library/jest-dom':
        specifier: 'catalog:'
        version: 6.6.3
      '@testing-library/react':
        specifier: 10.0.1
        version: 10.0.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@testing-library/react-hooks':
        specifier: 3.4.2
        version: 3.4.2(react-test-renderer@16.13.0(react@16.14.0))(react@16.14.0)
      '@testing-library/user-event':
        specifier: 12.2.2
        version: 12.2.2(@testing-library/dom@7.26.7)
      '@types/lodash':
        specifier: 4.17.15
        version: 4.17.15
      '@types/node':
        specifier: ^20.14.7
        version: 20.14.8
      '@types/react':
        specifier: 16.9.25
        version: 16.9.25
      '@types/react-dom':
        specifier: 16.9.3
        version: 16.9.3
      '@types/react-redux':
        specifier: 7.1.7
        version: 7.1.7
      '@types/testing-library__jest-dom':
        specifier: 5.14.0
        version: 5.14.0
      '@types/testing-library__react':
        specifier: 10.0.1
        version: 10.0.1
      '@types/testing-library__react-hooks':
        specifier: 3.4.1
        version: 3.4.1
      '@types/yup':
        specifier: ^0.29.1
        version: 0.29.1
      '@vitejs/plugin-react-swc':
        specifier: ^3.7.2
        version: 3.7.2(@swc/helpers@0.5.17)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      '@vitest/coverage-v8':
        specifier: ^3.0.5
        version: 3.0.5(vitest@3.0.5(@types/node@20.14.8)(jiti@2.4.2)(jsdom@26.0.0)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      autoprefixer:
        specifier: ^10.4.21
        version: 10.4.21(postcss@8.5.1)
      cross-env:
        specifier: ^7.0.3
        version: 7.0.3
      eslint:
        specifier: 'catalog:'
        version: 9.19.0(jiti@2.4.2)
      eslint-config-prettier:
        specifier: 'catalog:'
        version: 10.0.1(eslint@9.19.0(jiti@2.4.2))
      eslint-plugin-react-hooks:
        specifier: ^5.1.0
        version: 5.1.0(eslint@9.19.0(jiti@2.4.2))
      eslint-plugin-simple-import-sort:
        specifier: 'catalog:'
        version: 12.1.1(eslint@9.19.0(jiti@2.4.2))
      extract-react-intl-messages:
        specifier: 4.1.1
        version: 4.1.1
      globals:
        specifier: 'catalog:'
        version: 16.0.0
      jest-extended:
        specifier: ^4.0.2
        version: 4.0.2
      jsdom:
        specifier: 'catalog:'
        version: 26.0.0
      json-sass:
        specifier: 1.3.5
        version: 1.3.5
      jwt-decode:
        specifier: ^4.0.0
        version: 4.0.0
      knip:
        specifier: 'catalog:'
        version: 5.50.2(@types/node@20.14.8)(typescript@5.5.2)
      less:
        specifier: 4.2.0
        version: 4.2.0
      lint-staged:
        specifier: ^15.4.3
        version: 15.4.3
      postcss-rem-to-pixel:
        specifier: ^4.1.2
        version: 4.1.2
      prettier:
        specifier: 'catalog:'
        version: 3.5.1
      sass:
        specifier: ^1.57.1
        version: 1.77.6
      typescript:
        specifier: 'catalog:'
        version: 5.5.2
      typescript-eslint:
        specifier: 'catalog:'
        version: 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      typescript-strict-plugin:
        specifier: ^2.4.4
        version: 2.4.4
      vite:
        specifier: 'catalog:'
        version: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
      vite-plugin-dts:
        specifier: ^4.5.3
        version: 4.5.3(@types/node@20.14.8)(rollup@4.34.4)(typescript@5.5.2)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      vite-tsconfig-paths:
        specifier: ^5.1.4
        version: 5.1.4(typescript@5.5.2)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      vitest:
        specifier: 'catalog:'
        version: 3.0.5(@types/node@20.14.8)(jiti@2.4.2)(jsdom@26.0.0)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
      zod-fixture:
        specifier: ^2.5.2
        version: 2.5.2(zod@3.24.2)

  packages/react-intl: {}

packages:

  '@adobe/css-tools@4.4.0':
    resolution: {integrity: sha512-Ff9+ksdQQB3rMncgqDK78uLznstjyfIf2Arnh22pW8kBpLs6rpKDwgnZT46hin5Hl1WzazzK64DOrhSwYpS7bQ==}

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@ant-design/colors@4.0.5':
    resolution: {integrity: sha512-3mnuX2prnWOWvpFTS2WH2LoouWlOgtnIpc6IarWN6GOzzLF8dW/U8UctuvIPhoboETehZfJ61XP+CGakBEPJ3Q==}

  '@ant-design/colors@6.0.0':
    resolution: {integrity: sha512-qAZRvPzfdWHtfameEGP2Qvuf838NhergR35o+EuVyB5XvSA98xod5r4utvi4TJ3ywmevm290g9nsCG5MryrdWQ==}

  '@ant-design/css-animation@1.7.3':
    resolution: {integrity: sha512-LrX0OGZtW+W6iLnTAqnTaoIsRelYeuLZWsrmBJFUXDALQphPsN8cE5DCsmoSlL0QYb94BQxINiuS70Ar/8BNgA==}

  '@ant-design/icons-svg@4.4.2':
    resolution: {integrity: sha512-vHbT+zJEVzllwP+CM+ul7reTEfBR0vgxFe7+lREAsAA7YGsYpboiq2sQNeQeRvh09GfQgs/GyFEvZpJ9cLXpXA==}

  '@ant-design/icons@4.8.3':
    resolution: {integrity: sha512-HGlIQZzrEbAhpJR6+IGdzfbPym94Owr6JZkJ2QCCnOkPVIWMO2xgIVcOKnl8YcpijIo39V7l2qQL5fmtw56cMw==}
    engines: {node: '>=8'}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  '@ant-design/react-slick@0.27.14':
    resolution: {integrity: sha512-s6JVexqFmU5rs5Pm828ojtm5rCp8jDXyrc5OxEtCE2z58SIyQlkpnU9BJh98LEeBZyj02WFkGN8CWpSaD+G4PA==}
    peerDependencies:
      react: ^0.14.0 || ^15.0.1 || ^16.0.0 || ^17.0.0
      react-dom: ^0.14.0 || ^15.0.1 || ^16.0.0 || ^17.0.0

  '@asamuzakjp/css-color@2.8.3':
    resolution: {integrity: sha512-GIc76d9UI1hCvOATjZPyHFmE5qhRccp3/zGfMPapK3jBi+yocEzp6BBB0UnfRYP9NP4FANqUZYb0hnfs3TM3hw==}

  '@babel/code-frame@7.24.7':
    resolution: {integrity: sha512-BcYH1CVJBO9tvyIZ2jVeXgSIMvGZ2FDRvDdOIVQyuklNKSsx+eppDEBq/g47Ayw+RqNFE+URvOShmf+f/qwAlA==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.25.4':
    resolution: {integrity: sha512-+LGRog6RAsCJrrrg/IO6LGmpphNe5DiK30dGjCoxxeGv49B10/3XYGxPsAwrDlMFcFEvdAUavDT8r9k/hSyQqQ==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.24.7':
    resolution: {integrity: sha512-nykK+LEK86ahTkX/3TgauT0ikKoNCfKHEaZYTUVupJdTLzGNvrblu4u6fa7DhZONAltdf8e662t/abY8idrd/g==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.25.6':
    resolution: {integrity: sha512-VPC82gr1seXOpkjAAKoLhP50vx4vGNlF4msF64dSFq1P8RfB+QAuJWGHPXXPc8QyfVWwwB/TNNU4+ayZmHNbZw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.25.2':
    resolution: {integrity: sha512-U2U5LsSaZ7TAt3cfaymQ8WHh0pxvdHoEk6HVpaexxixjyEquMh0L0YNJNM6CTGKMXV1iksi0iZkGw4AcFkPaaw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.24.7':
    resolution: {integrity: sha512-8AyH3C+74cgCVVXow/myrynrAGv+nTVg5vKu2nZph9x7RcRwzmh0VFallJuFTZ9mx6u4eSdXZfcOzSqTUm0HCA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.25.2':
    resolution: {integrity: sha512-BjyRAbix6j/wv83ftcVJmBt72QtHI56C7JXZoG2xATiLpmoC7dpd8WnkikExHDVPpi/3qCmO6WY1EaXOluiecQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-plugin-utils@7.24.8':
    resolution: {integrity: sha512-FFWx5142D8h2Mgr/iPVGH5G7w6jDn4jUSpZTyDnQO0Yn7Ks2Kuz6Pci8H6MPCoUJegd/UZQ3tAvfLCxQSnWWwg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-simple-access@7.24.7':
    resolution: {integrity: sha512-zBAIvbCMh5Ts+b86r/CjU+4XGYIs+R1j951gxI3KmmxBMhCg4oQMsv6ZXQ64XOm/cvzfU1FmoCyt6+owc5QMYg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.24.8':
    resolution: {integrity: sha512-pO9KhhRcuUyGnJWwyEgnRJTSIZHiT+vMD0kPeD+so0l7mxkMT19g3pjY9GTnHySck/hDzq+dtW/4VgnMkippsQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.24.7':
    resolution: {integrity: sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.24.8':
    resolution: {integrity: sha512-xb8t9tD1MHLungh/AIoWYN+gVHaB9kwlu8gffXGSt3FFEIT7RjS+xWbc2vUD1UTZdIpKj/ab3rdqJ7ufngyi2Q==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.24.7':
    resolution: {integrity: sha512-NlmJJtvcw72yRJRcnCmGvSi+3jDEg8qFu3z0AFoymmzLx5ERVWyzd9kVXr7Th9/8yIJi2Zc6av4Tqz3wFs8QWg==}
    engines: {node: '>=6.9.0'}

  '@babel/highlight@7.24.7':
    resolution: {integrity: sha512-EStJpq4OuY8xYfhGVXngigBJRWxftKX9ksiGDnmlY3o7B/V7KIAc9X4oiK87uPJSc/vs5L869bem5fhZa8caZw==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.25.6':
    resolution: {integrity: sha512-trGdfBdbD0l1ZPmcJ83eNxB9rbEax4ALFTF7fN386TMYbeCQbyme5cOEXQhbGXKebwGaB/J52w1mrklMcbgy6Q==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/runtime-corejs3@7.24.7':
    resolution: {integrity: sha512-eytSX6JLBY6PVAeQa2bFlDx/7Mmln/gaEpsit5a3WEvjGfiIytEsgAwuIXCPM0xvw0v0cJn3ilq0/TvXrW0kgA==}
    engines: {node: '>=6.9.0'}

  '@babel/runtime@7.24.7':
    resolution: {integrity: sha512-UwgBRMjJP+xv857DCngvqXI3Iq6J4v0wXmwc6sapg+zyhbwmQX67LUEFrkK5tbyJ30jGuG3ZvWpBiB9LCy1kWw==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.25.0':
    resolution: {integrity: sha512-aOOgh1/5XzKvg1jvVz7AVrx2piJ2XBi227DHmbY6y+bM9H2FlN+IfecYu4Xl0cNiiVejlsCri89LUsbj8vJD9Q==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.25.6':
    resolution: {integrity: sha512-9Vrcx5ZW6UwK5tvqsj0nGpp/XzqthkT0dqIc9g1AdtygFToNtTF67XzYS//dm+SAK9cp3B9R4ZO/46p63SCjlQ==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.25.6':
    resolution: {integrity: sha512-/l42B1qxpG6RdfYf343Uw1vmDjeNhneUXtzhojE7pDgfpEypmRhI6j1kr17XCVv4Cgl9HdAiQY2x0GwKm7rWCw==}
    engines: {node: '>=6.9.0'}

  '@bcoe/v8-coverage@1.0.2':
    resolution: {integrity: sha512-6zABk/ECA/QYSCQ1NGiVwwbQerUCZ+TQbp64Q3AgmfNvurHH0j8TtXa1qbShXA6qqkpAj4V5W8pP6mLe1mcMqA==}
    engines: {node: '>=18'}

  '@csstools/color-helpers@5.0.1':
    resolution: {integrity: sha512-MKtmkA0BX87PKaO1NFRTFH+UnkgnmySQOvNxJubsadusqPEC2aJ9MOQiMceZJJ6oitUl/i0L6u0M1IrmAOmgBA==}
    engines: {node: '>=18'}

  '@csstools/css-calc@2.1.1':
    resolution: {integrity: sha512-rL7kaUnTkL9K+Cvo2pnCieqNpTKgQzy5f+N+5Iuko9HAoasP+xgprVh7KN/MaJVvVL1l0EzQq2MoqBHKSrDrag==}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.4
      '@csstools/css-tokenizer': ^3.0.3

  '@csstools/css-color-parser@3.0.7':
    resolution: {integrity: sha512-nkMp2mTICw32uE5NN+EsJ4f5N+IGFeCFu4bGpiKgb2Pq/7J/MpyLBeQ5ry4KKtRFZaYs6sTmcMYrSRIyj5DFKA==}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.4
      '@csstools/css-tokenizer': ^3.0.3

  '@csstools/css-parser-algorithms@3.0.4':
    resolution: {integrity: sha512-Up7rBoV77rv29d3uKHUIVubz1BTcgyUK72IvCQAbfbMv584xHcGKCKbWh7i8hPrRJ7qU4Y8IO3IY9m+iTB7P3A==}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-tokenizer': ^3.0.3

  '@csstools/css-tokenizer@3.0.3':
    resolution: {integrity: sha512-UJnjoFsmxfKUdNYdWgOB0mWUypuLvAfQPH1+pyvRJs6euowbFkFC6P13w1l8mJyi3vxYMxc9kld5jZEGRQs6bw==}
    engines: {node: '>=18'}

  '@ctrl/tinycolor@3.6.1':
    resolution: {integrity: sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==}
    engines: {node: '>=10'}

  '@elastic/apm-rum-core@5.21.1':
    resolution: {integrity: sha512-/LyLhVdJ+zcsKogwq2AEYARc//RDXOoTHWPERLay4sCsvvxc4/GkkhhOC40CqI0oMu4kUAoJInQWZuCM7zCZRQ==}
    engines: {node: '>=8.0.0'}

  '@elastic/apm-rum@5.16.0':
    resolution: {integrity: sha512-vNwRWDIccdu6iPDpCyrC3myRnpBzRH6wlAoOZjgSFJilQJMsymb8NzR1rkQmNEBca+frRuDCZabd+ylEZ47YBA==}
    engines: {node: '>=8.0.0'}

  '@emotion/cache@10.0.29':
    resolution: {integrity: sha512-fU2VtSVlHiF27empSbxi1O2JFdNWZO+2NFHfwO0pxgTep6Xa3uGb+3pVKfLww2l/IBGLNEZl5Xf/++A4wAYDYQ==}

  '@emotion/core@10.3.1':
    resolution: {integrity: sha512-447aUEjPIm0MnE6QYIaFz9VQOHSXf4Iu6EWOIqq11EAPqinkSZmfymPTmlOE3QjLv846lH4JVZBUOtwGbuQoww==}
    peerDependencies:
      react: '>=16.3.0'

  '@emotion/css@10.0.27':
    resolution: {integrity: sha512-6wZjsvYeBhyZQYNrGoR5yPMYbMBNEnanDrqmsqS1mzDm1cOTu12shvl2j4QHNS36UaTE0USIJawCH9C8oW34Zw==}

  '@emotion/hash@0.8.0':
    resolution: {integrity: sha512-kBJtf7PH6aWwZ6fka3zQ0p6SBYzx4fl1LoZXE2RrnYST9Xljm7WfKJrU4g/Xr3Beg72MLrp1AWNUmuYJTL7Cow==}

  '@emotion/is-prop-valid@0.8.8':
    resolution: {integrity: sha512-u5WtneEAr5IDG2Wv65yhunPSMLIpuKsbuOktRojfrEiEvRyC85LgPMZI63cr7NUqT8ZIGdSVg8ZKGxIug4lXcA==}

  '@emotion/memoize@0.7.4':
    resolution: {integrity: sha512-Ja/Vfqe3HpuzRsG1oBtWTHk2PGZ7GR+2Vz5iYGelAw8dx32K0y7PjVuxK6z1nMpZOqAFsRUPCkK1YjJ56qJlgw==}

  '@emotion/serialize@0.11.16':
    resolution: {integrity: sha512-G3J4o8by0VRrO+PFeSc3js2myYNOXVJ3Ya+RGVxnshRYgsvErfAOglKAiy1Eo1vhzxqtUvjCyS5gtewzkmvSSg==}

  '@emotion/sheet@0.9.4':
    resolution: {integrity: sha512-zM9PFmgVSqBw4zL101Q0HrBVTGmpAxFZH/pYx/cjJT5advXguvcgjHFTCaIO3enL/xr89vK2bh0Mfyj9aa0ANA==}

  '@emotion/styled-base@10.3.0':
    resolution: {integrity: sha512-PBRqsVKR7QRNkmfH78hTSSwHWcwDpecH9W6heujWAcyp2wdz/64PP73s7fWS1dIPm8/Exc8JAzYS8dEWXjv60w==}
    peerDependencies:
      '@emotion/core': ^10.0.28
      react: '>=16.3.0'

  '@emotion/styled@10.3.0':
    resolution: {integrity: sha512-GgcUpXBBEU5ido+/p/mCT2/Xx+Oqmp9JzQRuC+a4lYM4i4LBBn/dWvc0rQ19N9ObA8/T4NWMrPNe79kMBDJqoQ==}
    peerDependencies:
      '@emotion/core': ^10.0.27
      react: '>=16.3.0'

  '@emotion/stylis@0.8.5':
    resolution: {integrity: sha512-h6KtPihKFn3T9fuIrwvXXUOwlx3rfUvfZIcP5a6rh8Y7zjE3O06hT5Ss4S/YI1AYhuZ1kjaE/5EaOOI2NqSylQ==}

  '@emotion/unitless@0.7.5':
    resolution: {integrity: sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg==}

  '@emotion/utils@0.11.3':
    resolution: {integrity: sha512-0o4l6pZC+hI88+bzuaX/6BgOvQVhbt2PfmxauVaYOGgbsAw14wdKyvMCZXnsnsHys94iadcF+RG/wZyx6+ZZBw==}

  '@emotion/weak-memoize@0.2.5':
    resolution: {integrity: sha512-6U71C2Wp7r5XtFtQzYrW5iKFT67OixrSxjI4MptCHzdSVlgabczzqLe0ZSgnub/5Kp4hSbpDB1tMytZY9pwxxA==}

  '@esbuild/aix-ppc64@0.24.2':
    resolution: {integrity: sha512-thpVCb/rhxE/BnMLQ7GReQLLN8q9qbHmI55F4489/ByVg2aQaQ6kbcLb6FHkocZzQhxc4gx0sCk0tJkKBFzDhA==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.24.2':
    resolution: {integrity: sha512-cNLgeqCqV8WxfcTIOeL4OAtSmL8JjcN6m09XIgro1Wi7cF4t/THaWEa7eL5CMoMBdjoHOTh/vwTO/o2TRXIyzg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.24.2':
    resolution: {integrity: sha512-tmwl4hJkCfNHwFB3nBa8z1Uy3ypZpxqxfTQOcHX+xRByyYgunVbZ9MzUUfb0RxaHIMnbHagwAxuTL+tnNM+1/Q==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.24.2':
    resolution: {integrity: sha512-B6Q0YQDqMx9D7rvIcsXfmJfvUYLoP722bgfBlO5cGvNVb5V/+Y7nhBE3mHV9OpxBf4eAS2S68KZztiPaWq4XYw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.24.2':
    resolution: {integrity: sha512-kj3AnYWc+CekmZnS5IPu9D+HWtUI49hbnyqk0FLEJDbzCIQt7hg7ucF1SQAilhtYpIujfaHr6O0UHlzzSPdOeA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.24.2':
    resolution: {integrity: sha512-WeSrmwwHaPkNR5H3yYfowhZcbriGqooyu3zI/3GGpF8AyUdsrrP0X6KumITGA9WOyiJavnGZUwPGvxvwfWPHIA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.24.2':
    resolution: {integrity: sha512-UN8HXjtJ0k/Mj6a9+5u6+2eZ2ERD7Edt1Q9IZiB5UZAIdPnVKDoG7mdTVGhHJIeEml60JteamR3qhsr1r8gXvg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.24.2':
    resolution: {integrity: sha512-TvW7wE/89PYW+IevEJXZ5sF6gJRDY/14hyIGFXdIucxCsbRmLUcjseQu1SyTko+2idmCw94TgyaEZi9HUSOe3Q==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.24.2':
    resolution: {integrity: sha512-7HnAD6074BW43YvvUmE/35Id9/NB7BeX5EoNkK9obndmZBUk8xmJJeU7DwmUeN7tkysslb2eSl6CTrYz6oEMQg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.24.2':
    resolution: {integrity: sha512-n0WRM/gWIdU29J57hJyUdIsk0WarGd6To0s+Y+LwvlC55wt+GT/OgkwoXCXvIue1i1sSNWblHEig00GBWiJgfA==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.24.2':
    resolution: {integrity: sha512-sfv0tGPQhcZOgTKO3oBE9xpHuUqguHvSo4jl+wjnKwFpapx+vUDcawbwPNuBIAYdRAvIDBfZVvXprIj3HA+Ugw==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.24.2':
    resolution: {integrity: sha512-CN9AZr8kEndGooS35ntToZLTQLHEjtVB5n7dl8ZcTZMonJ7CCfStrYhrzF97eAecqVbVJ7APOEe18RPI4KLhwQ==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.24.2':
    resolution: {integrity: sha512-iMkk7qr/wl3exJATwkISxI7kTcmHKE+BlymIAbHO8xanq/TjHaaVThFF6ipWzPHryoFsesNQJPE/3wFJw4+huw==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.24.2':
    resolution: {integrity: sha512-shsVrgCZ57Vr2L8mm39kO5PPIb+843FStGt7sGGoqiiWYconSxwTiuswC1VJZLCjNiMLAMh34jg4VSEQb+iEbw==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.24.2':
    resolution: {integrity: sha512-4eSFWnU9Hhd68fW16GD0TINewo1L6dRrB+oLNNbYyMUAeOD2yCK5KXGK1GH4qD/kT+bTEXjsyTCiJGHPZ3eM9Q==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.24.2':
    resolution: {integrity: sha512-S0Bh0A53b0YHL2XEXC20bHLuGMOhFDO6GN4b3YjRLK//Ep3ql3erpNcPlEFed93hsQAjAQDNsvcK+hV90FubSw==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.24.2':
    resolution: {integrity: sha512-8Qi4nQcCTbLnK9WoMjdC9NiTG6/E38RNICU6sUNqK0QFxCYgoARqVqxdFmWkdonVsvGqWhmm7MO0jyTqLqwj0Q==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.24.2':
    resolution: {integrity: sha512-wuLK/VztRRpMt9zyHSazyCVdCXlpHkKm34WUyinD2lzK07FAHTq0KQvZZlXikNWkDGoT6x3TD51jKQ7gMVpopw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.24.2':
    resolution: {integrity: sha512-VefFaQUc4FMmJuAxmIHgUmfNiLXY438XrL4GDNV1Y1H/RW3qow68xTwjZKfj/+Plp9NANmzbH5R40Meudu8mmw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.24.2':
    resolution: {integrity: sha512-YQbi46SBct6iKnszhSvdluqDmxCJA+Pu280Av9WICNwQmMxV7nLRHZfjQzwbPs3jeWnuAhE9Jy0NrnJ12Oz+0A==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.24.2':
    resolution: {integrity: sha512-+iDS6zpNM6EnJyWv0bMGLWSWeXGN/HTaF/LXHXHwejGsVi+ooqDfMCCTerNFxEkM3wYVcExkeGXNqshc9iMaOA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.24.2':
    resolution: {integrity: sha512-hTdsW27jcktEvpwNHJU4ZwWFGkz2zRJUz8pvddmXPtXDzVKTTINmlmga3ZzwcuMpUvLw7JkLy9QLKyGpD2Yxig==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.24.2':
    resolution: {integrity: sha512-LihEQ2BBKVFLOC9ZItT9iFprsE9tqjDjnbulhHoFxYQtQfai7qfluVODIYxt1PgdoyQkz23+01rzwNwYfutxUQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.24.2':
    resolution: {integrity: sha512-q+iGUwfs8tncmFC9pcnD5IvRHAzmbwQ3GPS5/ceCyHdjXubwQWI12MKWSNSMYLJMq23/IUCvJMS76PDqXe1fxA==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.24.2':
    resolution: {integrity: sha512-7VTgWzgMGvup6aSqDPLiW5zHaxYJGTO4OokMjIlrCtf+VpEL+cXKtCvg723iguPYI5oaUNdS+/V7OU2gvXVWEg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-plugin-eslint-comments@4.4.1':
    resolution: {integrity: sha512-lb/Z/MzbTf7CaVYM9WCFNQZ4L1yi3ev2fsFPF99h31ljhSEyUoyEsKsNWiU+qD1glbYTDJdqgyaLKtyTkkqtuQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0

  '@eslint-community/eslint-utils@4.4.1':
    resolution: {integrity: sha512-s3O3waFUrMV8P/XaF/+ZTp1X9XBZW1a4B97ZnjQF2KYWaFD2A8KyFBsrsfSjEmjn3RGWAIuvlneuZm3CUK3jbA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/eslint-utils@4.7.0':
    resolution: {integrity: sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint-react/ast@1.26.2':
    resolution: {integrity: sha512-WuljGOJaaiehGkW0aAyuCZIGKfcv/Q1fSl4rvlfWohIDgpp5MFIkBa56drR75WUdNKrrUb3JirnVGIAhegUBIA==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}

  '@eslint-react/core@1.26.2':
    resolution: {integrity: sha512-2mB5hZBL6XmOjDNL3o0h/qHQHuzxGQGYtQQHjD0Yddhde7NU/b4z/oxtrzEInc6Lk2Ry7Rhqi4S49EpwKXWJlQ==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}

  '@eslint-react/eff@1.26.2':
    resolution: {integrity: sha512-7ttz+DPNZl+cHdR5PwU9/ff95VHZmo10icGVX34HyRktJuU2boinWzib5KRg6V1jVwgWuzdvULNXyBd5NVMhhg==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}

  '@eslint-react/eslint-plugin@1.26.2':
    resolution: {integrity: sha512-nTfR32jTLChc0RXKbks2Gf6seMYeqiCGj0qYq+yOmEn/XhcDWVQj86SHIJLFPwvH3LSwDUSgiQzdW9jn/rNv3A==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: ^4.9.5 || ^5.3.3
    peerDependenciesMeta:
      typescript:
        optional: true

  '@eslint-react/jsx@1.26.2':
    resolution: {integrity: sha512-lldo9Sd/tZslBN8X7/ZAZXY7UccZZYctrNAoeR8DFMFWLxzvooykixLOl5YkRCWm4uaSmq3r3VNFZ35N2wcbyQ==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}

  '@eslint-react/shared@1.26.2':
    resolution: {integrity: sha512-q/xrNkFe8sHAPjaAuvqyCl3Ls5ly9cfUpAfhAgxYtArNAtIZHvuwu0zrwoHMYk0ZpZi+VlQYwUCtKX8axPXoTw==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}

  '@eslint-react/var@1.26.2':
    resolution: {integrity: sha512-9abwhGTd4DBxOy5jVF0CnjEYDiRTXg4cbbAulZ+MVqE03KZDWNAVYYEYI5e+YTOcyJbGYY/zPEYmB+c+cUEiyw==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}

  '@eslint/compat@1.2.6':
    resolution: {integrity: sha512-k7HNCqApoDHM6XzT30zGoETj+D+uUcZUb+IVAJmar3u6bvHf7hhHJcWx09QHj4/a2qrKZMWU0E16tvkiAdv06Q==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^9.10.0
    peerDependenciesMeta:
      eslint:
        optional: true

  '@eslint/config-array@0.19.1':
    resolution: {integrity: sha512-fo6Mtm5mWyKjA/Chy1BYTdn5mGJoDNjC7C64ug20ADsRDGrA85bN3uK3MaKbeRkRuuIEAR5N33Jr1pbm411/PA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.10.0':
    resolution: {integrity: sha512-gFHJ+xBOo4G3WRlR1e/3G8A6/KZAH6zcE/hkLRCZTi/B9avAG365QhFA8uOGzTMqgTghpn7/fSnscW++dpMSAw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@3.2.0':
    resolution: {integrity: sha512-grOjVNN8P3hjJn/eIETF1wwd12DdnwFDoyceUJLYYdkpbwq3nLi+4fqrTAONx7XDALqlL220wC/RHSC/QTI/0w==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@9.19.0':
    resolution: {integrity: sha512-rbq9/g38qjfqFLOVPvwjIvFFdNziEC5S65jmjPw5r6A//QH+W91akh9irMwjDN8zKUTak6W9EsAv4m/7Wnw0UQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.5':
    resolution: {integrity: sha512-o0bhxnL89h5Bae5T318nFoFzGy+YE5i/gGkoPAgkmTVdRKTiv3p8JHevPiPaMwoloKfEiiaHlawCqaZMqRm+XQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.2.5':
    resolution: {integrity: sha512-lB05FkqEdUg2AA0xEbUz0SnkXT1LcCTa438W4IWTUh4hdOnVbQyOJ81OrDXsJk/LSiJHubgGEFoR5EHq1NsH1A==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@floating-ui/core@1.6.9':
    resolution: {integrity: sha512-uMXCuQ3BItDUbAMhIXw7UPXRfAlOAvZzdK9BWpE60MCn+Svt3aLn9jsPTi/WNGlRUu2uI0v5S7JiIUsbsvh3fw==}

  '@floating-ui/core@1.7.0':
    resolution: {integrity: sha512-FRdBLykrPPA6P76GGGqlex/e7fbe0F1ykgxHYNXQsH/iTEtjMj/f9bpY5oQqbjt5VgZvgz/uKXbGuROijh3VLA==}

  '@floating-ui/dom@1.6.13':
    resolution: {integrity: sha512-umqzocjDgNRGTuO7Q8CU32dkHkECqI8ZdMZ5Swb6QAM0t5rnlrN3lGo1hdpscRd3WS8T6DKYK4ephgIH9iRh3w==}

  '@floating-ui/dom@1.7.0':
    resolution: {integrity: sha512-lGTor4VlXcesUMh1cupTUTDoCxMb0V6bm3CnxHzQcw8Eaf1jQbgQX4i02fYgT0vJ82tb5MZ4CZk1LRGkktJCzg==}

  '@floating-ui/utils@0.2.9':
    resolution: {integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==}

  '@formatjs/ecma402-abstract@1.4.0':
    resolution: {integrity: sha512-Mv027hcLFjE45K8UJ8PjRpdDGfR0aManEFj1KzoN8zXNveHGEygpZGfFf/FTTMl+QEVSrPAUlyxaCApvmv47AQ==}

  '@formatjs/ecma402-abstract@1.5.0':
    resolution: {integrity: sha512-wXv36yo+mfWllweN0Fq7sUs7PUiNopn7I0JpLTe3hGu6ZMR4CV7LqK1llhB18pndwpKoafQKb1et2DCJAOW20Q==}

  '@formatjs/ecma402-abstract@2.3.3':
    resolution: {integrity: sha512-pJT1OkhplSmvvr6i3CWTPvC/FGC06MbN5TNBfRO6Ox62AEz90eMq+dVvtX9Bl3jxCEkS0tATzDarRZuOLw7oFg==}

  '@formatjs/fast-memoize@2.2.6':
    resolution: {integrity: sha512-luIXeE2LJbQnnzotY1f2U2m7xuQNj2DA8Vq4ce1BY9ebRZaoPB1+8eZ6nXpLzsxuW5spQxr7LdCg+CApZwkqkw==}

  '@formatjs/icu-messageformat-parser@2.11.1':
    resolution: {integrity: sha512-o0AhSNaOfKoic0Sn1GkFCK4MxdRsw7mPJ5/rBpIqdvcC7MIuyUSW8WChUEvrK78HhNpYOgqCQbINxCTumJLzZA==}

  '@formatjs/icu-skeleton-parser@1.8.13':
    resolution: {integrity: sha512-N/LIdTvVc1TpJmMt2jVg0Fr1F7Q1qJPdZSCs19unMskCmVQ/sa0H9L8PWt13vq+gLdLg1+pPsvBLydL1Apahjg==}

  '@formatjs/intl-localematcher@0.6.0':
    resolution: {integrity: sha512-4rB4g+3hESy1bHSBG3tDFaMY2CH67iT7yne1e+0CLTsGLDcmoEWWpJjjpWVaYgYfYuohIRuo0E+N536gd2ZHZA==}

  '@formatjs/intl-numberformat@5.7.6':
    resolution: {integrity: sha512-ZlZfYtvbVHYZY5OG3RXizoCwxKxEKOrzEe2YOw9wbzoxF3PmFn0SAgojCFGLyNXkkR6xVxlylhbuOPf1dkIVNg==}

  '@formatjs/ts-transformer@2.13.0':
    resolution: {integrity: sha512-mu7sHXZk1NWZrQ3eUqugpSYo8x5/tXkrI4uIbFqCEC0eNgQaIcoKgVeDFgDAcgG+cEme2atAUYSFF+DFWC4org==}
    peerDependencies:
      ts-jest: ^26.4.0
    peerDependenciesMeta:
      ts-jest:
        optional: true

  '@hookform/devtools@2.2.1':
    resolution: {integrity: sha512-VQh4kqwUOpz9LCDzIP0aJ4qnD/ob8Gp09L8gDy++9XtL66z6g8kbCynUvBrJm4qbCNdH0M7/Spm3AUjJqUuFlA==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      react-hook-form: '>=6.5.0'

  '@hookform/resolvers@1.3.8':
    resolution: {integrity: sha512-mEUqsqbx5S78+uN1tVS1vrgaFEeh4cbFzBs0tuzvWmJFGSSDaqf0Z9SoKvrDFXl8eBIQvADA6H4vsbUYnlXuxw==}
    peerDependencies:
      react-hook-form: '>=6.6.0'

  '@humanfs/core@0.19.1':
    resolution: {integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==}
    engines: {node: '>=18.18.0'}

  '@humanfs/node@0.16.6':
    resolution: {integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==}
    engines: {node: '>=18.18.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}

  '@humanwhocodes/retry@0.3.1':
    resolution: {integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==}
    engines: {node: '>=18.18'}

  '@humanwhocodes/retry@0.4.1':
    resolution: {integrity: sha512-c7hNEllBlenFTHBky65mhq8WD2kbN9Q6gk0bTk8lSBvc554jpXSkST1iePudpt7+A/AQvuHs9EMqjHDXMY1lrA==}
    engines: {node: '>=18.18'}

  '@internationalized/date@3.8.1':
    resolution: {integrity: sha512-PgVE6B6eIZtzf9Gu5HvJxRK3ufUFz9DhspELuhW/N0GuMGMTLvPQNRkHP2hTuP9lblOk+f+1xi96sPiPXANXAA==}

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}

  '@istanbuljs/schema@0.1.3':
    resolution: {integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==}
    engines: {node: '>=8'}

  '@jambit/eslint-plugin-typed-redux-saga@0.4.0':
    resolution: {integrity: sha512-toReLCgaBZ6N0gbq6QTKUCkDqGS4yi0RV16HPLJ9rz8H1TE78KwLHAGhW3XLqyVn+DbcuL7+89RkQse8T5oj8g==}

  '@jest/schemas@29.6.3':
    resolution: {integrity: sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/types@25.5.0':
    resolution: {integrity: sha512-OXD0RgQ86Tu3MazKo8bnrkDRaDXXMGUqd+kTtLtK1Zb7CRzQcaSRPPPV37SvYTdevXEBVxe0HXylEjs8ibkmCw==}
    engines: {node: '>= 8.3'}

  '@jest/types@26.6.2':
    resolution: {integrity: sha512-fC6QCp7Sc5sX6g8Tvbmj4XUTbyrik0akgRy03yjXbQaBWWNWGE7SGtJk98m0N8nzegD/7SggrUlivxo5ax4KWQ==}
    engines: {node: '>= 10.14.2'}

  '@jridgewell/gen-mapping@0.3.5':
    resolution: {integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}

  '@lucide/svelte@0.486.0':
    resolution: {integrity: sha512-Q8dav9ruYhdLepb2NNngYeGaMHxFv4Qd1FPc7YYkqouHfjTOJ3fLeyKmbANioRRmtMrLBNfOez9v5EIca+VCFg==}
    peerDependencies:
      svelte: ^5

  '@microsoft/api-extractor-model@7.30.4':
    resolution: {integrity: sha512-RobC0gyVYsd2Fao9MTKOfTdBm41P/bCMUmzS5mQ7/MoAKEqy0FOBph3JOYdq4X4BsEnMEiSHc+0NUNmdzxCpjA==}

  '@microsoft/api-extractor@7.52.1':
    resolution: {integrity: sha512-m3I5uAwE05orsu3D1AGyisX5KxsgVXB+U4bWOOaX/Z7Ftp/2Cy41qsNhO6LPvSxHBaapyser5dVorF1t5M6tig==}
    hasBin: true

  '@microsoft/signalr@7.0.14':
    resolution: {integrity: sha512-dnS7gSJF5LxByZwJaj82+F1K755ya7ttPT+JnSeCBef3sL8p8FBkHePXphK8NSuOquIb7vsphXWa28A+L2SPpw==}

  '@microsoft/tsdoc-config@0.17.1':
    resolution: {integrity: sha512-UtjIFe0C6oYgTnad4q1QP4qXwLhe6tIpNTRStJ2RZEPIkqQPREAwE5spzVxsdn9UaEMUqhh0AqSx3X4nWAKXWw==}

  '@microsoft/tsdoc@0.15.1':
    resolution: {integrity: sha512-4aErSrCR/On/e5G2hDP0wjooqDdauzEbIq8hIkIe5pXV0rtWJZvdCEKL0ykZxex+IxIwBp0eGeV48hQN07dXtw==}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}

  '@polka/url@1.0.0-next.28':
    resolution: {integrity: sha512-8LduaNlMZGwdZ6qWrKlfa+2M4gahzFkprZiAt2TF8uS0qQgBizKXpXURqvTJ4WtmupWxaLqjRb2UCTe72mu+Aw==}

  '@redux-saga/core@1.3.0':
    resolution: {integrity: sha512-L+i+qIGuyWn7CIg7k1MteHGfttKPmxwZR5E7OsGikCL2LzYA0RERlaUY00Y3P3ZV2EYgrsYlBrGs6cJP5OKKqA==}

  '@redux-saga/deferred@1.2.1':
    resolution: {integrity: sha512-cmin3IuuzMdfQjA0lG4B+jX+9HdTgHZZ+6u3jRAOwGUxy77GSlTi4Qp2d6PM1PUoTmQUR5aijlA39scWWPF31g==}

  '@redux-saga/delay-p@1.2.1':
    resolution: {integrity: sha512-MdiDxZdvb1m+Y0s4/hgdcAXntpUytr9g0hpcOO1XFVyyzkrDu3SKPgBFOtHn7lhu7n24ZKIAT1qtKyQjHqRd+w==}

  '@redux-saga/is@1.1.3':
    resolution: {integrity: sha512-naXrkETG1jLRfVfhOx/ZdLj0EyAzHYbgJWkXbB3qFliPcHKiWbv/ULQryOAEKyjrhiclmr6AMdgsXFyx7/yE6Q==}

  '@redux-saga/symbols@1.1.3':
    resolution: {integrity: sha512-hCx6ZvU4QAEUojETnX8EVg4ubNLBFl1Lps4j2tX7o45x/2qg37m3c6v+kSp8xjDJY+2tJw4QB3j8o8dsl1FDXg==}

  '@redux-saga/types@1.2.1':
    resolution: {integrity: sha512-1dgmkh+3so0+LlBWRhGA33ua4MYr7tUOj+a9Si28vUi0IUFNbff1T3sgpeDJI/LaC75bBYnQ0A3wXjn0OrRNBA==}

  '@reduxjs/toolkit@1.3.2':
    resolution: {integrity: sha512-IRI9Nx6Ys/u4NDqPvUC0+e8MH+e1VME9vn30xAmd+MBqDsClc0Dhrlv4Scw2qltRy/mrINarU6BqJp4/dcyyFg==}

  '@rollup/plugin-commonjs@28.0.5':
    resolution: {integrity: sha512-lytLp2JgAMwqJY6ve3OSROXr2XuEYHjnsQN3hmnxC+w11dI91LuUw4Yc1kk2FqKXeMG8psoFejFgK+znoij0cg==}
    engines: {node: '>=16.0.0 || 14 >= 14.17'}
    peerDependencies:
      rollup: ^2.68.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/plugin-json@6.1.0':
    resolution: {integrity: sha512-EGI2te5ENk1coGeADSIwZ7G2Q8CJS2sF120T7jLw4xFw9n7wIOXHo+kIYRAoVpJAN+kmqZSoO3Fp4JtoNF4ReA==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/plugin-node-resolve@16.0.1':
    resolution: {integrity: sha512-tk5YCxJWIG81umIvNkSod2qK5KyQW19qcBF/B78n1bjtOON6gzKoVeSzAE8yHCZEDmqkHKkxplExA8KzdJLJpA==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^2.78.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/pluginutils@5.1.4':
    resolution: {integrity: sha512-USm05zrsFxYLPdWWq+K3STlWiT/3ELn3RcV5hJMghpeAIhxfsUIg6mt12CBJBInWMV4VneoV7SfGv8xIwo2qNQ==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/rollup-android-arm-eabi@4.34.4':
    resolution: {integrity: sha512-gGi5adZWvjtJU7Axs//CWaQbQd/vGy8KGcnEaCWiyCqxWYDxwIlAHFuSe6Guoxtd0SRvSfVTDMPd5H+4KE2kKA==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.34.4':
    resolution: {integrity: sha512-1aRlh1gqtF7vNPMnlf1vJKk72Yshw5zknR/ZAVh7zycRAGF2XBMVDAHmFQz/Zws5k++nux3LOq/Ejj1WrDR6xg==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.34.4':
    resolution: {integrity: sha512-drHl+4qhFj+PV/jrQ78p9ch6A0MfNVZScl/nBps5a7u01aGf/GuBRrHnRegA9bP222CBDfjYbFdjkIJ/FurvSQ==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.34.4':
    resolution: {integrity: sha512-hQqq/8QALU6t1+fbNmm6dwYsa0PDD4L5r3TpHx9dNl+aSEMnIksHZkSO3AVH+hBMvZhpumIGrTFj8XCOGuIXjw==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.34.4':
    resolution: {integrity: sha512-/L0LixBmbefkec1JTeAQJP0ETzGjFtNml2gpQXA8rpLo7Md+iXQzo9kwEgzyat5Q+OG/C//2B9Fx52UxsOXbzw==}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.34.4':
    resolution: {integrity: sha512-6Rk3PLRK+b8L/M6m/x6Mfj60LhAUcLJ34oPaxufA+CfqkUrDoUPQYFdRrhqyOvtOKXLJZJwxlOLbQjNYQcRQfw==}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.34.4':
    resolution: {integrity: sha512-kmT3x0IPRuXY/tNoABp2nDvI9EvdiS2JZsd4I9yOcLCCViKsP0gB38mVHOhluzx+SSVnM1KNn9k6osyXZhLoCA==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm-musleabihf@4.34.4':
    resolution: {integrity: sha512-3iSA9tx+4PZcJH/Wnwsvx/BY4qHpit/u2YoZoXugWVfc36/4mRkgGEoRbRV7nzNBSCOgbWMeuQ27IQWgJ7tRzw==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm64-gnu@4.34.4':
    resolution: {integrity: sha512-7CwSJW+sEhM9sESEk+pEREF2JL0BmyCro8UyTq0Kyh0nu1v0QPNY3yfLPFKChzVoUmaKj8zbdgBxUhBRR+xGxg==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-arm64-musl@4.34.4':
    resolution: {integrity: sha512-GZdafB41/4s12j8Ss2izofjeFXRAAM7sHCb+S4JsI9vaONX/zQ8cXd87B9MRU/igGAJkKvmFmJJBeeT9jJ5Cbw==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-loongarch64-gnu@4.34.4':
    resolution: {integrity: sha512-uuphLuw1X6ur11675c2twC6YxbzyLSpWggvdawTUamlsoUv81aAXRMPBC1uvQllnBGls0Qt5Siw8reSIBnbdqQ==}
    cpu: [loong64]
    os: [linux]

  '@rollup/rollup-linux-powerpc64le-gnu@4.34.4':
    resolution: {integrity: sha512-KvLEw1os2gSmD6k6QPCQMm2T9P2GYvsMZMRpMz78QpSoEevHbV/KOUbI/46/JRalhtSAYZBYLAnT9YE4i/l4vg==}
    cpu: [ppc64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-gnu@4.34.4':
    resolution: {integrity: sha512-wcpCLHGM9yv+3Dql/CI4zrY2mpQ4WFergD3c9cpRowltEh5I84pRT/EuHZsG0In4eBPPYthXnuR++HrFkeqwkA==}
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-s390x-gnu@4.34.4':
    resolution: {integrity: sha512-nLbfQp2lbJYU8obhRQusXKbuiqm4jSJteLwfjnunDT5ugBKdxqw1X9KWwk8xp1OMC6P5d0WbzxzhWoznuVK6XA==}
    cpu: [s390x]
    os: [linux]

  '@rollup/rollup-linux-x64-gnu@4.34.4':
    resolution: {integrity: sha512-JGejzEfVzqc/XNiCKZj14eb6s5w8DdWlnQ5tWUbs99kkdvfq9btxxVX97AaxiUX7xJTKFA0LwoS0KU8C2faZRg==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-linux-x64-musl@4.34.4':
    resolution: {integrity: sha512-/iFIbhzeyZZy49ozAWJ1ZR2KW6ZdYUbQXLT4O5n1cRZRoTpwExnHLjlurDXXPKEGxiAg0ujaR9JDYKljpr2fDg==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-win32-arm64-msvc@4.34.4':
    resolution: {integrity: sha512-qORc3UzoD5UUTneiP2Afg5n5Ti1GAW9Gp5vHPxzvAFFA3FBaum9WqGvYXGf+c7beFdOKNos31/41PRMUwh1tpA==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.34.4':
    resolution: {integrity: sha512-5g7E2PHNK2uvoD5bASBD9aelm44nf1w4I5FEI7MPHLWcCSrR8JragXZWgKPXk5i2FU3JFfa6CGZLw2RrGBHs2Q==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.34.4':
    resolution: {integrity: sha512-p0scwGkR4kZ242xLPBuhSckrJ734frz6v9xZzD+kHVYRAkSUmdSLCIJRfql6H5//aF8Q10K+i7q8DiPfZp0b7A==}
    cpu: [x64]
    os: [win32]

  '@rushstack/node-core-library@5.12.0':
    resolution: {integrity: sha512-QSwwzgzWoil1SCQse+yCHwlhRxNv2dX9siPnAb9zR/UmMhac4mjMrlMZpk64BlCeOFi1kJKgXRkihSwRMbboAQ==}
    peerDependencies:
      '@types/node': '*'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@rushstack/rig-package@0.5.3':
    resolution: {integrity: sha512-olzSSjYrvCNxUFZowevC3uz8gvKr3WTpHQ7BkpjtRpA3wK+T0ybep/SRUMfr195gBzJm5gaXw0ZMgjIyHqJUow==}

  '@rushstack/terminal@0.15.1':
    resolution: {integrity: sha512-3vgJYwumcjoDOXU3IxZfd616lqOdmr8Ezj4OWgJZfhmiBK4Nh7eWcv8sU8N/HdzXcuHDXCRGn/6O2Q75QvaZMA==}
    peerDependencies:
      '@types/node': '*'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@rushstack/ts-command-line@4.23.6':
    resolution: {integrity: sha512-7WepygaF3YPEoToh4MAL/mmHkiIImQq3/uAkQX46kVoKTNOOlCtFGyNnze6OYuWw2o9rxsyrHVfIBKxq/am2RA==}

  '@sentry-internal/browser-utils@9.9.0':
    resolution: {integrity: sha512-V/YhKLis98JFkqBGZaEBlDNFpJHJjoCvNb05raAYXdITfDIl37Kxqj0zX+IzyRhqnswkQ+DBTyoEoci09IR2bQ==}
    engines: {node: '>=18'}

  '@sentry-internal/feedback@9.9.0':
    resolution: {integrity: sha512-hrxuOLm0Xsnx75hTNt3eLgNNjER3egrHZShdRzlMiakfKpA9f2X10z75vlZmT5ZUygDQnp9UVUnu28cDuVb9Zw==}
    engines: {node: '>=18'}

  '@sentry-internal/replay-canvas@9.9.0':
    resolution: {integrity: sha512-YK0ixGjquahGpNsQskCEVwycdHlwNBLCx9XJr1BmGnlOw6fUCmpyVetaGg/ZyhkzKGNXAGoTa4s7FUFnAG4bKg==}
    engines: {node: '>=18'}

  '@sentry-internal/replay@9.9.0':
    resolution: {integrity: sha512-EWczKMu3qiZ0SUUWU3zkGod+AWD/VQCLiQw+tw+PEpdHbRZIdYKsEptengZCFKthrwe2QmYpVCTSRxGvujJ/6g==}
    engines: {node: '>=18'}

  '@sentry/babel-plugin-component-annotate@3.2.2':
    resolution: {integrity: sha512-D+SKQ266ra/wo87s9+UI/rKQi3qhGPCR8eSCDe0VJudhjHsqyNU+JJ5lnIGCgmZaWFTXgdBP/gdr1Iz1zqGs4Q==}
    engines: {node: '>= 14'}

  '@sentry/browser@9.9.0':
    resolution: {integrity: sha512-pIMdkOC+iggZefBs6ck5fL1mBhbLzjdw/8K99iqSeDh+lLvmlHVZajAhPlmw50xfH8CyQ1s22dhcL+zXbg3NKw==}
    engines: {node: '>=18'}

  '@sentry/bundler-plugin-core@3.2.2':
    resolution: {integrity: sha512-YGrtmqQ2jMixccX2slVG/Lw7pCGJL3DGB3clmY9mO8QBEBIN3/gEANiHJVWwRidpUOS/0b7yVVGAdwZ87oPwTg==}
    engines: {node: '>= 14'}

  '@sentry/cli-darwin@2.42.2':
    resolution: {integrity: sha512-GtJSuxER7Vrp1IpxdUyRZzcckzMnb4N5KTW7sbTwUiwqARRo+wxS+gczYrS8tdgtmXs5XYhzhs+t4d52ITHMIg==}
    engines: {node: '>=10'}
    os: [darwin]

  '@sentry/cli-linux-arm64@2.42.2':
    resolution: {integrity: sha512-BOxzI7sgEU5Dhq3o4SblFXdE9zScpz6EXc5Zwr1UDZvzgXZGosUtKVc7d1LmkrHP8Q2o18HcDWtF3WvJRb5Zpw==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux, freebsd]

  '@sentry/cli-linux-arm@2.42.2':
    resolution: {integrity: sha512-7udCw+YL9lwq+9eL3WLspvnuG+k5Icg92YE7zsteTzWLwgPVzaxeZD2f8hwhsu+wmL+jNqbpCRmktPteh3i2mg==}
    engines: {node: '>=10'}
    cpu: [arm]
    os: [linux, freebsd]

  '@sentry/cli-linux-i686@2.42.2':
    resolution: {integrity: sha512-Sw/dQp5ZPvKnq3/y7wIJyxTUJYPGoTX/YeMbDs8BzDlu9to2LWV3K3r7hE7W1Lpbaw4tSquUHiQjP5QHCOS7aQ==}
    engines: {node: '>=10'}
    cpu: [x86, ia32]
    os: [linux, freebsd]

  '@sentry/cli-linux-x64@2.42.2':
    resolution: {integrity: sha512-mU4zUspAal6TIwlNLBV5oq6yYqiENnCWSxtSQVzWs0Jyq97wtqGNG9U+QrnwjJZ+ta/hvye9fvL2X25D/RxHQw==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux, freebsd]

  '@sentry/cli-win32-i686@2.42.2':
    resolution: {integrity: sha512-iHvFHPGqgJMNqXJoQpqttfsv2GI3cGodeTq4aoVLU/BT3+hXzbV0x1VpvvEhncJkDgDicJpFLM8sEPHb3b8abw==}
    engines: {node: '>=10'}
    cpu: [x86, ia32]
    os: [win32]

  '@sentry/cli-win32-x64@2.42.2':
    resolution: {integrity: sha512-vPPGHjYoaGmfrU7xhfFxG7qlTBacroz5NdT+0FmDn6692D8IvpNXl1K+eV3Kag44ipJBBeR8g1HRJyx/F/9ACw==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [win32]

  '@sentry/cli@2.42.2':
    resolution: {integrity: sha512-spb7S/RUumCGyiSTg8DlrCX4bivCNmU/A1hcfkwuciTFGu8l5CDc2I6jJWWZw8/0enDGxuj5XujgXvU5tr4bxg==}
    engines: {node: '>= 10'}
    hasBin: true

  '@sentry/core@9.9.0':
    resolution: {integrity: sha512-GxKvx8PSgoWhLLS+/WBGIXy7rsFcnJBPDqFXIfcAGy89k2j06d9IP0kiIc63qBGStSUkh5FFJLPTakZ5RXiFXA==}
    engines: {node: '>=18'}

  '@sentry/vite-plugin@3.2.2':
    resolution: {integrity: sha512-WSkHOhZszMrIE9zmx2l4JhMnMlZmN/yAoHyf59pwFLIMctuZak6lNPbTbIFkFHDzIJ9Nut5RAVsw1qjmWc1PTA==}
    engines: {node: '>= 14'}

  '@sinclair/typebox@0.27.8':
    resolution: {integrity: sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==}

  '@skeletonlabs/skeleton-svelte@1.0.0':
    resolution: {integrity: sha512-nMk7HCVFy+DAKMHB0x7T0LdUJgCOMMTHjiFUXDP3btwpxmen60qIHfFdBy/7uYyqCi2IR0vSIzE1MtSrqmzJ+g==}
    peerDependencies:
      '@skeletonlabs/skeleton': ^3.0.0
      svelte: ^5.20.0

  '@skeletonlabs/skeleton@3.1.1':
    resolution: {integrity: sha512-qAf+qxfgj6sSySYG5WeBEKpaAAYQyC0qrD9wu+Myai5CUhSctvey7gq6aJlMWSHjte4wcRC+2f/tOzdQfPWOlA==}
    peerDependencies:
      tailwindcss: ^4.0.0

  '@sveltejs/acorn-typescript@1.0.5':
    resolution: {integrity: sha512-IwQk4yfwLdibDlrXVE04jTZYlLnwsTT2PIOQQGNLWfjavGifnk1JD1LcZjZaBTRcxZu2FfPfNLOE04DSu9lqtQ==}
    peerDependencies:
      acorn: ^8.9.0

  '@sveltejs/adapter-node@5.2.12':
    resolution: {integrity: sha512-0bp4Yb3jKIEcZWVcJC/L1xXp9zzJS4hDwfb4VITAkfT4OVdkspSHsx7YhqJDbb2hgLl6R9Vs7VQR+fqIVOxPUQ==}
    peerDependencies:
      '@sveltejs/kit': ^2.4.0

  '@sveltejs/adapter-static@3.0.8':
    resolution: {integrity: sha512-YaDrquRpZwfcXbnlDsSrBQNCChVOT9MGuSg+dMAyfsAa1SmiAhrA5jUYUiIMC59G92kIbY/AaQOWcBdq+lh+zg==}
    peerDependencies:
      '@sveltejs/kit': ^2.0.0

  '@sveltejs/kit@2.21.1':
    resolution: {integrity: sha512-vLbtVwtDcK8LhJKnFkFYwM0uCdFmzioQnif0bjEYH1I24Arz22JPr/hLUiXGVYAwhu8INKx5qrdvr4tHgPwX6w==}
    engines: {node: '>=18.13'}
    hasBin: true
    peerDependencies:
      '@sveltejs/vite-plugin-svelte': ^3.0.0 || ^4.0.0-next.1 || ^5.0.0
      svelte: ^4.0.0 || ^5.0.0-next.0
      vite: ^5.0.3 || ^6.0.0

  '@sveltejs/vite-plugin-svelte-inspector@4.0.1':
    resolution: {integrity: sha512-J/Nmb2Q2y7mck2hyCX4ckVHcR5tu2J+MtBEQqpDrrgELZ2uvraQcK/ioCV61AqkdXFgriksOKIceDcQmqnGhVw==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22}
    peerDependencies:
      '@sveltejs/vite-plugin-svelte': ^5.0.0
      svelte: ^5.0.0
      vite: ^6.0.0

  '@sveltejs/vite-plugin-svelte@5.0.3':
    resolution: {integrity: sha512-MCFS6CrQDu1yGwspm4qtli0e63vaPCehf6V7pIMP15AsWgMKrqDGCPFF/0kn4SP0ii4aySu4Pa62+fIRGFMjgw==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22}
    peerDependencies:
      svelte: ^5.0.0
      vite: ^6.0.0

  '@swc/core-darwin-arm64@1.7.26':
    resolution: {integrity: sha512-FF3CRYTg6a7ZVW4yT9mesxoVVZTrcSWtmZhxKCYJX9brH4CS/7PRPjAKNk6kzWgWuRoglP7hkjQcd6EpMcZEAw==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [darwin]

  '@swc/core-darwin-x64@1.7.26':
    resolution: {integrity: sha512-az3cibZdsay2HNKmc4bjf62QVukuiMRh5sfM5kHR/JMTrLyS6vSw7Ihs3UTkZjUxkLTT8ro54LI6sV6sUQUbLQ==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [darwin]

  '@swc/core-linux-arm-gnueabihf@1.7.26':
    resolution: {integrity: sha512-VYPFVJDO5zT5U3RpCdHE5v1gz4mmR8BfHecUZTmD2v1JeFY6fv9KArJUpjrHEEsjK/ucXkQFmJ0jaiWXmpOV9Q==}
    engines: {node: '>=10'}
    cpu: [arm]
    os: [linux]

  '@swc/core-linux-arm64-gnu@1.7.26':
    resolution: {integrity: sha512-YKevOV7abpjcAzXrhsl+W48Z9mZvgoVs2eP5nY+uoMAdP2b3GxC0Df1Co0I90o2lkzO4jYBpTMcZlmUXLdXn+Q==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]

  '@swc/core-linux-arm64-musl@1.7.26':
    resolution: {integrity: sha512-3w8iZICMkQQON0uIcvz7+Q1MPOW6hJ4O5ETjA0LSP/tuKqx30hIniCGOgPDnv3UTMruLUnQbtBwVCZTBKR3Rkg==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]

  '@swc/core-linux-x64-gnu@1.7.26':
    resolution: {integrity: sha512-c+pp9Zkk2lqb06bNGkR2Looxrs7FtGDMA4/aHjZcCqATgp348hOKH5WPvNLBl+yPrISuWjbKDVn3NgAvfvpH4w==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]

  '@swc/core-linux-x64-musl@1.7.26':
    resolution: {integrity: sha512-PgtyfHBF6xG87dUSSdTJHwZ3/8vWZfNIXQV2GlwEpslrOkGqy+WaiiyE7Of7z9AvDILfBBBcJvJ/r8u980wAfQ==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]

  '@swc/core-win32-arm64-msvc@1.7.26':
    resolution: {integrity: sha512-9TNXPIJqFynlAOrRD6tUQjMq7KApSklK3R/tXgIxc7Qx+lWu8hlDQ/kVPLpU7PWvMMwC/3hKBW+p5f+Tms1hmA==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [win32]

  '@swc/core-win32-ia32-msvc@1.7.26':
    resolution: {integrity: sha512-9YngxNcG3177GYdsTum4V98Re+TlCeJEP4kEwEg9EagT5s3YejYdKwVAkAsJszzkXuyRDdnHUpYbTrPG6FiXrQ==}
    engines: {node: '>=10'}
    cpu: [ia32]
    os: [win32]

  '@swc/core-win32-x64-msvc@1.7.26':
    resolution: {integrity: sha512-VR+hzg9XqucgLjXxA13MtV5O3C0bK0ywtLIBw/+a+O+Oc6mxFWHtdUeXDbIi5AiPbn0fjgVJMqYnyjGyyX8u0w==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [win32]

  '@swc/core@1.7.26':
    resolution: {integrity: sha512-f5uYFf+TmMQyYIoxkn/evWhNGuUzC730dFwAKGwBVHHVoPyak1/GvJUm6i1SKl+2Hrj9oN0i3WSoWWZ4pgI8lw==}
    engines: {node: '>=10'}
    peerDependencies:
      '@swc/helpers': '*'
    peerDependenciesMeta:
      '@swc/helpers':
        optional: true

  '@swc/counter@0.1.3':
    resolution: {integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==}

  '@swc/helpers@0.5.17':
    resolution: {integrity: sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==}

  '@swc/types@0.1.12':
    resolution: {integrity: sha512-wBJA+SdtkbFhHjTMYH+dEH1y4VpfGdAc2Kw/LK09i9bXd/K6j6PkDcFCEzb6iVfZMkPRrl/q0e3toqTAJdkIVA==}

  '@tailwindcss/forms@0.5.10':
    resolution: {integrity: sha512-utI1ONF6uf/pPNO68kmN1b8rEwNXv3czukalo8VtJH8ksIkZXr3Q3VYudZLkCsDd4Wku120uF02hYK25XGPorw==}
    peerDependencies:
      tailwindcss: '>=3.0.0 || >= 3.0.0-alpha.1 || >= 4.0.0-alpha.20 || >= 4.0.0-beta.1'

  '@tailwindcss/node@4.0.9':
    resolution: {integrity: sha512-tOJvdI7XfJbARYhxX+0RArAhmuDcczTC46DGCEziqxzzbIaPnfYaIyRT31n4u8lROrsO7Q6u/K9bmQHL2uL1bQ==}

  '@tailwindcss/oxide-android-arm64@4.0.9':
    resolution: {integrity: sha512-YBgy6+2flE/8dbtrdotVInhMVIxnHJPbAwa7U1gX4l2ThUIaPUp18LjB9wEH8wAGMBZUb//SzLtdXXNBHPUl6Q==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]

  '@tailwindcss/oxide-darwin-arm64@4.0.9':
    resolution: {integrity: sha512-pWdl4J2dIHXALgy2jVkwKBmtEb73kqIfMpYmcgESr7oPQ+lbcQ4+tlPeVXaSAmang+vglAfFpXQCOvs/aGSqlw==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@tailwindcss/oxide-darwin-x64@4.0.9':
    resolution: {integrity: sha512-4Dq3lKp0/C7vrRSkNPtBGVebEyWt9QPPlQctxJ0H3MDyiQYvzVYf8jKow7h5QkWNe8hbatEqljMj/Y0M+ERYJg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@tailwindcss/oxide-freebsd-x64@4.0.9':
    resolution: {integrity: sha512-k7U1RwRODta8x0uealtVt3RoWAWqA+D5FAOsvVGpYoI6ObgmnzqWW6pnVwz70tL8UZ/QXjeMyiICXyjzB6OGtQ==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [freebsd]

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.0.9':
    resolution: {integrity: sha512-NDDjVweHz2zo4j+oS8y3KwKL5wGCZoXGA9ruJM982uVJLdsF8/1AeKvUwKRlMBpxHt1EdWJSAh8a0Mfhl28GlQ==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@tailwindcss/oxide-linux-arm64-gnu@4.0.9':
    resolution: {integrity: sha512-jk90UZ0jzJl3Dy1BhuFfRZ2KP9wVKMXPjmCtY4U6fF2LvrjP5gWFJj5VHzfzHonJexjrGe1lMzgtjriuZkxagg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@tailwindcss/oxide-linux-arm64-musl@4.0.9':
    resolution: {integrity: sha512-3eMjyTC6HBxh9nRgOHzrc96PYh1/jWOwHZ3Kk0JN0Kl25BJ80Lj9HEvvwVDNTgPg154LdICwuFLuhfgH9DULmg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@tailwindcss/oxide-linux-x64-gnu@4.0.9':
    resolution: {integrity: sha512-v0D8WqI/c3WpWH1kq/HP0J899ATLdGZmENa2/emmNjubT0sWtEke9W9+wXeEoACuGAhF9i3PO5MeyditpDCiWQ==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@tailwindcss/oxide-linux-x64-musl@4.0.9':
    resolution: {integrity: sha512-Kvp0TCkfeXyeehqLJr7otsc4hd/BUPfcIGrQiwsTVCfaMfjQZCG7DjI+9/QqPZha8YapLA9UoIcUILRYO7NE1Q==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@tailwindcss/oxide-win32-arm64-msvc@4.0.9':
    resolution: {integrity: sha512-m3+60T/7YvWekajNq/eexjhV8z10rswcz4BC9bioJ7YaN+7K8W2AmLmG0B79H14m6UHE571qB0XsPus4n0QVgQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@tailwindcss/oxide-win32-x64-msvc@4.0.9':
    resolution: {integrity: sha512-dpc05mSlqkwVNOUjGu/ZXd5U1XNch1kHFJ4/cHkZFvaW1RzbHmRt24gvM8/HC6IirMxNarzVw4IXVtvrOoZtxA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@tailwindcss/oxide@4.0.9':
    resolution: {integrity: sha512-eLizHmXFqHswJONwfqi/WZjtmWZpIalpvMlNhTM99/bkHtUs6IqgI1XQ0/W5eO2HiRQcIlXUogI2ycvKhVLNcA==}
    engines: {node: '>= 10'}

  '@tailwindcss/typography@0.5.16':
    resolution: {integrity: sha512-0wDLwCVF5V3x3b1SGXPCDcdsbDHMBe+lkFzBRaHeLvNi+nrrnZ1lA18u+OTWO8iSWU2GxUOCvlXtDuqftc1oiA==}
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders || >=4.0.0-alpha.20 || >=4.0.0-beta.1'

  '@tailwindcss/vite@4.0.9':
    resolution: {integrity: sha512-BIKJO+hwdIsN7V6I7SziMZIVHWWMsV/uCQKYEbeiGRDRld+TkqyRRl9+dQ0MCXbhcVr+D9T/qX2E84kT7V281g==}
    peerDependencies:
      vite: ^5.2.0 || ^6

  '@testing-library/dom@10.4.0':
    resolution: {integrity: sha512-pemlzrSESWbdAloYml3bAJMEfNh1Z7EduzqPKprCH5S341frlpYnUEW0H72dLxa6IsYr+mPno20GiSm+h9dEdQ==}
    engines: {node: '>=18'}

  '@testing-library/dom@7.26.7':
    resolution: {integrity: sha512-Ykb+W02Q5lAAuwwANq/LCxMqrLKF7S+udJ0tFDwyuHyr84kQ2YKeYdIl5B3HnLKSWK4mGqA+Xe7dbU25DXhlkQ==}
    engines: {node: '>=10'}

  '@testing-library/jest-dom@6.6.3':
    resolution: {integrity: sha512-IteBhl4XqYNkM54f4ejhLRJiZNqcSCoXUOG2CPK7qbD322KjQozM4kHQOfkG2oln9b9HTYqs+Sae8vBATubxxA==}
    engines: {node: '>=14', npm: '>=6', yarn: '>=1'}

  '@testing-library/react-hooks@3.4.2':
    resolution: {integrity: sha512-RfPG0ckOzUIVeIqlOc1YztKgFW+ON8Y5xaSPbiBkfj9nMkkiLhLeBXT5icfPX65oJV/zCZu4z8EVnUc6GY9C5A==}
    peerDependencies:
      react: '>=16.9.0'
      react-test-renderer: '>=16.9.0'

  '@testing-library/react@10.0.1':
    resolution: {integrity: sha512-sMHWud2dcymOzq2AhEniICSijEwKeTiBX+K0y36FYNY7wH2t0SIP1o732Bf5dDY0jYoMC2hj2UJSVpZC/rDsWg==}
    engines: {node: '>=10'}
    peerDependencies:
      react: '*'
      react-dom: '*'

  '@testing-library/svelte@5.2.7':
    resolution: {integrity: sha512-aGhUaFmEXEVost4QOsbHUUbHLwi7ZZRRxAHFDO2Cmr0BZD3/3+XvaYEPq70Rdw0NRNjdqZHdARBEcrCOkPuAqw==}
    engines: {node: '>= 10'}
    peerDependencies:
      svelte: ^3 || ^4 || ^5 || ^5.0.0-next.0
      vite: '*'
      vitest: '*'
    peerDependenciesMeta:
      vite:
        optional: true
      vitest:
        optional: true

  '@testing-library/user-event@12.2.2':
    resolution: {integrity: sha512-mTYL9LrwiSeyorStUOMuRGQDn1ca40tIhuv//o/K3lY8wBEp+9Im90MFVx5i3u7zCPmavn3uWZs/10chsbI8Tg==}
    engines: {node: '>=10', npm: '>=6'}
    peerDependencies:
      '@testing-library/dom': '>=7.21.4'

  '@types/argparse@1.0.38':
    resolution: {integrity: sha512-ebDJ9b0e702Yr7pWgB0jzm+CX4Srzz8RcXtLJDJB+BSccqMa36uyH/zUsSYao5+BD1ytv3k3rPYCq4mAE1hsXA==}

  '@types/aria-query@4.2.2':
    resolution: {integrity: sha512-HnYpAE1Y6kRyKM/XkEuiRQhTHvkzMBurTHnpFLYLBGPIylZNPs9jJcuOOYWxPLJCSEtmZT0Y8rHDokKN7rRTig==}

  '@types/aria-query@5.0.4':
    resolution: {integrity: sha512-rfT93uj5s0PRL7EzccGMs3brplhcrghnDoV26NqKhCAS1hVo+WdNsPvE/yb6ilfr5hi2MEk6d5EWJTKdxg8jVw==}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==}

  '@types/babel__generator@7.6.8':
    resolution: {integrity: sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==}

  '@types/babel__traverse@7.20.6':
    resolution: {integrity: sha512-r1bzfrm0tomOI8g1SzvCaQHo6Lcv6zu0EA+W2kHrt8dyrHQxGzBBL4kdkzIS+jBMV+EYcMAEAqXqYaLJq5rOZg==}

  '@types/cookie@0.6.0':
    resolution: {integrity: sha512-4Kh9a6B2bQciAhf7FSuMRRkUWecJgJu9nPnx3yzpsfXX/c50REIqpHY4C82bXP90qrLtXtkDxTZosYO3UpOwlA==}

  '@types/estree@1.0.6':
    resolution: {integrity: sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==}

  '@types/fs-extra@9.0.13':
    resolution: {integrity: sha512-nEnwB++1u5lVDM2UI4c1+5R+FYaKfaAzS4OococimjVm3nQw3TuzH5UNsocrcTBbhnerblyHj4A49qXbIiZdpA==}

  '@types/hoist-non-react-statics@3.3.5':
    resolution: {integrity: sha512-SbcrWzkKBw2cdwRTwQAswfpB9g9LJWfjtUeW/jvNwbhC8cpmmNYVePa+ncbUe0rGTQ7G3Ff6mYUN2VMfLVr+Sg==}

  '@types/istanbul-lib-coverage@2.0.6':
    resolution: {integrity: sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==}

  '@types/istanbul-lib-report@3.0.3':
    resolution: {integrity: sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==}

  '@types/istanbul-reports@1.1.2':
    resolution: {integrity: sha512-P/W9yOX/3oPZSpaYOCQzGqgCQRXn0FFO/V8bWrCQs+wLmvVVxk6CRBXALEvNs9OHIatlnlFokfhuDo2ug01ciw==}

  '@types/istanbul-reports@3.0.4':
    resolution: {integrity: sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==}

  '@types/jest@25.1.4':
    resolution: {integrity: sha512-QDDY2uNAhCV7TMCITrxz+MRk1EizcsevzfeS6LykIlq2V1E5oO4wXG8V2ZEd9w7Snxeeagk46YbMgZ8ESHx3sw==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/lodash@4.17.15':
    resolution: {integrity: sha512-w/P33JFeySuhN6JLkysYUK2gEmy9kHHFN7E8ro0tkfmlDOgxBDzWEZ/J8cWA+fHqFevpswDTFZnDx+R9lbL6xw==}

  '@types/minimist@1.2.5':
    resolution: {integrity: sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==}

  '@types/node@20.14.8':
    resolution: {integrity: sha512-DO+2/jZinXfROG7j7WKFn/3C6nFwxy2lLpgLjEXJz+0XKphZlTLJ14mo8Vfg8X5BWN6XjyESXq+LcYdT7tR3bA==}

  '@types/normalize-package-data@2.4.4':
    resolution: {integrity: sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==}

  '@types/parse-json@4.0.2':
    resolution: {integrity: sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==}

  '@types/prop-types@15.7.12':
    resolution: {integrity: sha512-5zvhXYtRNRluoE/jAp4GVsSduVUzNWKkOZrCDBWYtE7biZywwdC2AcEzg+cSMLFRfVgeAFqpfNabiPjxFddV1Q==}

  '@types/react-dom@16.9.3':
    resolution: {integrity: sha512-FUuZKXPr9qlzUT9lhuzrZgLjH63TvNn28Ch3MvKG4B+F52zQtO8DtE0Opbncy3xaucNZM2WIPfuNTgkbKx5Brg==}

  '@types/react-redux@7.1.7':
    resolution: {integrity: sha512-U+WrzeFfI83+evZE2dkZ/oF/1vjIYgqrb5dGgedkqVV8HEfDFujNgWCwHL89TDuWKb47U0nTBT6PLGq4IIogWg==}

  '@types/react-test-renderer@16.9.2':
    resolution: {integrity: sha512-4eJr1JFLIAlWhzDkBCkhrOIWOvOxcCAfQh+jiKg7l/nNZcCIL2MHl2dZhogIFKyHzedVWHaVP1Yydq/Ruu4agw==}

  '@types/react@16.9.25':
    resolution: {integrity: sha512-Dlj2V72cfYLPNscIG3/SMUOzhzj7GK3bpSrfefwt2YT9GLynvLCCZjbhyF6VsT0q0+aRACRX03TDJGb7cA0cqg==}

  '@types/resolve@1.20.2':
    resolution: {integrity: sha512-60BCwRFOZCQhDncwQdxxeOEEkbc5dIMccYLwbxsS4TUNeVECQ/pBJ0j09mrHOl/JJvpRPGwO9SvE4nR2Nb/a4Q==}

  '@types/schema-utils@2.4.0':
    resolution: {integrity: sha512-454hrj5gz/FXcUE20ygfEiN4DxZ1sprUo0V1gqIqkNZ/CzoEzAZEll2uxMsuyz6BYjiQan4Aa65xbTemfzW9hQ==}
    deprecated: This is a stub types definition. schema-utils provides its own type definitions, so you do not need this installed.

  '@types/testing-library__dom@7.5.0':
    resolution: {integrity: sha512-mj1aH4cj3XUpMEgVpognma5kHVtbm6U6cHZmEFzCRiXPvKkuHrFr3+yXdGLXvfFRBaQIVshPGHI+hGTOJlhS/g==}
    deprecated: This is a stub types definition. testing-library__dom provides its own type definitions, so you do not need this installed.

  '@types/testing-library__jest-dom@5.14.0':
    resolution: {integrity: sha512-l2P2GO+hFF4Liye+fAajT1qBqvZOiL79YMpEvgGs1xTK7hECxBI8Wz4J7ntACJNiJ9r0vXQqYovroXRLPDja6A==}

  '@types/testing-library__react-hooks@3.4.1':
    resolution: {integrity: sha512-G4JdzEcq61fUyV6wVW9ebHWEiLK2iQvaBuCHHn9eMSbZzVh4Z4wHnUGIvQOYCCYeu5DnUtFyNYuAAgbSaO/43Q==}

  '@types/testing-library__react@10.0.1':
    resolution: {integrity: sha512-RbDwmActAckbujLZeVO/daSfdL1pnjVqas25UueOkAY5r7vriavWf0Zqg7ghXMHa8ycD/kLkv8QOj31LmSYwww==}

  '@types/testing-library__react@9.1.3':
    resolution: {integrity: sha512-iCdNPKU3IsYwRK9JieSYAiX0+aYDXOGAmrC/3/M7AqqSDKnWWVv07X+Zk1uFSL7cMTUYzv4lQRfohucEocn5/w==}

  '@types/yargs-parser@21.0.3':
    resolution: {integrity: sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==}

  '@types/yargs@15.0.19':
    resolution: {integrity: sha512-2XUaGVmyQjgyAZldf0D0c14vvo/yv0MhQBSTJcejMMaitsn3nxCB6TmH4G0ZQf+uxROOa9mpanoSm8h6SG/1ZA==}

  '@types/yup@0.29.1':
    resolution: {integrity: sha512-QGerem3F2hYdk4PTYGo8fzToCn86edFbwCTKfwYtxSFebb6UpPj0aP6+eTvk8CrswnVQjoBNKmVPm6b74gQxLg==}

  '@typescript-eslint/eslint-plugin@8.23.0':
    resolution: {integrity: sha512-vBz65tJgRrA1Q5gWlRfvoH+w943dq9K1p1yDBY2pc+a1nbBLZp7fB9+Hk8DaALUbzjqlMfgaqlVPT1REJdkt/w==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.0.0 || ^8.0.0-alpha.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/parser@8.23.0':
    resolution: {integrity: sha512-h2lUByouOXFAlMec2mILeELUbME5SZRN/7R9Cw2RD2lRQQY08MWMM+PmVVKKJNK1aIwqTo9t/0CvOxwPbRIE2Q==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/scope-manager@8.23.0':
    resolution: {integrity: sha512-OGqo7+dXHqI7Hfm+WqkZjKjsiRtFUQHPdGMXzk5mYXhJUedO7e/Y7i8AK3MyLMgZR93TX4bIzYrfyVjLC+0VSw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/type-utils@8.23.0':
    resolution: {integrity: sha512-iIuLdYpQWZKbiH+RkCGc6iu+VwscP5rCtQ1lyQ7TYuKLrcZoeJVpcLiG8DliXVkUxirW/PWlmS+d6yD51L9jvA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/types@8.23.0':
    resolution: {integrity: sha512-1sK4ILJbCmZOTt9k4vkoulT6/y5CHJ1qUYxqpF1K/DBAd8+ZUL4LlSCxOssuH5m4rUaaN0uS0HlVPvd45zjduQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/typescript-estree@8.23.0':
    resolution: {integrity: sha512-LcqzfipsB8RTvH8FX24W4UUFk1bl+0yTOf9ZA08XngFwMg4Kj8A+9hwz8Cr/ZS4KwHrmo9PJiLZkOt49vPnuvQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/utils@8.23.0':
    resolution: {integrity: sha512-uB/+PSo6Exu02b5ZEiVtmY6RVYO7YU5xqgzTIVZwTHvvK3HsL8tZZHFaTLFtRG3CsV4A5mhOv+NZx5BlhXPyIA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  '@typescript-eslint/visitor-keys@8.23.0':
    resolution: {integrity: sha512-oWWhcWDLwDfu++BGTZcmXWqpwtkwb5o7fxUIGksMQQDSdPW9prsSnfIOZMlsj4vBOSrcnjIUZMiIjODgGosFhQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@vitejs/plugin-react-swc@3.7.2':
    resolution: {integrity: sha512-y0byko2b2tSVVf5Gpng1eEhX1OvPC7x8yns1Fx8jDzlJp4LS6CMkCPfLw47cjyoMrshQDoQw4qcgjsU9VvlCew==}
    peerDependencies:
      vite: ^4 || ^5 || ^6

  '@vitest/coverage-v8@3.0.5':
    resolution: {integrity: sha512-zOOWIsj5fHh3jjGwQg+P+J1FW3s4jBu1Zqga0qW60yutsBtqEqNEJKWYh7cYn1yGD+1bdPsPdC/eL4eVK56xMg==}
    peerDependencies:
      '@vitest/browser': 3.0.5
      vitest: 3.0.5
    peerDependenciesMeta:
      '@vitest/browser':
        optional: true

  '@vitest/expect@3.0.5':
    resolution: {integrity: sha512-nNIOqupgZ4v5jWuQx2DSlHLEs7Q4Oh/7AYwNyE+k0UQzG7tSmjPXShUikn1mpNGzYEN2jJbTvLejwShMitovBA==}

  '@vitest/mocker@3.0.5':
    resolution: {integrity: sha512-CLPNBFBIE7x6aEGbIjaQAX03ZZlBMaWwAjBdMkIf/cAn6xzLTiM3zYqO/WAbieEjsAZir6tO71mzeHZoodThvw==}
    peerDependencies:
      msw: ^2.4.9
      vite: ^5.0.0 || ^6.0.0
    peerDependenciesMeta:
      msw:
        optional: true
      vite:
        optional: true

  '@vitest/pretty-format@3.0.5':
    resolution: {integrity: sha512-CjUtdmpOcm4RVtB+up8r2vVDLR16Mgm/bYdkGFe3Yj/scRfCpbSi2W/BDSDcFK7ohw8UXvjMbOp9H4fByd/cOA==}

  '@vitest/runner@3.0.5':
    resolution: {integrity: sha512-BAiZFityFexZQi2yN4OX3OkJC6scwRo8EhRB0Z5HIGGgd2q+Nq29LgHU/+ovCtd0fOfXj5ZI6pwdlUmC5bpi8A==}

  '@vitest/snapshot@3.0.5':
    resolution: {integrity: sha512-GJPZYcd7v8QNUJ7vRvLDmRwl+a1fGg4T/54lZXe+UOGy47F9yUfE18hRCtXL5aHN/AONu29NGzIXSVFh9K0feA==}

  '@vitest/spy@3.0.5':
    resolution: {integrity: sha512-5fOzHj0WbUNqPK6blI/8VzZdkBlQLnT25knX0r4dbZI9qoZDf3qAdjoMmDcLG5A83W6oUUFJgUd0EYBc2P5xqg==}

  '@vitest/utils@3.0.5':
    resolution: {integrity: sha512-N9AX0NUoUtVwKwy21JtwzaqR5L5R5A99GAbrHfCCXK1lp593i/3AZAXhSP43wRQuxYsflrdzEfXZFo1reR1Nkg==}

  '@volar/language-core@2.4.12':
    resolution: {integrity: sha512-RLrFdXEaQBWfSnYGVxvR2WrO6Bub0unkdHYIdC31HzIEqATIuuhRRzYu76iGPZ6OtA4Au1SnW0ZwIqPP217YhA==}

  '@volar/source-map@2.4.12':
    resolution: {integrity: sha512-bUFIKvn2U0AWojOaqf63ER0N/iHIBYZPpNGogfLPQ68F5Eet6FnLlyho7BS0y2HJ1jFhSif7AcuTx1TqsCzRzw==}

  '@volar/typescript@2.4.12':
    resolution: {integrity: sha512-HJB73OTJDgPc80K30wxi3if4fSsZZAOScbj2fcicMuOPoOkcf9NNAINb33o+DzhBdF9xTKC1gnPmIRDous5S0g==}

  '@vue/compiler-core@3.5.13':
    resolution: {integrity: sha512-oOdAkwqUfW1WqpwSYJce06wvt6HljgY3fGeM9NcVA1HaYOij3mZG9Rkysn0OHuyUAGMbEbARIpsG+LPVlBJ5/Q==}

  '@vue/compiler-dom@3.5.13':
    resolution: {integrity: sha512-ZOJ46sMOKUjO3e94wPdCzQ6P1Lx/vhp2RSvfaab88Ajexs0AHeV0uasYhi99WPaogmBlRHNRuly8xV75cNTMDA==}

  '@vue/compiler-vue2@2.7.16':
    resolution: {integrity: sha512-qYC3Psj9S/mfu9uVi5WvNZIzq+xnXMhOwbTFKKDD7b1lhpnn71jXSFdTQ+WsIEk0ONCd7VV2IMm7ONl6tbQ86A==}

  '@vue/language-core@2.2.0':
    resolution: {integrity: sha512-O1ZZFaaBGkKbsRfnVH1ifOK1/1BUkyK+3SQsfnh6PmMmD4qJcTU8godCeA96jjDRTL6zgnK7YzCHfaUlH2r0Mw==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@vue/shared@3.5.13':
    resolution: {integrity: sha512-/hnE/qP5ZoGpol0a5mDi45bOd7t3tjYJBjsgCsivow7D48cJeV5l05RD82lPqi7gRiphZM37rnhW1l6ZoCNNnQ==}

  '@zag-js/accordion@1.7.0':
    resolution: {integrity: sha512-LNJOjLTW2KwrToXBrXIbNIAiISA94n0AdWp14H8RrskdokywmEGiC0GgWTGEJ7DNA6TGP6Ae5o9rJ4fHSmCsDQ==}

  '@zag-js/anatomy@1.7.0':
    resolution: {integrity: sha512-fkRgH6vPCwykmRdV38uAJeTtJc8tayAnURfoovHAtB9bK0goagPbpdcYTNyGn8msul0h+KBloOtnw4obvX0nPw==}

  '@zag-js/aria-hidden@1.7.0':
    resolution: {integrity: sha512-YNbACFZoqw/1JymxCZXtuAFdeYZm7sK3E0jv3bPbqytPj7TziLa1dRDWDdx8cPcu0B4n4WrBMBSCGUjj/nWDCA==}

  '@zag-js/auto-resize@1.7.0':
    resolution: {integrity: sha512-ifWflzZc1fNJ+XUZaYpB220AiAr4l3Eczq8ELwj/ugg7T/10Wo0FkxTCVmCZfIiCMoqHuh/2oTX3PCTIwg6uxg==}

  '@zag-js/avatar@1.7.0':
    resolution: {integrity: sha512-vzMCMpYIM2BIvPvK34VaRMUsUSpg3jwoxCzA31k+QrCmjm3ti8pLoT4waE01XHiaQwNPcTFbMWUi/nIQQKG14A==}

  '@zag-js/collection@1.7.0':
    resolution: {integrity: sha512-gH7I03ag2niEhCVgNpXBYybnIROGXmAkX+5e1rYQ60mOh2oQnK+5k9k3DRkca5rAKbu4uT6JjYFwnY9sA/NZfA==}

  '@zag-js/combobox@1.7.0':
    resolution: {integrity: sha512-kaMvGoBZwiFC9KaUbHXNFkneg7grZmJlteVxk6kJXYd7JGDHhhYsFznPNIC0apvBCIEqwyBGVB/lCjK+BseZtw==}

  '@zag-js/core@1.7.0':
    resolution: {integrity: sha512-FyK1POPqgBp7DBpUIwvmBQH16+L52NaTaQJzg8iTI9mI/4m3AxZ5aN+8a8qzwGIkVI6rlDcrBkmuOcHDVIOEGA==}

  '@zag-js/dialog@1.7.0':
    resolution: {integrity: sha512-gx/CtKsPg/Y+2d+HtP3tjEdl7KM+x6lUDttjDDBn9rvXFs2REW69AlcJtRzs6B22CxDPmxssGPr1oi3zaU1AUA==}

  '@zag-js/dismissable@1.7.0':
    resolution: {integrity: sha512-o6S++e7iaBmizIgsvLt5RwY7gn2OQGeG2etet+oaUAMtNhi/1+uGG+rTZgOMj/MGg9BYpPld5tXfk/RrlShh9Q==}

  '@zag-js/dom-query@1.7.0':
    resolution: {integrity: sha512-cj+mKB7Sj7mqAepHMsbV4bGvDJfUYCt4d4ruYw0dVpDa1Z9N38TtztTznfrm9kuqOYcJkgE0q3Rn/kPLi8rK8g==}

  '@zag-js/element-rect@1.7.0':
    resolution: {integrity: sha512-j0h1+DASUI5urwBCELdjfk4oekLQ0D2v3a1wQJopGh+ITRVAC1gE1YFx3O+vnP2HwqANxG4+RQHwoQBM2bMBCQ==}

  '@zag-js/element-size@1.7.0':
    resolution: {integrity: sha512-Nq+HxG64Ts1QvaJPeDuy8zo/RqcbE95RPNVuHBwuxK3sbXOt7umgIrxQMp8uH+1xeJlp7F8/ydKOPyKOTtgiJg==}

  '@zag-js/file-upload@1.7.0':
    resolution: {integrity: sha512-6yJhUDLYsqbd0YBO70PzMDNVJJv8OdC0ZWrf51GMUSugGfSpvQZNDfpAW5Zkzqd4B5nkJDw5KiTSR5NYQlO7VA==}

  '@zag-js/file-utils@1.7.0':
    resolution: {integrity: sha512-Wb1VoI7UquG1ckJPMFPnmgLg351NI55SXjsEq+CrqgKQCo0httYFLPlkOpp4AbGsoUFZxXRxEXDEVzq5kpPFzQ==}

  '@zag-js/focus-trap@1.7.0':
    resolution: {integrity: sha512-JHMZAfiL1aoxMAQGolx+iDMgqOMy067yffaLr1tMX55NGZPfEyXEjgxmPXRPf728/7IOShLkWLX17yacmW/w/Q==}

  '@zag-js/focus-visible@1.7.0':
    resolution: {integrity: sha512-ycrO6VetctoA7aaw83rnp3erDmQe2Zsyobzp4fzpMbOBTNWzMklt4Kz54xa1ntkia8CpSWVfoauORLlaZoDiAw==}

  '@zag-js/i18n-utils@1.7.0':
    resolution: {integrity: sha512-CcDXxfobG2LlOU1m3xPzV5pXpCe0tSE9u+drtKMz7F/HOZkR3V0rpCCi/zKySPNa3uLC7G8efz1fGQXiOVKONw==}

  '@zag-js/interact-outside@1.7.0':
    resolution: {integrity: sha512-tmsVQmcH2N2X2mG2/8/+WRIo9WbRVvLe1OZa3lzFYV4Mu5i+tNK1CHMESpoAd/RdjJ6AyTR2zYiH05WZe76gMw==}

  '@zag-js/live-region@1.7.0':
    resolution: {integrity: sha512-u2bYIAnBIY+GZqfPqxn2ZylOqE2blUVW7Yc2Z4Ey05K4JXSH2gKR3xPmJCS9/u8tcFKQz5L4KQ/98ntgBG2fGQ==}

  '@zag-js/menu@1.7.0':
    resolution: {integrity: sha512-F2XbPC0cWrmj7nLrs1/is2osaPYX9blhEiZuEcGSrWG00w6xWyPb7bFpccW2nbq87JEc58xzW1pnTzPnaAnwSQ==}

  '@zag-js/pagination@1.7.0':
    resolution: {integrity: sha512-gIbJe1fIYlQCpXqWssET9CCmMWLvcz8OCCw7W3ASeLYRvUW3IzhkMAht5pEsvJEZ9tIWaab5fZ7OLqcgCTgVQw==}

  '@zag-js/popover@1.7.0':
    resolution: {integrity: sha512-Nf9grOVBWlnwQL+AR6X2hAy5bTNQng9xG2Cfo4E8rD2G/CJLKtUGCHHkG8xeQ969HT4urbOrgrZ5UpAhkpNlmw==}

  '@zag-js/popper@1.7.0':
    resolution: {integrity: sha512-1Tr9ZBS2VPeZ/zeAR5uEBYLkWn4VcycbaDDkvWxa44fi6LxknDf064cP+ql9AfUp/eUGD2hN9OSEhyxB/JXjKQ==}

  '@zag-js/progress@1.7.0':
    resolution: {integrity: sha512-dfjPtUGRZW0pURBalm55ACoN083EJ90cDT1RRRF72JhqlRJu/vSXngjSUFtYuG1WADGS3D7F5XIFMo+PAGynFg==}

  '@zag-js/radio-group@1.7.0':
    resolution: {integrity: sha512-9NlI5fTh8ZVX5nXm7nU/ZheQLZpHwrHZeKRjomVQQALEWuMZ5YJtVXZaUT5xsCRTk+LEQVSaKp10+aD/5cIMlA==}

  '@zag-js/rating-group@1.7.0':
    resolution: {integrity: sha512-jDr8M+2fXTxB9l8qm8ktA362eM6Xt6FzIz0dKlV1JsYr5KamhsZ70Y8MPB6i3b45FGdDdj02a2aaWGLRUaRnrw==}

  '@zag-js/rect-utils@1.7.0':
    resolution: {integrity: sha512-VvpqanvSrD/a5Gf5VHCM9yhkaBFWWsYTRNNQBtguNDrOh/tFvQBFAwes/BxvT+4fG4xbBL/fbSZIyhZ77Q7L2w==}

  '@zag-js/remove-scroll@1.7.0':
    resolution: {integrity: sha512-sjuBT/iHUZKoDaIdEa5fn0Ii6qjPbp/xO5g/2n2gI3RhRPjcc9jmrTxuvjKftB+ZoBy4GO8MbeaPKdQLIreufg==}

  '@zag-js/slider@1.7.0':
    resolution: {integrity: sha512-0h9ejtOWa4XjxApcCFyGt7By22kd6gG4PdUZgXiKlPCQFgYrxWXZqMlwH6ZtyD4VYUuRPJ05CezDU5KlmZD/3A==}

  '@zag-js/store@1.7.0':
    resolution: {integrity: sha512-3n+AGo3Y3d1+SkEjY/6QPcDU5kfGu4DEA9qMxJgnnOlYT07SEWByMQD2uoEji9M9psHcVvxm86OnF3Y6UuTsuA==}

  '@zag-js/svelte@1.7.0':
    resolution: {integrity: sha512-NN9G990gJlqMkW4FMa8tBLEs858MnogWaY5/QPs7skoVnFz5f+3S2kXtyJhyStBbRpZWCnELfbmb5KnMDZ57MQ==}
    peerDependencies:
      svelte: ^5.0.0-next.1

  '@zag-js/switch@1.7.0':
    resolution: {integrity: sha512-sz3whYMAD949fJ5v9DegU43SrpUNKhoPOum4LOpoSrh364ePfm7ShsTIgJnqPrdMknr+17ljLx54tXPS1SsMTw==}

  '@zag-js/tabs@1.7.0':
    resolution: {integrity: sha512-bAMp7Vhyis5j3BSKs4m0OwsbchRLLzFf6Yaf54CNraAUdKRwLQckznrajQLPI5F+BrHkGzMXvj/lt9jlGiKDcw==}

  '@zag-js/tags-input@1.7.0':
    resolution: {integrity: sha512-ME/KwP1yrPHX0bP0EqkHI30IQgrE2cAkREoRluM5ScpG3Uiug98x6+zts0YS9j1OB3pyTl0d4alECBruxN8cPA==}

  '@zag-js/tooltip@1.7.0':
    resolution: {integrity: sha512-ehZOewcxYZL4+ND5QMeDlQQrckssMTzxcReRCOVFXrRZb5X1jX6+ale9MSG+cJYMpQUqT2J5VtzMJH+GNj/jfw==}

  '@zag-js/types@1.7.0':
    resolution: {integrity: sha512-rmPonVc8EBOGIEJYjzWIBQ6LJwUMc3LnipRREECO+n7LNlUQUliCOFbHw1UOGP+4ZkCKmxjGFR3jLtjY8aN4gQ==}

  '@zag-js/utils@1.7.0':
    resolution: {integrity: sha512-yIxvH5V27a1WuLgCxHX7qpdtFo8vTJaZLafBpSNfVYG4B8FaxTE+P7JAcpmAzs3UyXura/WfAY2eVWWVBpk9ZA==}

  abort-controller@3.0.0:
    resolution: {integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==}
    engines: {node: '>=6.5'}

  acorn-babel@0.11.1-32:
    resolution: {integrity: sha512-Rnq3fPTdfBL9V0Bvt6Z+DBTPdTnemvMJkWgqgo8XQWQNy1t3xHoWDe0m0NKE9pQXfx9VytJ9jndlTdPHEjZzVg==}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@5.7.4:
    resolution: {integrity: sha512-1D++VG7BhrtvQpNbBzovKNc1FLGGEE/oGe7b9xJm/RFHMBeUaUGpluV9RLjZa47YFdPcDAenEYuq9pQPcMdLJg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acorn@8.14.0:
    resolution: {integrity: sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acorn@8.14.1:
    resolution: {integrity: sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  add-dom-event-listener@1.1.0:
    resolution: {integrity: sha512-WCxx1ixHT0GQU9hb0KI/mhgRQhnU+U3GvwY6ZvVjYq8rsihIGoaIOUbY0yMPBxLH5MDtr0kz3fisWGNcbWW7Jw==}

  agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}

  agent-base@7.1.3:
    resolution: {integrity: sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==}
    engines: {node: '>= 14'}

  ajv-draft-04@1.0.0:
    resolution: {integrity: sha512-mv00Te6nmYbRp5DCwclxtt7yV/joXJPGS7nM+97GdxvuttCOfgI3K4U25zboyeX0O+myI8ERluxQe5wljMmVIw==}
    peerDependencies:
      ajv: ^8.5.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv-formats@3.0.1:
    resolution: {integrity: sha512-8iUql50EUR+uUcdRQ3HDqa6EVyo3docL8g5WJ3FNcWmu62IbkGUue/pEyLBW8VGKKucTPgqeks4fIU1DA4yowQ==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv-keywords@3.5.2:
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==}
    peerDependencies:
      ajv: ^6.9.1

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ajv@8.12.0:
    resolution: {integrity: sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==}

  ajv@8.13.0:
    resolution: {integrity: sha512-PRA911Blj99jR5RMeTunVbNXMF6Lp4vZXnk5GQjcnUWUTsrXtekg/pnmFFI2u/I36Y/2bITGS30GZCXei6uNkA==}

  alien-signals@0.4.14:
    resolution: {integrity: sha512-itUAVzhczTmP2U5yX67xVpsbbOiquusbWVyA9N+sy6+r6YVbFkahXvNCeEPWEOMhwDYwbVbGHFkVL03N9I5g+Q==}

  amdefine@1.0.1:
    resolution: {integrity: sha512-S2Hw0TtNkMJhIabBwIojKL9YHO5T0n5eNqWJ7Lrlel/zDbftQpxpapi8tZs3X1HWa+u+QeydGmzzNU0m09+Rcg==}
    engines: {node: '>=0.4.2'}

  ansi-escapes@7.0.0:
    resolution: {integrity: sha512-GdYO7a61mR0fOlAsvC9/rIHf7L96sBc6dEWzeOu+KAea5bZyQRPIpojrVoI4AXGJS/ycu/fBTdLrUkA4ODrvjw==}
    engines: {node: '>=18'}

  ansi-regex@0.2.1:
    resolution: {integrity: sha512-sGwIGMjhYdW26/IhwK2gkWWI8DRCVO6uj3hYgHT+zD+QL1pa37tM3ujhyfcJIYSbsxp7Gxhy7zrRW/1AHm4BmA==}
    engines: {node: '>=0.10.0'}

  ansi-regex@2.1.1:
    resolution: {integrity: sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==}
    engines: {node: '>=0.10.0'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==}
    engines: {node: '>=12'}

  ansi-styles@1.1.0:
    resolution: {integrity: sha512-f2PKUkN5QngiSemowa6Mrk9MPCdtFiOSmibjZ+j1qhLGHHYsqZwmBMRF3IRMVXo8sybDqx2fJl2d/8OphBoWkA==}
    engines: {node: '>=0.10.0'}

  ansi-styles@2.2.1:
    resolution: {integrity: sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==}
    engines: {node: '>=0.10.0'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  antd@4.6.6:
    resolution: {integrity: sha512-ruqzFFyohjEEp+Qg5VzBzFgDjqdj0FoPt3V/bGtLjeuQXPqFrPs+y4WWWmqi3ydGmN4VPIGBwd3CybQDrZAN3g==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  aria-query@4.2.2:
    resolution: {integrity: sha512-o/HelwhuKpTj/frsOsbNLNgnNGVIFsVP/SW2BSF14gVl7kAfMOJ6/8wUAUvG1R1NHKrfG+2sHZTu0yauT1qBrA==}
    engines: {node: '>=6.0'}

  aria-query@5.3.0:
    resolution: {integrity: sha512-b0P0sZPKtyu8HkeRAfCq0IfURZK+SuwMjY1UXGBU27wpAiTwQAIlq56IbIO+ytk/JjS1fMR14ee5WBBfKi5J6A==}

  aria-query@5.3.1:
    resolution: {integrity: sha512-Z/ZeOgVl7bcSYZ/u/rh0fOpvEpq//LZmdbkXyc7syVzjPAhfOa9ebsdTSjEBDU4vs5nC98Kfduj1uFo0qyET3g==}
    engines: {node: '>= 0.4'}

  array-tree-filter@2.1.0:
    resolution: {integrity: sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw==}

  arrify@1.0.1:
    resolution: {integrity: sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA==}
    engines: {node: '>=0.10.0'}

  assertion-error@2.0.1:
    resolution: {integrity: sha512-Izi8RQcffqCeNVgFigKli1ssklIbpHnCYc6AknXGYoB6grJqyeby7jv12JUQgmTAnIDnbck1uxksT4dzN3PWBA==}
    engines: {node: '>=12'}

  ast-types@0.6.16:
    resolution: {integrity: sha512-RhFLnSepxd+6FBmMSF9Po91dsYHf6ARPcH4Co6Csx906M5ZRyxS1nJn4vqSOXlNt3YzgwlmQFzztAhFxjqe/dA==}
    engines: {node: '>= 0.6'}

  ast-types@0.8.15:
    resolution: {integrity: sha512-8WsusRFHT6D2CpPTCLLLeIp4dN4pMEgmVX/jaSBsbMFObktStNdGOE1ZW4x8V/RABr1VtqruQgpabZyvzrrrww==}
    engines: {node: '>= 0.8'}

  ast-types@0.9.6:
    resolution: {integrity: sha512-qEdtR2UH78yyHX/AUNfXmJTlM48XoFZKBdwi1nzkI1mJL21cmbu0cvjxjpkXJ5NENMq42H+hNs8VLJcqXLerBQ==}
    engines: {node: '>= 0.8'}

  async-each@0.1.6:
    resolution: {integrity: sha512-+AOhqLFrKKsf3E6oWhYzSX/YPEZztxHi6W3TU6LoQFuZjzSt5tqITd0ZJh2Qy3vsWoFj6HTIEBRKqPiS70HzjQ==}

  async-validator@3.5.2:
    resolution: {integrity: sha512-8eLCg00W9pIRZSB781UUX/H6Oskmm8xloZfr09lz5bikRpBVDlJ3hRVuxxP1SxcwsEYfJ4IU8Q19Y8/893r3rQ==}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  at-least-node@1.0.0:
    resolution: {integrity: sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==}
    engines: {node: '>= 4.0.0'}

  autoprefixer@10.4.21:
    resolution: {integrity: sha512-O+A6LWV5LDHSJD3LjHYoNi4VLsj/Whi7k6zG12xTYaU4cQ8oxQGckXNX8cRHK5yOZ/ppVHe0ZBXGzSV9jXdVbQ==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  axios@1.7.2:
    resolution: {integrity: sha512-2A8QhOMrbomlDuiLeK9XibIBzuHeRcqqNOHp0Cyp5EoJ1IFDh+XZH3A6BkXtv0K4gFGCI0Y4BM7B1wOEi0Rmgw==}

  axobject-query@4.1.0:
    resolution: {integrity: sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==}
    engines: {node: '>= 0.4'}

  babel-plugin-emotion@10.2.2:
    resolution: {integrity: sha512-SMSkGoqTbTyUTDeuVuPIWifPdUGkTk1Kf9BWRiXIOIcuyMfsdp2EjeiiFvOzX8NOBvEh/ypKYvUh2rkgAJMCLA==}

  babel-plugin-macros@2.8.0:
    resolution: {integrity: sha512-SEP5kJpfGYqYKpBrj5XU3ahw5p5GOHJ0U5ssOSQ/WBVdwkD2Dzlce95exQTs3jOVWPPKLBN2rlEWkCK7dSmLvg==}

  babel-plugin-macros@3.1.0:
    resolution: {integrity: sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==}
    engines: {node: '>=10', npm: '>=6'}

  babel-plugin-react-intl@7.9.4:
    resolution: {integrity: sha512-cMKrHEXrw43yT4M89Wbgq8A8N8lffSquj1Piwov/HVukR7jwOw8gf9btXNsQhT27ccyqEwy+M286JQYy0jby2g==}
    deprecated: this package has been renamed to babel-plugin-formatjs

  babel-plugin-syntax-jsx@6.18.0:
    resolution: {integrity: sha512-qrPaCSo9c8RHNRHIotaufGbuOBN8rtdC4QrrFFc43vyWCCz7Kl7GL1PGaXtMGQZUXrkCjNEgxDfmAuAabr/rlw==}

  babel@4.1.1:
    resolution: {integrity: sha512-/CuYmXiSCwnklOHXFH5KByxnIOmPbfYgnXRYxYwh8YDt6kQAih26zY7V7mmJdqpcjblnZq1ygDTY4PpwkN0aYQ==}
    hasBin: true

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  bignumber.js@8.1.1:
    resolution: {integrity: sha512-QD46ppGintwPGuL1KqmwhR0O+N2cZUg8JG/VzwI2e28sM9TqHjQB10lI4QAaMHVbLzwVLLAwEglpKPViWX+5NQ==}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  birecord@0.1.1:
    resolution: {integrity: sha512-VUpsf/qykW0heRlC8LooCq28Kxn3mAqKohhDG/49rrsQ1dT1CXyj/pgXS+5BSRzFTR/3DyIBOqQOrGyZOh71Aw==}

  bits-ui@1.5.3:
    resolution: {integrity: sha512-BTZ9/GU11DaEGyQp+AY+sXCMLZO0gbDC5J8l7+Ngj4Vf6hNOwrpMmoh5iuKktA6cphXYolVkUDgBWmkh415I+w==}
    engines: {node: '>=18', pnpm: '>=8.7.0'}
    peerDependencies:
      svelte: ^5.11.0

  bl@4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  browserslist@4.23.3:
    resolution: {integrity: sha512-btwCFJVjI4YWDNfau8RhZ+B1Q/VLoUITrm3RlP6y1tYGWIOa+InuYiRGXUBXo8nA1qKmHMyLB/iVQg5TT4eFoA==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  browserslist@4.24.4:
    resolution: {integrity: sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}

  cac@6.7.14:
    resolution: {integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==}
    engines: {node: '>=8'}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase-keys@6.2.2:
    resolution: {integrity: sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg==}
    engines: {node: '>=8'}

  camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  caniuse-lite@1.0.30001660:
    resolution: {integrity: sha512-GacvNTTuATm26qC74pt+ad1fW15mlQ/zuTzzY1ZoIzECTP8HURDfF43kNxPgf7H1jmelCBQTTbBNxdSXOA7Bqg==}

  caniuse-lite@1.0.30001707:
    resolution: {integrity: sha512-3qtRjw/HQSMlDWf+X79N206fepf4SOOU6SQLMaq/0KkZLmSjPxAkBOQQ+FxbHKfHmYLZFfdWsO3KA90ceHPSnw==}

  chai@5.1.2:
    resolution: {integrity: sha512-aGtmf24DW6MLHHG5gCx4zaI3uBq3KRtxeVs0DjFH6Z0rDNbsvTxFASFvdj79pxjxZ8/5u3PIiN3IwEIQkiiuPw==}
    engines: {node: '>=12'}

  chalk@0.5.1:
    resolution: {integrity: sha512-bIKA54hP8iZhyDT81TOsJiQvR1gW+ZYSXFaZUAvoD4wCHdbHY2actmpTE4x344ZlFqHbvoxKOaESULTZN2gstg==}
    engines: {node: '>=0.10.0'}

  chalk@1.1.3:
    resolution: {integrity: sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==}
    engines: {node: '>=0.10.0'}

  chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}

  chalk@3.0.0:
    resolution: {integrity: sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==}
    engines: {node: '>=8'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  chalk@5.4.1:
    resolution: {integrity: sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  check-error@2.1.1:
    resolution: {integrity: sha512-OAlb+T7V4Op9OwdkjmguYRqncdlx5JiofwOAUkmTF+jNdHwzTaTs4sRAGpzLF3oOz5xAyDGrPgeIDFQmDOTiJw==}
    engines: {node: '>= 16'}

  chokidar@0.12.6:
    resolution: {integrity: sha512-nMQqT43NJ46Yc75wx9pQ7p/yhzg/5qXHPcgHKUcTLCdcUr1xMUM92SSY4sfGNpJ2zRHQqUsk1hnlP4GW8mm0Aw==}
    deprecated: Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  chokidar@4.0.3:
    resolution: {integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==}
    engines: {node: '>= 14.16.0'}

  classnames@2.2.6:
    resolution: {integrity: sha512-JR/iSQOSt+LQIWwrwEzJ9uk0xfN3mTVYMwt1Ir5mUcSN6pU+V4zQFFaJsclJbPuAUQH+yfWef6tm7l1quW3C8Q==}

  cli-cursor@3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}

  cli-cursor@5.0.0:
    resolution: {integrity: sha512-aCj4O5wKyszjMmDT4tZj93kxyydN/K5zPWSCe6/0AV/AA1pqe5ZBIw0a2ZfPQV7lL5/yb5HsUreJ6UFAF1tEQw==}
    engines: {node: '>=18'}

  cli-spinners@2.9.2:
    resolution: {integrity: sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==}
    engines: {node: '>=6'}

  cli-truncate@4.0.0:
    resolution: {integrity: sha512-nPdaFdQ0h/GEigbPClz11D0v/ZJEwxmeVZGeMo3Z5StPtUTkA9o1lD6QwoirYiSDzbcwn2XcjwmCp68W1IS4TA==}
    engines: {node: '>=18'}

  cliui@7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}

  clone@1.0.4:
    resolution: {integrity: sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==}
    engines: {node: '>=0.8'}

  clsx@2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==}
    engines: {node: '>=6'}

  color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  colorette@2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@13.1.0:
    resolution: {integrity: sha512-/rFeCpNJQbhSZjGVwO9RFV3xPqbnERS8MmIQzCtD/zl6gpJuV/bMLuN92oG3F7d8oDEHHRrujSXNUr8fpjntKw==}
    engines: {node: '>=18'}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  commondir@1.0.1:
    resolution: {integrity: sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==}

  commoner@0.10.8:
    resolution: {integrity: sha512-3/qHkNMM6o/KGXHITA14y78PcfmXh4+AOCJpSoF73h4VY1JpdGv3CHMS5+JW6SwLhfJt4RhNmLAa7+RRX/62EQ==}
    engines: {node: '>= 0.8'}
    hasBin: true

  compare-versions@6.1.1:
    resolution: {integrity: sha512-4hm4VPpIecmlg59CHXnRDnqGplJFrbLG4aFEl5vl6cK1u76ws3LLvX7ikFnTDl5vo39sjWD6AaDPYodJp/NNHg==}

  compute-scroll-into-view@1.0.20:
    resolution: {integrity: sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg==}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  confbox@0.1.8:
    resolution: {integrity: sha512-RMtmw0iFkeR4YV+fUOSucriAQNb9g8zFR52MWCtl+cCZOFRNL6zeB395vPzFhEjjn4fMxXudmELnl/KF/WrK6w==}

  confbox@0.2.1:
    resolution: {integrity: sha512-hkT3yDPFbs95mNCy1+7qNKC6Pro+/ibzYxtM2iqEigpf0sVw+bg4Zh9/snjsBcf990vfIsg5+1U7VyiyBb3etg==}

  convert-source-map@1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  cookie@0.6.0:
    resolution: {integrity: sha512-U71cyTamuh1CRNCfpGY6to28lxvNwPG4Guz/EVjgf3Jmzv0vlDp1atT9eS5dDjMYHucpHbWns6Lwf3BKz6svdw==}
    engines: {node: '>= 0.6'}

  copy-anything@2.0.6:
    resolution: {integrity: sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==}

  copy-to-clipboard@3.3.3:
    resolution: {integrity: sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==}

  core-js-pure@3.37.1:
    resolution: {integrity: sha512-J/r5JTHSmzTxbiYYrzXg9w1VpqrYt+gexenBE9pugeyhwPZTAEJddyiReJWsLO6uNQ8xJZFbod6XC7KKwatCiA==}

  core-js@0.5.4:
    resolution: {integrity: sha512-2aXgi6NSK3KDi+teS8rVlSn6UqWWWdjOkrhMKAgjR9JY9ns9/UZ+tcsRiTpGtzkNgqNmCL1Em/sKKJw7S2Qz8Q==}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.

  core-js@3.42.0:
    resolution: {integrity: sha512-Sz4PP4ZA+Rq4II21qkNqOEDTDrCvcANId3xpIgB34NDkWc3UduWj2dqEtN9yZIq8Dk3HyPI33x9sqqU5C8sr0g==}

  core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  cosmiconfig@6.0.0:
    resolution: {integrity: sha512-xb3ZL6+L8b9JLLCx3ZdoZy4+2ECphCMo2PwqgP1tlfVq6M6YReyzBJtvWWtbDSpNr9hn96pkCiZqUcFEc+54Qg==}
    engines: {node: '>=8'}

  cosmiconfig@7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==}
    engines: {node: '>=10'}

  cross-env@7.0.3:
    resolution: {integrity: sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==}
    engines: {node: '>=10.14', npm: '>=6', yarn: '>=1'}
    hasBin: true

  cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}

  css.escape@1.5.1:
    resolution: {integrity: sha512-YUifsXXuknHlUsmlgyY0PKzgPOr7/FjCePfHNt0jxm83wHZi44VDMQ7/fGNkjY3/jV1MC+1CmZbaHzugyeRtpg==}

  cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  cssstyle@4.2.1:
    resolution: {integrity: sha512-9+vem03dMXG7gDmZ62uqmRiMRNtinIZ9ZyuF6BdxzfOD+FdN5hretzynkn0ReS2DO2GSw76RWHs0UmJPI2zUjw==}
    engines: {node: '>=18'}

  csstype@2.6.21:
    resolution: {integrity: sha512-Z1PhmomIfypOpoMjRQB70jfvy/wxT50qW08YXO5lMIJkrdq4yOTR+AW7FqutScmB9NkLwxo+jU+kZLbofZZq/w==}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  data-urls@5.0.0:
    resolution: {integrity: sha512-ZYP5VBHshaDAiVZxjbRVcFJpc+4xGgT0bK3vzy1HLN8jTO975HEbuYzZJcHoQEY5K1a0z8YayJkyVETa08eNTg==}
    engines: {node: '>=18'}

  date-fns@2.30.0:
    resolution: {integrity: sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==}
    engines: {node: '>=0.11'}

  dayjs@1.11.11:
    resolution: {integrity: sha512-okzr3f11N6WuqYtZSvm+F776mB41wRZMhKP+hc34YdW+KmtYYK9iqvHSwo2k9FEH3fhGXvOPV6yz2IcSrfRUDg==}

  de-indent@1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==}

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.0:
    resolution: {integrity: sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize-keys@1.1.1:
    resolution: {integrity: sha512-WiPxgEirIV0/eIOMcnFBA3/IJZAZqKnwAwWyvvdi4lsr1WCN22nhdf/3db3DoZcUjTV2SqfzIwNyp6y2xs3nmg==}
    engines: {node: '>=0.10.0'}

  decamelize@1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}

  decimal.js@10.5.0:
    resolution: {integrity: sha512-8vDa8Qxvr/+d94hSh5P3IJwI5t8/c0KsMp+g8bNw9cY2icONa5aPfvKeieW1WlG0WQYwwhJ7mjui2xtiePQSXw==}

  deep-eql@5.0.2:
    resolution: {integrity: sha512-h5k/5U50IJJFpzfL6nO9jaaumfjO/f2NjK/oYB2Djzm4p9L+3T9qWpZqZ2hAbLPuuYq9wrU08WQyBTL5GbPk5Q==}
    engines: {node: '>=6'}

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  deepmerge@2.2.1:
    resolution: {integrity: sha512-R9hc1Xa/NOBi9WRVUWg19rl1UB7Tt4kuPd+thNJgFZoxXsTz7ncaPaeIm+40oSGuP33DfMb4sZt1QIGiJzC4EA==}
    engines: {node: '>=0.10.0'}

  deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}

  defaults@1.0.4:
    resolution: {integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==}

  defined@1.0.1:
    resolution: {integrity: sha512-hsBd2qSVCRE+5PmNdHt1uzyrFu5d3RwmFDKzyNZMFq/EwDNJF7Ee5+D5oEKF0hU6LhtoUF1macFvOe4AskQC1Q==}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  dequal@2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}

  detect-indent@3.0.1:
    resolution: {integrity: sha512-xo3WP66SNbr1Eim85s/qyH0ZL8PQUwp86HWm0S1l8WnJ/zjT6T3w1nwNA0yOZeuvOemupEYvpvF6BIdYRuERJQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  detect-indent@6.1.0:
    resolution: {integrity: sha512-reYkTUJAZb9gUuZ2RvVCNhVHdg62RHnJ7WJl8ftMi4diZ6NWlciOzQN88pUhSELEwflJht4oQDv0F0BMlwaYtA==}
    engines: {node: '>=8'}

  detect-libc@1.0.3:
    resolution: {integrity: sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==}
    engines: {node: '>=0.10'}
    hasBin: true

  detective@4.7.1:
    resolution: {integrity: sha512-H6PmeeUcZloWtdt4DAkFyzFL94arpHr3NOwwmVILFiy+9Qd4JTxxXrzfyGk/lmct2qVGBwTSwSXagqu2BxmWig==}

  devalue@5.1.1:
    resolution: {integrity: sha512-maua5KUiapvEwiEAe+XnlZ3Rh0GD+qI1J/nb9vrJc3muPXvcF/8gXYTWF76+5DAqHyDUtOIImEuo0YKE9mshVw==}

  diff-sequences@25.2.6:
    resolution: {integrity: sha512-Hq8o7+6GaZeoFjtpgvRBUknSXNeJiCx7V9Fr94ZMljNiCr9n9L8H8aJqgWOQiDDGdyn29fRNcDdRVJ5fdyihfg==}
    engines: {node: '>= 8.3'}

  diff-sequences@29.6.3:
    resolution: {integrity: sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  dom-accessibility-api@0.5.16:
    resolution: {integrity: sha512-X7BJ2yElsnOJ30pZF4uIIDfBEVgF4XEBxL9Bxhy6dnrm5hkzqmsWHGTiHqRiITNhMyFLyAiWndIJP7Z1NTteDg==}

  dom-accessibility-api@0.6.3:
    resolution: {integrity: sha512-7ZgogeTnjuHbo+ct10G9Ffp0mif17idi0IyWNVA/wcwcm7NPOD/WEHVP3n7n3MhXqxoIYm8d6MuZohYWIZ4T3w==}

  dom-align@1.12.4:
    resolution: {integrity: sha512-R8LUSEay/68zE5c8/3BDxiTEvgb4xZTF0RKmAHfiEVN3klfIpXfi2/QCoiWPccVQ0J/ZGdz9OjzL4uJEP/MRAw==}

  dotenv@16.4.7:
    resolution: {integrity: sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==}
    engines: {node: '>=12'}

  drange@1.1.1:
    resolution: {integrity: sha512-pYxfDYpued//QpnLIm4Avk7rsNtAtQkUES2cwAYSvD/wd2pKD71gN2Ebj3e7klzXwjocvE8c5vx/1fxwpqmSxA==}
    engines: {node: '>=4'}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  easy-table@1.2.0:
    resolution: {integrity: sha512-OFzVOv03YpvtcWGe5AayU5G2hgybsg3iqA6drU8UaoZyB9jLGMTrz9+asnLp/E+6qPh88yEI1gvyZFZ41dmgww==}

  electron-to-chromium@1.5.123:
    resolution: {integrity: sha512-refir3NlutEZqlKaBLK0tzlVLe5P2wDKS7UQt/3SpibizgsRAPOsqQC3ffw1nlv3ze5gjRQZYHoPymgVZkplFA==}

  electron-to-chromium@1.5.25:
    resolution: {integrity: sha512-kMb204zvK3PsSlgvvwzI3wBIcAw15tRkYk+NQdsjdDtcQWTp2RABbMQ9rUBy8KNEOM+/E6ep+XC3AykiWZld4g==}

  emoji-regex@10.4.0:
    resolution: {integrity: sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  end-of-stream@1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==}

  enhanced-resolve@5.18.1:
    resolution: {integrity: sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg==}
    engines: {node: '>=10.13.0'}

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  environment@1.1.0:
    resolution: {integrity: sha512-xUtoPkMggbz0MPyPiIWr1Kp4aeWJjDZ6SMvURhimjdZgsRuDplF5/s9hcgGhyXMhs+6vpnuoiZ2kFiu3FMnS8Q==}
    engines: {node: '>=18'}

  errno@0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true

  error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}

  error-stack-parser@1.3.6:
    resolution: {integrity: sha512-xhuSYd8wLgOXwNgjcPeXMPL/IiiA1Huck+OPvClpJViVNNlJVtM41o+1emp7bPvlCJwCatFX2DWc05/DgfbWzA==}

  es-module-lexer@1.6.0:
    resolution: {integrity: sha512-qqnD1yMU6tk/jnaMosogGySTZP8YtUgAffA9nMN+E/rjxcfRQ6IEk7IiozUjgxKoFHBGjTLnrHB/YC45r/59EQ==}

  esbuild@0.24.2:
    resolution: {integrity: sha512-+9egpBW8I3CD5XPe0n6BfT5fxLzxrlDzqydF3aviG+9ni1lDC/OvMHcxqEFV0+LANZG5R1bFMWfUrjVsdwxJvA==}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.1.2:
    resolution: {integrity: sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==}
    engines: {node: '>=6'}

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  eslint-config-prettier@10.0.1:
    resolution: {integrity: sha512-lZBts941cyJyeaooiKxAtzoPHTN+GbQTJFAIdQbRhA4/8whaAraEh47Whw/ZFfrjNSnlAxqfm9i0XVAEkULjCw==}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-plugin-react-debug@1.26.2:
    resolution: {integrity: sha512-UKCXj090YGXYmVLfZ8yZh09RLPlMfhJFYRXGUL4i/IHal22PO7kNTwNSHw105THVJXTiKPxuj/dDbII3H2C+7w==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: ^4.9.5 || ^5.3.3
    peerDependenciesMeta:
      typescript:
        optional: true

  eslint-plugin-react-dom@1.26.2:
    resolution: {integrity: sha512-W4PLB5+zRt+Ceewtwf2tobEPBF+Pvl5ycElRPeKT9VOpn6IAqk0i5JqCVu7mPvPruLFbUDlGaHK769aZhqLyjA==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: ^4.9.5 || ^5.3.3
    peerDependenciesMeta:
      typescript:
        optional: true

  eslint-plugin-react-hooks-extra@1.26.2:
    resolution: {integrity: sha512-Xh1Pp6lvYDI8aOFDvtd1E6WzBE3QVk2cV48pmKQOWzzL25Tod/7kk4pOXoML1t1rqRQW8xcoL7UmrlR8IMQh+w==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: ^4.9.5 || ^5.3.3
    peerDependenciesMeta:
      typescript:
        optional: true

  eslint-plugin-react-hooks@5.1.0:
    resolution: {integrity: sha512-mpJRtPgHN2tNAvZ35AMfqeB3Xqeo273QxrHJsbBEPWODRM4r0yB6jfoROqKEYrOn27UtRPpcpHc2UqyBSuUNTw==}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0

  eslint-plugin-react-naming-convention@1.26.2:
    resolution: {integrity: sha512-eiudTnDwwVpOY6K2g2fsoklG3x4X7N0X4+wFM2AE0+qSy4TCCFic+H+NRi3T53nL0pbvNawHkjS8sRSRRzOG3A==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: ^4.9.5 || ^5.3.3
    peerDependenciesMeta:
      typescript:
        optional: true

  eslint-plugin-react-web-api@1.26.2:
    resolution: {integrity: sha512-xu0QWvptg9zDaf/hfiJ02hTOd/soF10ww3h9wnJZ7ohbMclIA89ZRET6lXXXJNie3HrOLsB+HmOg/h/Rc7zL+A==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: ^4.9.5 || ^5.3.3
    peerDependenciesMeta:
      typescript:
        optional: true

  eslint-plugin-react-x@1.26.2:
    resolution: {integrity: sha512-4wEHGPdCY8yNl0AYcZWDdQ6QyX7lRmjoaM7CSw3v9ZEHLh2u8ttKl2JJpx5mRKFWP0JxQ8YvbgLW8MovDAIWmw==}
    engines: {bun: '>=1.0.15', node: '>=18.18.0'}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      ts-api-utils: ^2.0.1
      typescript: ^4.9.5 || ^5.3.3
    peerDependenciesMeta:
      ts-api-utils:
        optional: true
      typescript:
        optional: true

  eslint-plugin-simple-import-sort@12.1.1:
    resolution: {integrity: sha512-6nuzu4xwQtE3332Uz0to+TxDQYRLTKRESSc2hefVT48Zc8JthmN23Gx9lnYhu0FtkRSL1oxny3kJ2aveVhmOVA==}
    peerDependencies:
      eslint: '>=5.0.0'

  eslint-plugin-svelte@3.8.2:
    resolution: {integrity: sha512-TtepyI7nqWOPBqDXu/1kAFTeus9VuMByFGj6WIxNiByCknmR7b4w5DBoQ2qhj2RY5dMXyUJGHRp/pm/J2BSRxg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.1 || ^9.0.0
      svelte: ^3.37.0 || ^4.0.0 || ^5.0.0
    peerDependenciesMeta:
      svelte:
        optional: true

  eslint-scope@8.2.0:
    resolution: {integrity: sha512-PHlWUfG6lvPc3yvP5A4PNyBL1W8fkDUccmI21JUu/+GKZBoH/W5u6usENXUrWFRsyoW5ACUjFGgAFQp5gUlb/A==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-scope@8.3.0:
    resolution: {integrity: sha512-pUNxi75F8MJ/GdeKtVLSbYg4ZI34J6C0C7sbL4YOp2exGwen7ZsuBqKzUhXd0qMQ362yET3z+uPwKeg/0C2XCQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.2.0:
    resolution: {integrity: sha512-UyLnSehNt62FFhSwjZlHmeokpRK59rcz29j+F1/aDgbkbRTk7wIc9XzdoasMUbRNKDM0qQt/+BJ4BrpFeABemw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@9.19.0:
    resolution: {integrity: sha512-ug92j0LepKlbbEv6hD911THhoRHmbdXt2gX+VDABAW/Ir7D3nqKdv5Pf5vtlyY6HQMTEP2skXY43ueqTCWssEA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  esm-env@1.2.2:
    resolution: {integrity: sha512-Epxrv+Nr/CaL4ZcFGPJIYLWFom+YeV1DqMLHJoEd9SYRxNbaFruBwfEX/kkHUJf55j2+TUbmDcmuilbP1TmXHA==}

  espree@10.3.0:
    resolution: {integrity: sha512-0QYC8b24HWY8zjRnDTL6RiHfDbAWn63qb4LMj1Z4b076A4une81+z03Kg7l7mn/48PUTqoLptSXez8oknU8Clg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  esprima-fb@15001.1001.0-dev-harmony-fb:
    resolution: {integrity: sha512-m7OsYzocA8OQ3+9CxmhIv7NPHtyDR2ixaLCO7kLZ+YH+xQ/BpaZmll9EXmc+kBxzWA8BRBXbNEuEQqQ6vfsgDw==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  esprima@2.7.3:
    resolution: {integrity: sha512-OarPfz0lFCiW4/AV2Oy1Rp9qu0iusTKqykwTspGCZtPxmF81JR4MmIebvF1F9+UOKth2ZubLQ4XGGaU+hSn99A==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  esprima@3.1.3:
    resolution: {integrity: sha512-AWwVMNxwhN8+NIPQzAQZCm7RkLC4RbM3B1OobMuyp3i+w73X57KCKaVIxaRZb+DYCojq7rspo+fmuQfAboyhFg==}
    engines: {node: '>=4'}
    hasBin: true

  esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  esquery@1.5.0:
    resolution: {integrity: sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==}
    engines: {node: '>=0.10'}

  esrap@1.4.6:
    resolution: {integrity: sha512-F/D2mADJ9SHY3IwksD4DAXjTt7qt7GWUf3/8RhCNWmC/67tyb55dpimHmy7EplakFaflV0R/PC+fdSPqrRHAQw==}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@1.9.3:
    resolution: {integrity: sha512-25w1fMXQrGdoquWnScXZGckOv+Wes+JDnuN/+7ex3SauFRS72r2lFDec0EKPt2YD1wUJ/IrfEex+9yp4hfSOJA==}
    engines: {node: '>=0.10.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}

  estree-walker@3.0.3:
    resolution: {integrity: sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==}

  esutils@1.1.6:
    resolution: {integrity: sha512-RG1ZkUT7iFJG9LSHr7KDuuMSlujfeTtMNIcInURxKAxhMtwQhI3NrQhz26gZQYlsYZQKzsnwtpKrFKj9K9Qu1A==}
    engines: {node: '>=0.10.0'}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  event-target-shim@5.0.1:
    resolution: {integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==}
    engines: {node: '>=6'}

  eventemitter3@5.0.1:
    resolution: {integrity: sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==}

  eventsource@2.0.2:
    resolution: {integrity: sha512-IzUmBGPR3+oUG9dUeXynyNmf91/3zUSJg1lCktzKw47OXuhco54U3r9B7O4XX+Rb1Itm9OZ2b0RkTs10bICOxA==}
    engines: {node: '>=12.0.0'}

  execa@4.1.0:
    resolution: {integrity: sha512-j5W0//W7f8UxAn8hXVnwG8tLwdiUy4FJLcSupCg6maBYZDpyBvTApK7KyuI4bKj8KOh1r2YH+6ucuYtJv1bTZA==}
    engines: {node: '>=10'}

  execa@8.0.1:
    resolution: {integrity: sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==}
    engines: {node: '>=16.17'}

  expect-type@1.1.0:
    resolution: {integrity: sha512-bFi65yM+xZgk+u/KRIpekdSYkTB5W1pEf0Lt8Q8Msh7b+eQ7LXVtIB1Bkm4fvclDEL1b2CZkMhv2mOeF8tMdkA==}
    engines: {node: '>=12.0.0'}

  exsolve@1.0.4:
    resolution: {integrity: sha512-xsZH6PXaER4XoV+NiT7JHp1bJodJVT+cxeSH1G0f0tlT0lJqYuHUP3bUx2HtfTDvOagMINYp8rsqusxud3RXhw==}

  extract-react-intl-messages@4.1.1:
    resolution: {integrity: sha512-dPogci5X7HVtV7VbUxajH/1YgfNRaW2VtEiVidZ/31Tq8314uzOtzVMNo0IrAPD2E+H1wHoPiu/j565TZsyIZg==}
    engines: {node: '>=10'}
    hasBin: true

  fast-deep-equal@2.0.1:
    resolution: {integrity: sha512-bCK/2Z4zLidyB4ReuIsvALH6w31YfAQDmXMqMx6FyfHqvBxtjC0eRumeSu4Bs3XtXwpyIywtSTrVT99BxY1f9w==}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-glob@3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fastq@1.17.1:
    resolution: {integrity: sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==}

  fdir@6.4.3:
    resolution: {integrity: sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  fetch-cookie@2.2.0:
    resolution: {integrity: sha512-h9AgfjURuCgA2+2ISl8GbavpUdR+WGAM2McW/ovn4tVccegp8ZqCKWSBR8uRdM8dDNlx5WdKRWxBYUwteLDCNQ==}

  fflate@0.4.8:
    resolution: {integrity: sha512-FJqqoDBR00Mdj9ppamLa/Y7vxm+PRmNWA67N846RvsoYVMKB4q3y/de5PA7gUmRMYK/8CMz2GDZQmCRN1wBcWA==}

  file-entry-cache@8.0.0:
    resolution: {integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==}
    engines: {node: '>=16.0.0'}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  find-root@1.1.0:
    resolution: {integrity: sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==}

  find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  flat-cache@4.0.1:
    resolution: {integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==}
    engines: {node: '>=16'}

  flat@5.0.2:
    resolution: {integrity: sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==}
    hasBin: true

  flatted@3.3.2:
    resolution: {integrity: sha512-AiwGJM8YcNOaobumgtng+6NHuOqC3A7MixFeDafM3X9cIUM+xUXoS5Vfgf+OihAYe20fxqNM9yPBXJzRtZ/4eA==}

  fn-name@3.0.0:
    resolution: {integrity: sha512-eNMNr5exLoavuAMhIUVsOKF79SWd/zG104ef6sxBTSw+cZc6BXdQXDvYcGvp0VbxVVSp1XDUNoz7mg1xMtSznA==}
    engines: {node: '>=8'}

  follow-redirects@1.15.6:
    resolution: {integrity: sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  foreground-child@3.3.0:
    resolution: {integrity: sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==}
    engines: {node: '>=14'}

  form-data@4.0.1:
    resolution: {integrity: sha512-tzN8e4TX8+kkxGPK8D5u0FNmjPUjw3lwC9lSLxxoB/+GtsJG91CO8bSWy73APlgAZzZbXEYZJuxjkHH2w+Ezhw==}
    engines: {node: '>= 6'}

  formik-antd@2.0.4:
    resolution: {integrity: sha512-3O87GtSFcwa+70pZuJu74yaWttiCnx4lJ4lbXcgx/WtLHSUAx9U2qlsxxwc7Nf7IeTQQbygijSCkqsKSCJHG6g==}
    peerDependencies:
      antd: '>= 3.12.1 < 5'
      formik: '>= 1.4.1 < 3'
      react: '>= 16.8.0'
      react-dom: '>= 16.8.0'

  formik@2.4.6:
    resolution: {integrity: sha512-A+2EI7U7aG296q2TLGvNapDNTZp1khVt5Vk0Q/fyfSROss0V/V6+txt2aJnwEos44IxTCW/LYAi/zgWzlevj+g==}
    peerDependencies:
      react: '>=16.8.0'

  fraction.js@4.3.7:
    resolution: {integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==}

  fs-extra@11.3.0:
    resolution: {integrity: sha512-Z4XaCL6dUDHfP/jT25jJKMmtxvuwbkrD1vNSMFlo9lNLY2c5FHYSQgHPRZUjAB26TpDEoW9HCOgplrdbaPV/ew==}
    engines: {node: '>=14.14'}

  fs-extra@9.1.0:
    resolution: {integrity: sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==}
    engines: {node: '>=10'}

  fs-readdir-recursive@0.1.2:
    resolution: {integrity: sha512-//yfxmYAazrsyb/rgeYDNFXFTuPYTGYirp5QHFSH8h/LaNUoP5bQAa2ikstdK1PR/bFd1CIlQLpUq6/u6UVfSw==}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@0.3.8:
    resolution: {integrity: sha512-3vlmn1QaPoqSnhnorLFlp3+r3dUCZ8eZlaew+H8QhqB+0YBc9HSITh9wiZo76KYYExTC9DwG6otE/OzwbBLVIw==}
    engines: {node: '>=0.8.0'}
    os: [darwin]

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-east-asian-width@1.3.0:
    resolution: {integrity: sha512-vpeMIQKxczTD/0s2CdEWHcb0eeJe6TFjxb+J5xgX7hScxqrGuyjmv4c1D4A/gelKfyox0gJJwIHF+fLjeaM8kQ==}
    engines: {node: '>=18'}

  get-stdin@4.0.1:
    resolution: {integrity: sha512-F5aQMywwJ2n85s4hJPTT9RPxGmubonuB10MNYo17/xph174n2MIR33HRguhzVag10O/npM7SPk73LMZNP+FaWw==}
    engines: {node: '>=0.10.0'}

  get-stream@5.2.0:
    resolution: {integrity: sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==}
    engines: {node: '>=8'}

  get-stream@8.0.1:
    resolution: {integrity: sha512-VaUJspBffn/LMCJVoMvSAdmscJyS1auj5Zulnn5UoYcY531UWmdwhRWkcGKnGU93m5HSXP9LP2usOryrBtQowA==}
    engines: {node: '>=16'}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob@10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true

  glob@5.0.15:
    resolution: {integrity: sha512-c9IPMazfRITpmAAKi22dK1VKxGDX9ehhqfABDriL/lzO92xcUKEJPQHrVA/2YHSNFB4iFlykVmWvwo48nr3OxA==}
    deprecated: Glob versions prior to v9 are no longer supported

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported

  glob@9.3.5:
    resolution: {integrity: sha512-e1LleDykUz2Iu+MTYdkSsuWX8lvAjAcs0Xef0lNIu0S2wOAzuTxCJtcd9S3cijlwYF18EsU3rzb8jPVobxDh9Q==}
    engines: {node: '>=16 || 14 >=14.17'}

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  globals@14.0.0:
    resolution: {integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==}
    engines: {node: '>=18'}

  globals@16.0.0:
    resolution: {integrity: sha512-iInW14XItCXET01CQFqudPOWP2jYMl7T+QRQT+UNcR/iQncN/F0UNpgd76iFkBPgNQb4+X3LV9tLJYzwh+Gl3A==}
    engines: {node: '>=18'}

  globals@6.4.1:
    resolution: {integrity: sha512-Lh7H0bYRNBMc2CapY+TYsCzcSM4HWHGFoQORuEcePk3y3IhpaZmFSJDirhNYSwq8QeHvaCqV/tHI2bdUhYryuw==}
    engines: {node: '>=0.10.0'}

  globrex@0.1.2:
    resolution: {integrity: sha512-uHJgbwAMwNFf5mLst7IWLNg14x1CkeqglJb/K3doi4dw6q2IvAAmM/Y81kevy83wP+Sst+nutFTYOGg3d1lsxg==}

  graceful-fs@2.0.3:
    resolution: {integrity: sha512-hcj/NTUWv+C3MbqrVb9F+aH6lvTwEHJdx2foBxlrVq5h6zE8Bfu4pv4CAAqbDcZrw/9Ak5lsRXlY9Ao8/F0Tuw==}
    engines: {node: '>=0.4.0'}
    deprecated: please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}

  hard-rejection@2.1.0:
    resolution: {integrity: sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==}
    engines: {node: '>=6'}

  has-ansi@0.1.0:
    resolution: {integrity: sha512-1YsTg1fk2/6JToQhtZkArMkurq8UoWU1Qe0aR3VUHjgij4nOylSWLWAtBXoZ4/dXOmugfLGm1c+QhuD0JyedFA==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  has-ansi@2.0.0:
    resolution: {integrity: sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg==}
    engines: {node: '>=0.10.0'}

  has-flag@1.0.0:
    resolution: {integrity: sha512-DyYHfIYwAJmjAjSSPKANxI8bFY9YtFrgkAfinBojQ8YJTOuOuav64tMUJv584SES4xl74PmuaevIyaLESHdTAA==}
    engines: {node: '>=0.10.0'}

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true

  hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}

  hosted-git-info@2.8.9:
    resolution: {integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==}

  html-encoding-sniffer@4.0.0:
    resolution: {integrity: sha512-Y22oTqIU4uuPgEemfz7NDJz6OeKf12Lsu+QC+s3BVpda64lTiMYCyGwg5ki4vFxkMwQdeZDl2adZoqUgdFuTgQ==}
    engines: {node: '>=18'}

  html-escaper@2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}

  html-parse-stringify2@2.0.1:
    resolution: {integrity: sha512-wMKQ3aJ/dwXzDHPpA7XgsRXXCkEhHkAF6Ioh7D51lgZO7Qy0LmcFddC9TI/qNQJvSM1KL8KbcR3FtuybsrzFlQ==}

  http-proxy-agent@7.0.2:
    resolution: {integrity: sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==}
    engines: {node: '>= 14'}

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}

  https-proxy-agent@7.0.6:
    resolution: {integrity: sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==}
    engines: {node: '>= 14'}

  human-signals@1.1.1:
    resolution: {integrity: sha512-SEQu7vl8KjNL2eoGBLF3+wAjpsNfA9XMlXAYj/3EdaNfAlxKthD1xjEQfGOUhllCGGJVNY34bRr6lPINhNjyZw==}
    engines: {node: '>=8.12.0'}

  human-signals@5.0.0:
    resolution: {integrity: sha512-AXcZb6vzzrFAUE61HnN4mpLqd/cSIwNQjtNWR0euPm6y0iqx3G4gOXaIDdtdDwZmhwe82LA6+zinmW4UBWVePQ==}
    engines: {node: '>=16.17.0'}

  husky@9.1.7:
    resolution: {integrity: sha512-5gs5ytaNjBrh5Ow3zrvdUUY+0VxIuWVL4i9irt6friV+BqdCfmV11CQTWMiBYWHbXhco+J1kHfTOUkePhCDvMA==}
    engines: {node: '>=18'}
    hasBin: true

  i18next-browser-languagedetector@4.0.2:
    resolution: {integrity: sha512-AK4IZ3XST4HIKShgpB2gOFeDPrMOnZx56GLA6dGo/8rvkiczIlq05lV8w77c3ShEZxtTZeUVRI4Q/cBFFVXS/w==}

  i18next@19.3.4:
    resolution: {integrity: sha512-ef7AxxutzdhBsBNugE9jgqsbwesG1muJOtZ9ZrPARPs/jXegViTp4+8JCeMp8BAyTIo1Zn0giqc8+2UpqFjU0w==}

  iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  ignore@5.3.1:
    resolution: {integrity: sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw==}
    engines: {node: '>= 4'}

  image-size@0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  immer@6.0.9:
    resolution: {integrity: sha512-SyCYnAuiRf67Lvk0VkwFvwtDoEiCMjeamnHvRfnVDyc7re1/rQrNxuL+jJ7lA3WvdC4uznrvbmm+clJ9+XXatg==}

  immutable@4.3.6:
    resolution: {integrity: sha512-Ju0+lEMyzMVZarkTn/gqRpdqd5dOPaz1mCZ0SH3JV6iFw81PldE/PEB1hWVEA288HPt4WXW8O7AWxB10M+03QQ==}

  import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}

  import-lazy@4.0.0:
    resolution: {integrity: sha512-rKtvo6a868b5Hu3heneU+L4yEQ4jYKLtjpnPeUdK7h0yzXGmyBTypknlkCvHFBqfX9YlorEiMM6Dnq/5atfHkw==}
    engines: {node: '>=8'}

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  indent-string@4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  inline-style-parser@0.2.4:
    resolution: {integrity: sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==}

  intl-messageformat-parser@5.5.1:
    resolution: {integrity: sha512-TvB3LqF2VtP6yI6HXlRT5TxX98HKha6hCcrg9dwlPwNaedVNuQA9KgBdtWKgiyakyCTYHQ+KJeFEstNKfZr64w==}
    deprecated: We've written a new parser that's 6x faster and is backwards compatible. Please use @formatjs/icu-messageformat-parser

  intl-messageformat-parser@6.1.2:
    resolution: {integrity: sha512-4GQDEPhl/ZMNDKwMsLqyw1LG2IAWjmLJXdmnRcHKeLQzpgtNYZI6lVw1279pqIkRk2MfKb9aDsVFzm565azK5A==}
    deprecated: We've written a new parser that's 6x faster and is backwards compatible. Please use @formatjs/icu-messageformat-parser

  intl-messageformat@10.7.15:
    resolution: {integrity: sha512-LRyExsEsefQSBjU2p47oAheoKz+EOJxSLDdjOaEjdriajfHsMXOmV/EhMvYSg9bAgCUHasuAC+mcUBe/95PfIg==}

  invariant@2.2.4:
    resolution: {integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==}

  is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-core-module@2.15.1:
    resolution: {integrity: sha512-z0vtXSwucUJtANQWldhbtbt7BnL0vxiFjIdDLAatwhDYty2bad6s+rijD6Ri4YuYJubLzIJLUidCh09e1djEVQ==}
    engines: {node: '>= 0.4'}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-finite@1.1.0:
    resolution: {integrity: sha512-cdyMtqX/BOqqNBBiKlIVkytNHm49MtMlYyn1zxzvJKWmFMlGzm+ry5BBfYyeY9YmNKbRSo/o7OX9w9ale0wg3w==}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-fullwidth-code-point@4.0.0:
    resolution: {integrity: sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==}
    engines: {node: '>=12'}

  is-fullwidth-code-point@5.0.0:
    resolution: {integrity: sha512-OVa3u9kkBbw7b8Xw5F9P+D/T9X+Z4+JruYVNapTjPYZYUznQ5YfWeFkOj606XYYW8yugTfC8Pj0hYqvi4ryAhA==}
    engines: {node: '>=18'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-immutable-type@5.0.1:
    resolution: {integrity: sha512-LkHEOGVZZXxGl8vDs+10k3DvP++SEoYEAJLRk6buTFi6kD7QekThV7xHS0j6gpnUCQ0zpud/gMDGiV4dQneLTg==}
    peerDependencies:
      eslint: '*'
      typescript: '>=4.7.4'

  is-integer@1.0.7:
    resolution: {integrity: sha512-RPQc/s9yBHSvpi+hs9dYiJ2cuFeU6x3TyyIp8O2H6SKEltIvJOzRj9ToyvcStDvPR/pS4rxgr1oBFajQjZ2Szg==}

  is-interactive@1.0.0:
    resolution: {integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==}
    engines: {node: '>=8'}

  is-module@1.0.0:
    resolution: {integrity: sha512-51ypPSPCoTEIN9dy5Oy+h4pShgJmPCygKfyRCISBI+JoWT/2oJvK8QPxmwv7b/p239jXrm9M1mlQbyKJ5A152g==}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-plain-obj@1.1.0:
    resolution: {integrity: sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==}
    engines: {node: '>=0.10.0'}

  is-plain-obj@2.1.0:
    resolution: {integrity: sha512-YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA==}
    engines: {node: '>=8'}

  is-potential-custom-element-name@1.0.1:
    resolution: {integrity: sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==}

  is-reference@1.2.1:
    resolution: {integrity: sha512-U82MsXXiFIrjCK4otLT+o2NA2Cd2g5MLoOVXUZjIOhLurrRxpEXzI8O0KZHr3IjLvlAH1kTPYSuqer5T9ZVBKQ==}

  is-reference@3.0.3:
    resolution: {integrity: sha512-ixkJoqQvAP88E6wLydLGGqCJsrFUnqoH6HnaczB8XmDH1oaWU+xxdptvikTgaEhtZ53Ky6YXiBuUI2WXLMCwjw==}

  is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}

  is-stream@3.0.0:
    resolution: {integrity: sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  is-typedarray@1.0.0:
    resolution: {integrity: sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==}

  is-unicode-supported@0.1.0:
    resolution: {integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==}
    engines: {node: '>=10'}

  is-what@3.14.1:
    resolution: {integrity: sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==}

  isarray@0.0.1:
    resolution: {integrity: sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  istanbul-lib-coverage@3.2.2:
    resolution: {integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==}
    engines: {node: '>=8'}

  istanbul-lib-report@3.0.1:
    resolution: {integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==}
    engines: {node: '>=10'}

  istanbul-lib-source-maps@5.0.6:
    resolution: {integrity: sha512-yg2d+Em4KizZC5niWhQaIomgf5WlL4vOOjZ5xGCmF8SnPE/mDWWXgvRExdcpCgh9lLRRa1/fSYp2ymmbJ1pI+A==}
    engines: {node: '>=10'}

  istanbul-reports@3.1.7:
    resolution: {integrity: sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==}
    engines: {node: '>=8'}

  jackspeak@3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}

  jest-diff@25.5.0:
    resolution: {integrity: sha512-z1kygetuPiREYdNIumRpAHY6RXiGmp70YHptjdaxTWGmA085W3iCnXNx0DhflK3vwrKmrRWyY1wUpkPMVxMK7A==}
    engines: {node: '>= 8.3'}

  jest-diff@29.7.0:
    resolution: {integrity: sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-extended@4.0.2:
    resolution: {integrity: sha512-FH7aaPgtGYHc9mRjriS0ZEHYM5/W69tLrFTIdzm+yJgeoCmmrSB/luSfMSqWP9O29QWHPEmJ4qmU6EwsZideog==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      jest: '>=27.2.5'
    peerDependenciesMeta:
      jest:
        optional: true

  jest-get-type@25.2.6:
    resolution: {integrity: sha512-DxjtyzOHjObRM+sM1knti6or+eOgcGU4xVSb2HNP1TqO4ahsT+rqZg+nyqHWJSvWgKC5cG3QjGFBqxLghiF/Ig==}
    engines: {node: '>= 8.3'}

  jest-get-type@29.6.3:
    resolution: {integrity: sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jiti@2.4.2:
    resolution: {integrity: sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==}
    hasBin: true

  jju@1.4.0:
    resolution: {integrity: sha512-8wb9Yw966OSxApiCt0K3yNJL8pnNeIv+OEq2YMidz4FKP6nonSRoOXc80iXY4JaN2FC11B9qsNmDsm+ZOfMROA==}

  js-base64@2.6.4:
    resolution: {integrity: sha512-pZe//GGmwJndub7ZghVHz7vjb2LgC1m8B07Au3eYqeqv9emhESByMXxaEgkUkEqJe87oBbSniGYoQNIBklc7IQ==}

  js-tokenizer@1.3.3:
    resolution: {integrity: sha512-tgxug7XL+CdGeXvP/G/DA5L5zHfW/WSb8ZBvrmRjz4ubOO0gOdoS63ypGTLk4YcDCzFdlPdwii6T788yQCmYzA==}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsdom@26.0.0:
    resolution: {integrity: sha512-BZYDGVAIriBWTpIxYzrXjv3E/4u8+/pSG5bQdIYCbNCGOvsPkDQfTVLAIXAf9ETdCpduCVTkDe2NNZ8NIwUVzw==}
    engines: {node: '>=18'}
    peerDependencies:
      canvas: ^3.0.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jsesc@0.5.0:
    resolution: {integrity: sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==}
    hasBin: true

  jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  json-sass@1.3.5:
    resolution: {integrity: sha512-5GD0czACOBxjXwuvq3NHZYK9WYb035ZQ5aQW/trDyr7Sjp6+J3aV1rPqmPVgv6SNtL4DXXq2nVjy1TWqGmvTuQ==}
    hasBin: true

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json2mq@0.2.0:
    resolution: {integrity: sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA==}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}

  jwt-decode@4.0.0:
    resolution: {integrity: sha512-+KJGIyHgkGuIq3IEBNftfhW/LfWhXUIY6OmyVWjliu5KH1y0fw7VQ8YndE2O4qZdMSd9SqbnC8GOcZEy0Om7sA==}
    engines: {node: '>=18'}

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}

  kleur@4.1.5:
    resolution: {integrity: sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==}
    engines: {node: '>=6'}

  knip@5.50.2:
    resolution: {integrity: sha512-TGpfeeSMlaRd5wUkcb4HsVGSiQrE289LZF9qtW2TLHkAZbB2rM53wVQbXSf1KjOvJfBSZYSyYQ6q79lufrwsPw==}
    engines: {node: '>=18.18.0'}
    hasBin: true
    peerDependencies:
      '@types/node': '>=18'
      typescript: '>=5.0.4'

  known-css-properties@0.36.0:
    resolution: {integrity: sha512-A+9jP+IUmuQsNdsLdcg6Yt7voiMF/D4K83ew0OpJtpu+l34ef7LaohWV0Rc6KNvzw6ZDizkqfyB5JznZnzuKQA==}

  kolorist@1.8.0:
    resolution: {integrity: sha512-Y+60/zizpJ3HRH8DCss+q95yr6145JXZo46OTpFvDZWLfRCE4qChOyk1b26nMaNpfHHgxagk9dXT5OP0Tfe+dQ==}

  launchdarkly-js-client-sdk@3.5.0:
    resolution: {integrity: sha512-3dgxC9S8K2ix6qjdArjZGOJPtAytgfQTuE+vWgjWJK7725rpYbuqbHghIFr5B0+WyWyVBYANldjWd1JdtYLwsw==}

  launchdarkly-js-sdk-common@5.4.0:
    resolution: {integrity: sha512-Kb3SDcB6S0HUpFNBZgtEt0YUV/fVkyg+gODfaOCJQ0Y0ApxLKNmmJBZOrPE2qIdzw536u4BqEjtaJdqJWCEElg==}

  less@4.2.0:
    resolution: {integrity: sha512-P3b3HJDBtSzsXUl0im2L7gTO5Ubg8mEN6G8qoTS77iXxXX4Hvu4Qj540PZDvQ8V6DmX6iXo98k7Md0Cm1PrLaA==}
    engines: {node: '>=6'}
    hasBin: true

  leven@1.0.2:
    resolution: {integrity: sha512-U3eIzC2mMAOMOuoJ25sA3eyraoBwndpQyYgBq5dyqrMTpvMg9l9X/ucFHxv622YcCg179WWqleoF7rSzfYrV+Q==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  lightningcss-darwin-arm64@1.29.1:
    resolution: {integrity: sha512-HtR5XJ5A0lvCqYAoSv2QdZZyoHNttBpa5EP9aNuzBQeKGfbyH5+UipLWvVzpP4Uml5ej4BYs5I9Lco9u1fECqw==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [darwin]

  lightningcss-darwin-x64@1.29.1:
    resolution: {integrity: sha512-k33G9IzKUpHy/J/3+9MCO4e+PzaFblsgBjSGlpAaFikeBFm8B/CkO3cKU9oI4g+fjS2KlkLM/Bza9K/aw8wsNA==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [darwin]

  lightningcss-freebsd-x64@1.29.1:
    resolution: {integrity: sha512-0SUW22fv/8kln2LnIdOCmSuXnxgxVC276W5KLTwoehiO0hxkacBxjHOL5EtHD8BAXg2BvuhsJPmVMasvby3LiQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [freebsd]

  lightningcss-linux-arm-gnueabihf@1.29.1:
    resolution: {integrity: sha512-sD32pFvlR0kDlqsOZmYqH/68SqUMPNj+0pucGxToXZi4XZgZmqeX/NkxNKCPsswAXU3UeYgDSpGhu05eAufjDg==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm]
    os: [linux]

  lightningcss-linux-arm64-gnu@1.29.1:
    resolution: {integrity: sha512-0+vClRIZ6mmJl/dxGuRsE197o1HDEeeRk6nzycSy2GofC2JsY4ifCRnvUWf/CUBQmlrvMzt6SMQNMSEu22csWQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-arm64-musl@1.29.1:
    resolution: {integrity: sha512-UKMFrG4rL/uHNgelBsDwJcBqVpzNJbzsKkbI3Ja5fg00sgQnHw/VrzUTEc4jhZ+AN2BvQYz/tkHu4vt1kLuJyw==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-x64-gnu@1.29.1:
    resolution: {integrity: sha512-u1S+xdODy/eEtjADqirA774y3jLcm8RPtYztwReEXoZKdzgsHYPl0s5V52Tst+GKzqjebkULT86XMSxejzfISw==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]

  lightningcss-linux-x64-musl@1.29.1:
    resolution: {integrity: sha512-L0Tx0DtaNUTzXv0lbGCLB/c/qEADanHbu4QdcNOXLIe1i8i22rZRpbT3gpWYsCh9aSL9zFujY/WmEXIatWvXbw==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]

  lightningcss-win32-arm64-msvc@1.29.1:
    resolution: {integrity: sha512-QoOVnkIEFfbW4xPi+dpdft/zAKmgLgsRHfJalEPYuJDOWf7cLQzYg0DEh8/sn737FaeMJxHZRc1oBreiwZCjog==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [win32]

  lightningcss-win32-x64-msvc@1.29.1:
    resolution: {integrity: sha512-NygcbThNBe4JElP+olyTI/doBNGJvLs3bFCRPdvuCcxZCcCZ71B858IHpdm7L1btZex0FvCmM17FK98Y9MRy1Q==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [win32]

  lightningcss@1.29.1:
    resolution: {integrity: sha512-FmGoeD4S05ewj+AkhTY+D+myDvXI6eL27FjHIjoyUkO/uw7WZD1fBVs0QxeYWa7E17CUHJaYX/RUGISCtcrG4Q==}
    engines: {node: '>= 12.0.0'}

  lilconfig@2.1.0:
    resolution: {integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==}
    engines: {node: '>=10'}

  lilconfig@3.1.3:
    resolution: {integrity: sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==}
    engines: {node: '>=14'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  lint-staged@15.4.3:
    resolution: {integrity: sha512-FoH1vOeouNh1pw+90S+cnuoFwRfUD9ijY2GKy5h7HS3OR7JVir2N2xrsa0+Twc1B7cW72L+88geG5cW4wIhn7g==}
    engines: {node: '>=18.12.0'}
    hasBin: true

  listr2@8.2.5:
    resolution: {integrity: sha512-iyAZCeyD+c1gPyE9qpFu8af0Y+MRtmKOncdGoA2S5EY8iFq99dmmvkNnHiWo+pj0s7yH7l3KPIgee77tKpXPWQ==}
    engines: {node: '>=18.0.0'}

  little-state-machine@3.1.4:
    resolution: {integrity: sha512-gYlLCj6oUME0NG34/2O0Ljy52qYYyYDJ5yiAuq2ijbaRlBKIqtQQkKkEYn0KfjYXCE693j+bdY22EyZin25Bhw==}
    peerDependencies:
      react: ^16.8.0
      react-dom: ^16.8.0

  load-json-file@6.2.0:
    resolution: {integrity: sha512-gUD/epcRms75Cw8RT1pUdHugZYM5ce64ucs2GEISABwkRsOQr0q2wm/MV2TKThycIe5e0ytRweW2RZxclogCdQ==}
    engines: {node: '>=8'}

  local-pkg@1.1.1:
    resolution: {integrity: sha512-WunYko2W1NcdfAFpuLUoucsgULmgDBRkdxHxWQ7mK0cQqwPiy8E1enjuRBrhLtZkB5iScJ1XIPdhVEFK8aOLSg==}
    engines: {node: '>=14'}

  locate-character@3.0.0:
    resolution: {integrity: sha512-SW13ws7BjaeJ6p7Q6CO2nchbYEc3X3J6WrmTTDto7yMPqVSZTUyY5Tjbid+Ab8gLnATtygYtiDIJGQRRn2ZOiA==}

  locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}

  lodash-node@2.4.1:
    resolution: {integrity: sha512-egEt8eNQp2kZWRmngahiqMoDCDCENv3uM188S7Ed5t4k3v6RrLELXC+FqLNMUnhCo7gvQX3G1V8opK/Lcslahg==}
    deprecated: This package is discontinued. Use lodash@^4.0.0.

  lodash.castarray@4.4.0:
    resolution: {integrity: sha512-aVx8ztPv7/2ULbArGJ2Y42bG1mEQ5mGjpdvrbJcJFU3TbYybe+QlLS4pst9zV52ymy2in1KpFPiZnAOATxD4+Q==}

  lodash.isplainobject@4.0.6:
    resolution: {integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash.mergewith@4.6.2:
    resolution: {integrity: sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ==}

  lodash.pick@4.4.0:
    resolution: {integrity: sha512-hXt6Ul/5yWjfklSGvLQl8vM//l3FtyHZeuelpzK6mm99pNvN9yTDruNZPEJZD1oWrqo+izBmB7oUfWgcCX7s4Q==}
    deprecated: This package is deprecated. Use destructuring assignment syntax instead.

  lodash@2.4.2:
    resolution: {integrity: sha512-Kak1hi6/hYHGVPmdyiZijoQyz5x2iGVzs6w9GYB/HiXEtylY7tIoYEROMjvM1d9nXJqPOrG2MNPMn01bJ+S0Rw==}
    engines: {'0': node, '1': rhino}

  lodash@3.10.1:
    resolution: {integrity: sha512-9mDDwqVIma6OZX79ZlDACZl8sBm0TEnkf99zV3iMA4GzkIT/9hiqP5mY0HoT1iNLCrKc/R1HByV+yJfRWVJryQ==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  log-symbols@4.1.0:
    resolution: {integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==}
    engines: {node: '>=10'}

  log-update@6.1.0:
    resolution: {integrity: sha512-9ie8ItPR6tjY5uYJh8K/Zrv/RMZ5VOlOWvtZdEHYSTFKZfIBPQa9tOAEeAWhd+AnIneLJ22w5fjOYtoutpWq5w==}
    engines: {node: '>=18'}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  loupe@3.1.3:
    resolution: {integrity: sha512-kkIp7XSkP78ZxJEsSxW3712C6teJVoeHHwgo9zJ380de7IYyJ2ISlxojcH2pC5OFLewESmnRi/+XCDIEEVyoug==}

  lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}

  lru-cache@2.7.3:
    resolution: {integrity: sha512-WpibWJ60c3AgAz8a2iYErDrcT2C7OmKnsWhIcHOjkUHFjkXncJhtLxNSqUmxRxRunpb5I8Vprd7aNSd2NtksJQ==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}

  lz-string@1.5.0:
    resolution: {integrity: sha512-h5bgJWpxJNswbU7qCrV0tIKQCaS3blPDrqKWx+QxzuzL1zGUzij9XCWLrSLsJPu5t+eWA/ycetzYAO5IOMcWAQ==}
    hasBin: true

  magic-string@0.30.17:
    resolution: {integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==}

  magic-string@0.30.8:
    resolution: {integrity: sha512-ISQTe55T2ao7XtlAStud6qwYPZjE4GK1S/BeVPus4jrq6JuOnQ00YKQC581RWhR122W7msZV263KzVeLoqidyQ==}
    engines: {node: '>=12'}

  magicast@0.3.5:
    resolution: {integrity: sha512-L0WhttDl+2BOsybvEOLK7fW3UA0OQ0IQ2d6Zl2x/a6vVRs3bAY0ECOSHHeL5jD+SbOpOCUEi0y1DgHEn9Qn1AQ==}

  make-dir@2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}

  make-dir@3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}

  make-dir@4.0.0:
    resolution: {integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==}
    engines: {node: '>=10'}

  map-obj@1.0.1:
    resolution: {integrity: sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg==}
    engines: {node: '>=0.10.0'}

  map-obj@4.3.0:
    resolution: {integrity: sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==}
    engines: {node: '>=8'}

  meow@6.1.1:
    resolution: {integrity: sha512-3YffViIt2QWgTy6Pale5QpopX/IvU3LPL03jOTqp6pGj3VjesdO/U8CuHMKpnQr4shCNCM5fd5XFFvIIl6JBHg==}
    engines: {node: '>=8'}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}

  mimic-fn@4.0.0:
    resolution: {integrity: sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==}
    engines: {node: '>=12'}

  mimic-function@5.0.1:
    resolution: {integrity: sha512-VP79XUPxV2CigYP3jWwAUFSku2aKqBH7uTAapFWCBqutsbmDo96KY5o8uh6U+/YSIn5OxJnXp73beVkpqMIGhA==}
    engines: {node: '>=18'}

  min-indent@1.0.1:
    resolution: {integrity: sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==}
    engines: {node: '>=4'}

  mini-store@3.0.6:
    resolution: {integrity: sha512-YzffKHbYsMQGUWQRKdsearR79QsMzzJcDDmZKlJBqt5JNkqpyJHYlK6gP61O36X+sLf76sO9G6mhKBe83gIZIQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  mini-svg-data-uri@1.4.4:
    resolution: {integrity: sha512-r9deDe9p5FJUPZAk3A59wGH7Ii9YrjjWw0jmw/liSbHl2CHiyXj6FcDXDu2K3TjVAXqiJdaw3xxwlZZr9E6nHg==}
    hasBin: true

  minimatch@0.2.14:
    resolution: {integrity: sha512-zZ+Jy8lVWlvqqeM8iZB7w7KmQkoJn8djM585z88rywrEbzoqawVa9FR5p2hwD+y74nfuKOjmNvi9gtWJNLqHvA==}
    deprecated: Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue

  minimatch@10.0.1:
    resolution: {integrity: sha512-ethXTt3SGGR+95gudmqJ1eNhRO7eGEGIgYA9vnPatK4/etz2MEVDno5GMCibdMTuBMyElzIlgxMna3K94XDIDQ==}
    engines: {node: 20 || >=22}

  minimatch@3.0.8:
    resolution: {integrity: sha512-6FsRAQsxQ61mw+qP1ZzbL9Bc78x2p5OqNgNpnoAFLTrX8n5Kxph0CsnhmKKNXTWjXqU5L0pGPR7hYk+XWZr60Q==}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@8.0.4:
    resolution: {integrity: sha512-W0Wvr9HyFXZRGIDgCicunpQ299OKXs9RgZfaukz4qAW/pJhcpUfupc9c+OObPOFueNy8VSrZgEmDtk6Kh4WzDA==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist-options@4.1.0:
    resolution: {integrity: sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A==}
    engines: {node: '>= 6'}

  minimist@1.1.3:
    resolution: {integrity: sha512-2RbeLaM/Hbo9vJ1+iRrxzfDnX9108qb2m923U+s+Ot2eMey0IYGdSjzHmvtg2XsxoCuMnzOMw7qc573RvnLgwg==}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass@4.2.8:
    resolution: {integrity: sha512-fNzuVyifolSLFL4NzpF+wEF4qrgqaaKX0haXPQEdQ7NKAN+WecoKMHV09YcuL/DHxrUsYQOK3MiuDf7Ip2OXfQ==}
    engines: {node: '>=8'}

  minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}

  mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true

  mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true

  mlly@1.7.4:
    resolution: {integrity: sha512-qmdSIPC4bDJXgZTCR7XosJiNKySV7O215tsPtDN9iEO/7q/76b/ijtgRu/+epFXSJhijtTCCGp3DWS549P3xKw==}

  moment-timezone@0.5.45:
    resolution: {integrity: sha512-HIWmqA86KcmCAhnMAN0wuDOARV/525R2+lOLotuGFzn4HO+FH+/645z2wx0Dt3iDv6/p61SIvKnDstISainhLQ==}

  moment@2.30.1:
    resolution: {integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==}

  mri@1.2.0:
    resolution: {integrity: sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA==}
    engines: {node: '>=4'}

  mrmime@2.0.1:
    resolution: {integrity: sha512-Y3wQdFg2Va6etvQ5I82yUhGdsKrcYox6p7FfL1LbK2J4V01F9TGlepTIhnK24t7koZibmg82KGglhA1XK5IsLQ==}
    engines: {node: '>=10'}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  muggle-string@0.4.1:
    resolution: {integrity: sha512-VNTrAak/KhO2i8dqqnqnAHOa3cYBwXEZe9h+D5h/1ZqFSTEFHdM65lR7RoIqq3tBBYavsOXV84NoHXZ0AkPyqQ==}

  nan@2.20.0:
    resolution: {integrity: sha512-bk3gXBZDGILuuo/6sKtr0DQmSThYHLtNCdSdXk9YkxD/jK6X2vmCyyXBBxyqZ4XcnzTyYEAThfX3DCEnLf6igw==}

  nanoid@3.3.8:
    resolution: {integrity: sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  needle@3.3.1:
    resolution: {integrity: sha512-6k0YULvhpw+RoLNiQCRKOl09Rv1dPLr8hHnVjHqdolKwDrdNyk+Hmrthi4lIGPPz3r39dLx0hsF5s40sZ3Us4Q==}
    engines: {node: '>= 4.4.x'}
    hasBin: true

  node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-releases@2.0.18:
    resolution: {integrity: sha512-d9VeXT4SJ7ZeOqGX6R5EM022wpL+eWPooLI+5UpWn2jCT1aosUQEhQP214x33Wkwx3JQMvIm+tIoVOdodFS40g==}

  node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  normalize-package-data@2.5.0:
    resolution: {integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==}

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  normalize-range@0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==}
    engines: {node: '>=0.10.0'}

  npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}

  npm-run-path@5.3.0:
    resolution: {integrity: sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  numbro@2.3.1:
    resolution: {integrity: sha512-oVGNn/Ssvz4i9C76C07NDk3QXjj4DApD5QZsJSeGckzs4YxwyWeSu4bD+s9M3BzemqN3y0DW1Fthxlb2p/+U5w==}

  nwsapi@2.2.16:
    resolution: {integrity: sha512-F1I/bimDpj3ncaNDhfyMWuFqmQDBwDB0Fogc2qpL3BWvkQteFD/8BzWuIRl83rq0DXfm8SGt/HFhLXZyljTXcQ==}

  object-assign@2.0.0:
    resolution: {integrity: sha512-TTVfbeUpQoCNyoOddbCTlMYnK8LsIpLD72jtE6SjwYL2JRr7lskqbMghqdTFp9wHWrZAlDWYUJ1unzPnWWPWQA==}
    engines: {node: '>=0.10.0'}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  omit.js@2.0.2:
    resolution: {integrity: sha512-hJmu9D+bNB40YpL9jYebQl4lsTW6yEHRTroJzNLqQJYHm7c+NQnJGfZmIWh8S3q3KoaxV1aLhV6B3+0N0/kyJg==}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}

  onetime@6.0.0:
    resolution: {integrity: sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==}
    engines: {node: '>=12'}

  onetime@7.0.0:
    resolution: {integrity: sha512-VXJjc87FScF88uafS3JllDgvAm+c/Slfz06lorj2uAY34rlUu0Nt+v8wreiImcrgAjjIHp1rXpTDlLOGw29WwQ==}
    engines: {node: '>=18'}

  opentracing@0.14.7:
    resolution: {integrity: sha512-vz9iS7MJ5+Bp1URw8Khvdyw1H/hGvzHWlKQ7eRrQojSCDL1/SrWfrY9QebLw97n2deyRtzHRC3MkQfVNUCo91Q==}
    engines: {node: '>=0.10'}

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  ora@5.4.1:
    resolution: {integrity: sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==}
    engines: {node: '>=10'}

  output-file-sync@1.1.2:
    resolution: {integrity: sha512-uQLlclru4xpCi+tfs80l3QF24KL81X57ELNMy7W/dox+JTtxUf1bLyQ8968fFCmSqqbokjW0kn+WBIlO+rSkNg==}

  p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  package-json-from-dist@1.0.0:
    resolution: {integrity: sha512-dATvCeZN/8wQsGywez1mzHtTlP22H8OEfPrVMLNr4/eGa+ijtLn/6M5f0dY8UKNrC2O9UCU6SSoG3qRKnt7STw==}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}

  parse-ms@4.0.0:
    resolution: {integrity: sha512-TXfryirbmq34y8QBwgqCVLi+8oA3oWx2eAnSn62ITyEhEYaWRlVZ2DvMM9eZbMs/RfxPu/PK/aBLyGj4IrqMHw==}
    engines: {node: '>=18'}

  parse-node-version@1.0.1:
    resolution: {integrity: sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==}
    engines: {node: '>= 0.10'}

  parse5@7.2.1:
    resolution: {integrity: sha512-BuBYQYlv1ckiPdQi/ohiivi9Sagc9JG+Ozs0r7b/0iK3sKmrb0b9FdWdBbOdx6hBCM/F9Ir82ofnBhtZOjCRPQ==}

  path-browserify@1.0.1:
    resolution: {integrity: sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-key@4.0.0:
    resolution: {integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==}
    engines: {node: '>=12'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}

  path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  pathe@2.0.2:
    resolution: {integrity: sha512-15Ztpk+nov8DR524R4BF7uEuzESgzUEAV4Ah7CUMNGXdE5ELuvxElxGXndBl32vMSsWa1jpNf22Z+Er3sKwq+w==}

  pathe@2.0.3:
    resolution: {integrity: sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==}

  pathval@2.0.0:
    resolution: {integrity: sha512-vE7JKRyES09KiunauX7nd2Q9/L7lhok4smP9RZTDeD4MVs72Dp2qNFVz39Nz5a0FVEW0BJR6C0DYrq6unoziZA==}
    engines: {node: '>= 14.16'}

  performance-now@2.1.0:
    resolution: {integrity: sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  picomatch@4.0.2:
    resolution: {integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==}
    engines: {node: '>=12'}

  pidtree@0.6.0:
    resolution: {integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==}
    engines: {node: '>=0.10'}
    hasBin: true

  pify@4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}

  pify@5.0.0:
    resolution: {integrity: sha512-eW/gHNMlxdSP6dmG6uJip6FXN0EQBwm2clYYd8Wul42Cwu/DK8HEftzsapcNdYe2MfLiIwZqsDk2RDEsTE79hA==}
    engines: {node: '>=10'}

  pkg-types@1.3.1:
    resolution: {integrity: sha512-/Jm5M4RvtBFVkKWRu2BLUTNP8/M2a+UwuAX+ae4770q1qVGtfjG+WTCupoZixokjmHiry8uI+dlY8KXYV5HVVQ==}

  pkg-types@2.1.0:
    resolution: {integrity: sha512-wmJwA+8ihJixSoHKxZJRBQG1oY8Yr9pGLzRmSsNms0iNWyHHAlZCa7mmKiFR10YPZuz/2k169JiS/inOjBCZ2A==}

  postcss-load-config@3.1.4:
    resolution: {integrity: sha512-6DiM4E7v4coTE4uzA8U//WhtPwyhiim3eyjEMFCnUpzbrkK9wJHgKDT2mR+HbtSrd/NubVaYTOpSpjUl8NQeRg==}
    engines: {node: '>= 10'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-rem-to-pixel@4.1.2:
    resolution: {integrity: sha512-EaA1Ak5SxmT31KA1clM4jRcEXQQ7oceXM2WF59fJw/mhspn1fhm202ZIfto5qFCX4QVuk/WVvATfkWaIlMomSw==}

  postcss-safe-parser@7.0.1:
    resolution: {integrity: sha512-0AioNCJZ2DPYz5ABT6bddIqlhgwhpHZ/l65YAYo0BCIn0xiDpsnTHz0gnoTGk0OXZW0JRs+cDwL8u/teRdz+8A==}
    engines: {node: '>=18.0'}
    peerDependencies:
      postcss: ^8.4.31

  postcss-scss@4.0.9:
    resolution: {integrity: sha512-AjKOeiwAitL/MXxQW2DliT28EKukvvbEWx3LBmJIRN8KfBGZbRTxNYW0kSqi1COiTZ57nZ9NW06S6ux//N1c9A==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.4.29

  postcss-selector-parser@6.0.10:
    resolution: {integrity: sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==}
    engines: {node: '>=4'}

  postcss-selector-parser@7.1.0:
    resolution: {integrity: sha512-8sLjZwK0R+JlxlYcTuVnyT2v+htpdrjDOKuMcOVdYjt52Lh8hWRYpxBPoKx/Zg+bcjc3wx6fmQevMmUztS/ccA==}
    engines: {node: '>=4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  postcss@5.2.18:
    resolution: {integrity: sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==}
    engines: {node: '>=0.12'}

  postcss@8.5.1:
    resolution: {integrity: sha512-6oz2beyjc5VMn/KV1pPw8fliQkhBXrVn1Z3TVyqZxU8kZpzEKhBdmCFqI6ZbmGtamQvQGuU1sgPTk8ZrXDD7jQ==}
    engines: {node: ^10 || ^12 || >=14}

  posthog-js@1.245.1:
    resolution: {integrity: sha512-I2HWR4VKAfX6Y8e/VtAq1DRUMcZi/1pjlpaRqDp60AGs0JnTiJ1F7o2ke52VTIs6gBnoZIQCe3Xh8z384hLMGQ==}
    peerDependencies:
      '@rrweb/types': 2.0.0-alpha.17
      rrweb-snapshot: 2.0.0-alpha.17
    peerDependenciesMeta:
      '@rrweb/types':
        optional: true
      rrweb-snapshot:
        optional: true

  preact@10.26.6:
    resolution: {integrity: sha512-5SRRBinwpwkaD+OqlBDeITlRgvd8I8QlxHJw9AxSdMNV6O+LodN9nUyYGpSF7sadHjs6RzeFShMexC6DbtWr9g==}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  prettier-plugin-svelte@3.4.0:
    resolution: {integrity: sha512-pn1ra/0mPObzqoIQn/vUTR3ZZI6UuZ0sHqMK5x2jMLGrs53h0sXhkVuDcrlssHwIMk7FYrMjHBPoUSyyEEDlBQ==}
    peerDependencies:
      prettier: ^3.0.0
      svelte: ^3.2.0 || ^4.0.0-next.0 || ^5.0.0-next.0

  prettier-plugin-tailwindcss@0.6.11:
    resolution: {integrity: sha512-YxaYSIvZPAqhrrEpRtonnrXdghZg1irNg4qrjboCXrpybLWVs55cW2N3juhspVJiO0JBvYJT8SYsJpc8OQSnsA==}
    engines: {node: '>=14.21.3'}
    peerDependencies:
      '@ianvs/prettier-plugin-sort-imports': '*'
      '@prettier/plugin-pug': '*'
      '@shopify/prettier-plugin-liquid': '*'
      '@trivago/prettier-plugin-sort-imports': '*'
      '@zackad/prettier-plugin-twig': '*'
      prettier: ^3.0
      prettier-plugin-astro: '*'
      prettier-plugin-css-order: '*'
      prettier-plugin-import-sort: '*'
      prettier-plugin-jsdoc: '*'
      prettier-plugin-marko: '*'
      prettier-plugin-multiline-arrays: '*'
      prettier-plugin-organize-attributes: '*'
      prettier-plugin-organize-imports: '*'
      prettier-plugin-sort-imports: '*'
      prettier-plugin-style-order: '*'
      prettier-plugin-svelte: '*'
    peerDependenciesMeta:
      '@ianvs/prettier-plugin-sort-imports':
        optional: true
      '@prettier/plugin-pug':
        optional: true
      '@shopify/prettier-plugin-liquid':
        optional: true
      '@trivago/prettier-plugin-sort-imports':
        optional: true
      '@zackad/prettier-plugin-twig':
        optional: true
      prettier-plugin-astro:
        optional: true
      prettier-plugin-css-order:
        optional: true
      prettier-plugin-import-sort:
        optional: true
      prettier-plugin-jsdoc:
        optional: true
      prettier-plugin-marko:
        optional: true
      prettier-plugin-multiline-arrays:
        optional: true
      prettier-plugin-organize-attributes:
        optional: true
      prettier-plugin-organize-imports:
        optional: true
      prettier-plugin-sort-imports:
        optional: true
      prettier-plugin-style-order:
        optional: true
      prettier-plugin-svelte:
        optional: true

  prettier@3.5.1:
    resolution: {integrity: sha512-hPpFQvHwL3Qv5AdRvBFMhnKo4tYxp0ReXiPn2bxkiohEX6mBeBwEpBSQTkD458RaaDKQMYSp4hX4UtfUTA5wDw==}
    engines: {node: '>=14'}
    hasBin: true

  pretty-format@25.5.0:
    resolution: {integrity: sha512-kbo/kq2LQ/A/is0PQwsEHM7Ca6//bGPPvU6UnsdDRSKTWxT/ru/xb88v4BJf6a69H+uTytOEsTusT9ksd/1iWQ==}
    engines: {node: '>= 8.3'}

  pretty-format@26.6.2:
    resolution: {integrity: sha512-7AeGuCYNGmycyQbCqd/3PWH4eOoX/OiCa0uphp57NVTeAGdJGaAliecxwBDHYQCIvrW7aDBZCYeNTP/WX69mkg==}
    engines: {node: '>= 10'}

  pretty-format@27.5.1:
    resolution: {integrity: sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ==}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  pretty-format@29.7.0:
    resolution: {integrity: sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  pretty-ms@9.0.0:
    resolution: {integrity: sha512-E9e9HJ9R9NasGOgPaPE8VMeiPKAyWR5jcFpNnwIejslIhWqdqOrb2wShBsncMPUb+BcCd2OPYfh7p2W6oemTng==}
    engines: {node: '>=18'}

  private@0.1.8:
    resolution: {integrity: sha512-VvivMrbvd2nKkiG38qjULzlc+4Vx4wm/whI9pQD35YrARNnhxeiRktSOhSukRLFNlzg6Br/cJPet5J/u19r/mg==}
    engines: {node: '>= 0.6'}

  progress@2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==}
    engines: {node: '>=0.4.0'}

  promise-polyfill@8.3.0:
    resolution: {integrity: sha512-H5oELycFml5yto/atYqmjyigJoAo3+OXwolYiH7OfQuYlAqhxNvTfiNMbV9hsC6Yp83yE5r2KTVmtrG6R9i6Pg==}

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  property-expr@2.0.6:
    resolution: {integrity: sha512-SVtmxhRE/CGkn3eZY1T6pC8Nln6Fr/lu1mKSgRud0eC73whjGfoAogbn78LkD8aFL0zz3bAFerKSnOl7NlErBA==}

  proxy-compare@3.0.1:
    resolution: {integrity: sha512-V9plBAt3qjMlS1+nC8771KNf6oJ12gExvaxnNzN/9yVRLdTv/lc+oJlnSzrdYDAvBfTStPCoiaCOTmTs0adv7Q==}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  prr@1.0.1:
    resolution: {integrity: sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==}

  psl@1.9.0:
    resolution: {integrity: sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==}

  pump@3.0.0:
    resolution: {integrity: sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  q@1.5.1:
    resolution: {integrity: sha512-kV/CThkXo6xyFEZUugw/+pIOywXcDbFYgSct5cT3gqlbkBE1SJdwy6UQoZvodiWF/ckQLZyDE/Bu1M6gVu5lVw==}
    engines: {node: '>=0.6.0', teleport: '>=0.2.0'}
    deprecated: |-
      You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.

      (For a CapTP with native promises, see @endo/eventual-send and @endo/captp)

  quansync@0.2.10:
    resolution: {integrity: sha512-t41VRkMYbkHyCYmOvx/6URnN80H7k4X0lLdBMGsz+maAwrJQYB1djpV6vHrQIBE0WBSGqhtEHrK9U3DWWH8v7A==}

  querystringify@2.2.0:
    resolution: {integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  quick-lru@4.0.1:
    resolution: {integrity: sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==}
    engines: {node: '>=8'}

  raf@3.4.1:
    resolution: {integrity: sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==}

  randexp@0.5.3:
    resolution: {integrity: sha512-U+5l2KrcMNOUPYvazA3h5ekF80FHTUG+87SEAmHZmolh1M+i/WyTCxVzmi+tidIa1tM4BSe8g2Y/D3loWDjj+w==}
    engines: {node: '>=4'}

  rc-align@4.0.15:
    resolution: {integrity: sha512-wqJtVH60pka/nOX7/IspElA8gjPNQKIx/ZqJ6heATCkXpe1Zg4cPVrMD2vC96wjsFFL8WsmhPbx9tdMo1qqlIA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-animate@3.1.1:
    resolution: {integrity: sha512-8wg2Zg3EETy0k/9kYuis30NJNQg1D6/WSQwnCiz6SvyxQXNet/rVraRz3bPngwY6rcU2nlRvoShiYOorXyF7Sg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-cascader@1.4.3:
    resolution: {integrity: sha512-Q4l9Mv8aaISJ+giVnM9IaXxDeMqHUGLvi4F+LksS6pHlaKlN4awop/L+IMjIXpL+ug/ojaCyv/ixcVopJYYCVA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-checkbox@2.3.2:
    resolution: {integrity: sha512-afVi1FYiGv1U0JlpNH/UaEXdh6WUJjcWokj/nUN2TgG80bfG+MDdbfHKlLcNNba94mbjy2/SXJ1HDgrOkXGAjg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-collapse@2.0.1:
    resolution: {integrity: sha512-sRNqwQovzQoptTh7dCwj3kfxrdor2oNXrGSBz+QJxSFS7N3Ujgf8X/KlN2ElCkwBKf7nNv36t9dwH0HEku4wJg==}

  rc-dialog@8.2.2:
    resolution: {integrity: sha512-U4jR5bE7XpIbMC20JAIv91254b+vQ8LODd8Kxco0XvkL+eJ1aCYkOfRqevJ1ipOIzF3s6F08jSH8YvJqxvpAvA==}
    peerDependencies:
      react: ^16.0.0
      react-dom: ^16.0.0

  rc-drawer@4.1.0:
    resolution: {integrity: sha512-kjeQFngPjdzAFahNIV0EvEBoIKMOnvUsAxpkSPELoD/1DuR4nLafom5ryma+TIxGwkFJ92W6yjsMi1U9aiOTeQ==}
    peerDependencies:
      react: '*'

  rc-dropdown@3.2.5:
    resolution: {integrity: sha512-dVO2eulOSbEf+F4OyhCY5iGiMVhUYY/qeXxL7Ex2jDBt/xc89jU07mNoowV6aWxwVOc70pxEINff0oM2ogjluA==}
    peerDependencies:
      react: '*'
      react-dom: '*'

  rc-field-form@1.10.1:
    resolution: {integrity: sha512-aosTtNTqLYX2jsG5GyCv7axe+b57XH73T7TmmrX/cmhemhtFjvNE6RkRkmtP9VOJnZg5YGC5HfK172cnJ1Ij7Q==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '*'

  rc-image@3.0.6:
    resolution: {integrity: sha512-Dn8mTSlcgKJko417OX8+6yyNIL9+DEa81aexBfT78qWlEpcxtR4GgdsU0+zJLNqa2rnGZyjaBLFtaPw9tUuxYA==}
    peerDependencies:
      react: ^16.8.0
      react-dom: ^16.8.0

  rc-input-number@6.0.1:
    resolution: {integrity: sha512-cS1k6IB/V84VUQd5qWzGFrLHvZjWGHGmYbrvR0QP/C1Ju1SlBqlhqhOBTc6w+dpPs84PCH5caZtNzsHeWZ1zYA==}

  rc-mentions@1.5.3:
    resolution: {integrity: sha512-NG/KB8YiKBCJPHHvr/QapAb4f9YzLJn7kDHtmI1K6t7ZMM5YgrjIxNNhoRKKP9zJvb9PdPts69Hbg4ZMvLVIFQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-menu@8.7.1:
    resolution: {integrity: sha512-CuuJ9oS1oPAfenqAMa3CZZE7RrPcPTHV3310cf6RO2uJgE9ztqasRFMEBwtruH16OexTr0igTCXySm+e2/TBQg==}

  rc-motion@2.9.2:
    resolution: {integrity: sha512-fUAhHKLDdkAXIDLH0GYwof3raS58dtNUmzLF2MeiR8o6n4thNpSDQhOqQzWE4WfFZDCi9VEN8n7tiB7czREcyw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-notification@4.4.0:
    resolution: {integrity: sha512-IDeNAFGVeOsy1tv4zNVqMAXB9tianR80ewQbtObaAQfjwAjWfONdqdyjFkEU6nc6UQhSUYA5OcTGb7kwwbnh0g==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'

  rc-pagination@3.0.4:
    resolution: {integrity: sha512-9v9mmB7FTWS4kWRLFfWafm6LtvB+xdNi+pTIwUODSevzImrlrmMOIhDrOB3u2tEXiy8LyqvCnoyPYt5jQBapxA==}
    peerDependencies:
      react: ^16.0.0
      react-dom: ^16.0.0

  rc-picker@2.1.0:
    resolution: {integrity: sha512-Tu8+yR0qnBVND4v+eta8GRkLu4OvTUaxlk7ZHDAkVFm1RHLAl1GzA6Foni9vcpkMnFMFD6EzpaOlkArI0ryuDA==}
    engines: {node: '>=8.x'}

  rc-progress@3.1.4:
    resolution: {integrity: sha512-XBAif08eunHssGeIdxMXOmRQRULdHaDdIFENQ578CMb4dyewahmmfJRyab+hw4KH4XssEzzYOkAInTLS7JJG+Q==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-rate@2.8.2:
    resolution: {integrity: sha512-f9T/D+ZwWQrWHkpidpQbnXpnVMGMC4eSRAkwuu88a8Qv1C/9LNc4AErazoh8tpnZBFqq19F3j0Glv+sDgkfEig==}
    engines: {node: '>=8.x'}

  rc-resize-observer@0.2.6:
    resolution: {integrity: sha512-YX6nYnd6fk7zbuvT6oSDMKiZjyngjHoy+fz+vL3Tez38d/G5iGdaDJa2yE7345G6sc4Mm1IGRUIwclvltddhmA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-resize-observer@1.4.0:
    resolution: {integrity: sha512-PnMVyRid9JLxFavTjeDXEXo65HCRqbmLBw9xX9gfC4BZiSzbLXKzW3jPz+J0P71pLbD5tBMTT+mkstV5gD0c9Q==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-select@11.3.3:
    resolution: {integrity: sha512-YMsGVEZxXctj15nIZKlFCkiOxMe0PNBeACN6nHqDozDYKR/aqP8J3XZqZ5Gw/fcgS4bI50zPVMieJKlY8/6Wfw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'

  rc-slider@9.5.4:
    resolution: {integrity: sha512-24goJnWhmWi0ojNZMoPSMni2wh73IPqEK0TJh7rWn10hPLLKgG8x3KRR0g4uUdCS9APHyosqxGXUIJKGydJXVg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-steps@4.1.4:
    resolution: {integrity: sha512-qoCqKZWSpkh/b03ASGx1WhpKnuZcRWmvuW+ZUu4mvMdfvFzVxblTwUM+9aBd0mlEUFmt6GW8FXhMpHkK3Uzp3w==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-switch@3.2.2:
    resolution: {integrity: sha512-+gUJClsZZzvAHGy1vZfnwySxj+MjLlGRyXKXScrtCTcmiYNPzxDFOxdQ/3pK1Kt/0POvwJ/6ALOR8gwdXGhs+A==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-table@7.9.10:
    resolution: {integrity: sha512-WtPBxYsBU/a5MIglilbMlVkiXkPKXpUM/CPCFaqA2veh1b7J40mbTGQmU8VT6S0FClkI5jm0QBtSp6LstPkOMQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: ^16.0.0
      react-dom: ^16.0.0

  rc-tabs@11.6.2:
    resolution: {integrity: sha512-7Z5Lg+nP/H4V7dIlewrOC0+aogRVH3ASjTy4VIletYOeStGPWYSfwBnUTBdcCXcUuWuyyKnNkYrUD0yaRqUCIA==}
    engines: {node: '>=8.x'}

  rc-textarea@0.3.7:
    resolution: {integrity: sha512-yCdZ6binKmAQB13hc/oehh0E/QRwoPP1pjF21aHBxlgXO3RzPF6dUu4LG2R4FZ1zx/fQd2L1faktulrXOM/2rw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-tooltip@5.0.2:
    resolution: {integrity: sha512-A4FejSG56PzYtSNUU4H1pVzfhtkV/+qMT2clK0CsSj+9mbc4USEtpWeX6A/jjVL+goBOMKj8qlH7BCZmZWh/Nw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-tree-select@4.1.3:
    resolution: {integrity: sha512-vk/T1vHNvuBZyoq8CvOF6iaiyVe6Y8QmQflTYFgabVsTJ1d/obkO9tAXOvJELZgKJ9ljduDVaAZAgcq0Yap+mg==}
    peerDependencies:
      react: '*'
      react-dom: '*'

  rc-tree@3.10.0:
    resolution: {integrity: sha512-kf7J/f2E2T8Kfta3/1BIg65AzTmXOgOjn0KOpvD3KI/gqkfKMRKUS1ybkxW39JUPpKwdeOHFnYH+nFFMq7tkfg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'

  rc-trigger@5.0.9:
    resolution: {integrity: sha512-N+q/ur2dpJSPDWbZQ34ztpGorms1QIphtmFpxKE5z+wMJw2BIASkMDEfwHJ/ssvZQxScjQza0/eQ0CWUI0e+EQ==}
    engines: {node: '>=8.x'}

  rc-trigger@5.3.4:
    resolution: {integrity: sha512-mQv+vas0TwKcjAO2izNPkqR4j86OemLRmvL2nOzdP9OWNWA1ivoTt5hzFqYNW9zACwmTezRiN8bttrC7cZzYSw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-upload@3.3.4:
    resolution: {integrity: sha512-v2sirR4JL31UTHD/f0LGUdd+tpFaOVUTPeIEjAXRP9kRN8TFhqOgcXl5ixtyqj90FmtRUmKmafCv0EmhBQUHqQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-util@4.21.1:
    resolution: {integrity: sha512-Z+vlkSQVc1l8O2UjR3WQ+XdWlhj5q9BMQNLk2iOBch75CqPfrJyGtcWMcnhRlNuDu0Ndtt4kLVO8JI8BrABobg==}

  rc-util@5.43.0:
    resolution: {integrity: sha512-AzC7KKOXFqAdIBqdGWepL9Xn7cm3vnAmjlHqUnoQaTMZYhM4VlXGLkkHHxj/BZ7Td0+SOPKB4RGPboBVKT9htw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  rc-virtual-list@3.14.5:
    resolution: {integrity: sha512-ZMOnkCLv2wUN8Jz7yI4XiSLa9THlYvf00LuMhb1JlsQCewuU7ydPuHw1rGVPhe9VZYl/5UqODtNd7QKJ2DMGfg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  react-abac@0.0.7:
    resolution: {integrity: sha512-zJCBJvpWRsvaWha5ijSyxbrOrKv6sg/xUTqRG2O/ac1u/tfV4zTz2FtfRc0S6jPLneDDszcUUiVs0IItjMYMTw==}
    peerDependencies:
      react: ^16.8.1

  react-dom@16.14.0:
    resolution: {integrity: sha512-1gCeQXDLoIqMgqD3IO2Ah9bnf0w9kzhwN5q4FGnHZ67hBm9yePzB5JJAIQCc8x3pFnNlwFq4RidZggNAAkzWWw==}
    peerDependencies:
      react: ^16.14.0

  react-fast-compare@2.0.4:
    resolution: {integrity: sha512-suNP+J1VU1MWFKcyt7RtjiSWUjvidmQSlqu+eHslq+342xCbGTYmC0mEhPCOHxlW0CywylOC1u2DFAT+bv4dBw==}

  react-hook-form@6.15.8:
    resolution: {integrity: sha512-prq82ofMbnRyj5wqDe8hsTRcdR25jQ+B8KtCS7BLCzjFHAwNuCjRwzPuP4eYLsEBjEIeYd6try+pdLdw0kPkpg==}
    peerDependencies:
      react: ^16.8.0 || ^17

  react-i18next@11.6.0:
    resolution: {integrity: sha512-koyvoDgmY7y7vlbUOVWyoHahbBABfBse9X1vgYFw/WI+CfZwjumZ2/zQGYqLoMx6lEa0c9Lxr9GNP9L3HAJYUg==}
    peerDependencies:
      i18next: '>= 19.0.0'
      react: '>= 16.8.0'

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==}

  react-is@18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==}

  react-lifecycles-compat@3.0.4:
    resolution: {integrity: sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA==}

  react-redux@7.2.0:
    resolution: {integrity: sha512-EvCAZYGfOLqwV7gh849xy9/pt55rJXPwmYvI4lilPM5rUT/1NxuuN59ipdBksRVSvz0KInbPnp4IfoXJXCqiDA==}
    peerDependencies:
      react: ^16.8.3
      react-dom: '*'
      react-native: '*'
      redux: ^2.0.0 || ^3.0.0 || ^4.0.0-0
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true

  react-simple-animate@3.5.2:
    resolution: {integrity: sha512-xLE65euP920QMTOmv5haPlml+hmOPDkbIr5WeF7ADIXWBYt5kW/vwpNfWg8EKMab8aeDxIZ6QjffVh8v2dUyhg==}
    peerDependencies:
      react-dom: ^16.8.0 || ^17 || ^18

  react-split@2.0.14:
    resolution: {integrity: sha512-bKWydgMgaKTg/2JGQnaJPg51T6dmumTWZppFgEbbY0Fbme0F5TuatAScCLaqommbGQQf/ZT1zaejuPDriscISA==}
    peerDependencies:
      react: '*'

  react-test-renderer@16.13.0:
    resolution: {integrity: sha512-NQ2S9gdMUa7rgPGpKGyMcwl1d6D9MCF0lftdI3kts6kkiX+qvpC955jNjAZXlIDTjnN9jwFI8A8XhRh/9v0spA==}
    peerDependencies:
      react: ^16.0.0

  react@16.14.0:
    resolution: {integrity: sha512-0X2CImDkJGApiAlcf0ODKIneSwBPhqJawOa5wCtKbu7ZECrmS26NvtSILynQ66cgkT/RJ4LidJOc3bUESwmU8g==}
    engines: {node: '>=0.10.0'}

  read-babelrc-up@1.1.0:
    resolution: {integrity: sha512-fcl0JeI85Ss3//kfC3z2rsG2VxSiHl1bJgpjQWrne2YuQEewZpAgAjb17A6q/Q3ozWeZsUSroiIBVsnjmOU8vw==}
    engines: {node: '>=10'}

  read-pkg-up@7.0.1:
    resolution: {integrity: sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==}
    engines: {node: '>=8'}

  read-pkg@5.2.0:
    resolution: {integrity: sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==}
    engines: {node: '>=8'}

  readable-stream@1.0.34:
    resolution: {integrity: sha512-ok1qVCJuRkNmvebYikljxJA/UEsKwLl2nI1OmaqAu4/UE+h0wKCHok4XkL/gvi39OacXvw59RJUOFUkDib2rHg==}

  readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}

  readdirp@1.3.0:
    resolution: {integrity: sha512-H1BGeo9VW8nmdwGo64SKRQgNNZwEuqtVUHijOoTDYIpqJGNKU65JaRXL3iqa/8tmVJ9jfoKY+soTznq0cOruTw==}
    engines: {node: '>=0.6'}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  readdirp@4.1.2:
    resolution: {integrity: sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==}
    engines: {node: '>= 14.18.0'}

  recast@0.10.43:
    resolution: {integrity: sha512-GC1g4P336t8WOpzVGFOo83m14xQfHbVqe+eDus+4oubobkWb/kONwMWSG6+K3BUtBOoUdUU+GT9kmNCSOBv9+g==}
    engines: {node: '>= 0.8'}

  recast@0.11.23:
    resolution: {integrity: sha512-+nixG+3NugceyR8O1bLU45qs84JgI3+8EauyRZafLgC9XbdAOIVgwV1Pe2da0YzGo62KzWoZwUpVEQf6qNAXWA==}
    engines: {node: '>= 0.8'}

  redent@3.0.0:
    resolution: {integrity: sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==}
    engines: {node: '>=8'}

  redux-injectors@1.3.0:
    resolution: {integrity: sha512-ZoyKf8Y0bRqpmJImaVO3jnDKuMXTzMDp3j+b0bqtSuPAgWcHD/2P9gRr4mI1EjgCiheIyQ/JJI8yLG29ijqRaw==}
    peerDependencies:
      react: ^16.6.0
      react-dom: ^16.6.0
      react-redux: ^7.1.0

  redux-saga@1.1.3:
    resolution: {integrity: sha512-RkSn/z0mwaSa5/xH/hQLo8gNf4tlvT18qXDNvedihLcfzh+jMchDgaariQoehCpgRltEm4zHKJyINEz6aqswTw==}

  redux-thunk@2.4.2:
    resolution: {integrity: sha512-+P3TjtnP0k/FEjcBL5FZpoovtvrTNT/UXd4/sluaSyrURlSlhLSzEdfsTBW7WsKB6yPvgd7q/iZPICFjW4o57Q==}
    peerDependencies:
      redux: ^4

  redux@4.2.1:
    resolution: {integrity: sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==}

  regenerate@1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}

  regenerator-babel@0.8.10-2:
    resolution: {integrity: sha512-wVTQCiTcgCMlggBnqWUBUWQ6Inrn8CtYHOV/GV55oypz+pSyTckhevFt5VkA9TCIjfxsqze3HCbkVnVCxp9MHw==}
    engines: {node: '>= 0.6'}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.
    hasBin: true

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}

  regexpu@1.3.0:
    resolution: {integrity: sha512-OqpQCTCcVM6k9IbzxLjNN6TRj3NV7qF4L8zUqsNoeAmmIZp8wH1tdZnn0vNXE2tGNU4ho0xTZWk3FmahOtyMRA==}
    hasBin: true

  regjsgen@0.2.0:
    resolution: {integrity: sha512-x+Y3yA24uF68m5GA+tBjbGYo64xXVJpbToBaWCoSNSc1hdk6dfctaRWrNFTVJZIIhL5GxW8zwjoixbnifnK59g==}

  regjsparser@0.1.5:
    resolution: {integrity: sha512-jlQ9gYLfk2p3V5Ag5fYhA7fv7OHzd1KUH0PRP46xc3TgwjwgROIW572AfYg/X9kaNq/LJnu6oJcFRXlIrGoTRw==}
    hasBin: true

  repeating@1.1.3:
    resolution: {integrity: sha512-Nh30JLeMHdoI+AsQ5eblhZ7YlTsM9wiJQe/AHIunlK3KWzvXhXb36IJ7K1IOeRjIOtzMjdUHjwXUFxKJoPTSOg==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}

  requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}

  reselect@4.1.8:
    resolution: {integrity: sha512-ab9EmR80F/zQTMNeneUr4cv+jSwPJgIlvEmVwLerwrWVbpLlBuls9XHzIeTFy4cegU2NHBp3va0LKOzU5qFEYQ==}

  resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve@1.22.8:
    resolution: {integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==}
    hasBin: true

  restore-cursor@3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}

  restore-cursor@5.1.0:
    resolution: {integrity: sha512-oMA2dcrw6u0YfxJQXm342bFKX/E4sG9rbTzO9ptUcR/e8A33cHuvStiYOwH7fszkZlZ1z/ta9AAoPk2F4qIOHA==}
    engines: {node: '>=18'}

  ret@0.2.2:
    resolution: {integrity: sha512-M0b3YWQs7R3Z917WRQy1HHA7Ba7D8hvZg6UE5mLykJxQVE2ju0IXbGlaHPPlkY+WN7wFP+wUMXmBFA0aV6vYGQ==}
    engines: {node: '>=4'}

  reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.4.1:
    resolution: {integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==}

  rollup@4.34.4:
    resolution: {integrity: sha512-spF66xoyD7rz3o08sHP7wogp1gZ6itSq22SGa/IZTcUDXDlOyrShwMwkVSB+BUxFRZZCUYqdb3KWDEOMVQZxuw==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  rrweb-cssom@0.8.0:
    resolution: {integrity: sha512-guoltQEx+9aMf2gDZ0s62EcV8lsXR+0w8915TC3ITdn2YueuNjdAYh/levpU9nFaoChh9RUS5ZdQMrKfVEN9tw==}

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  runed@0.23.4:
    resolution: {integrity: sha512-9q8oUiBYeXIDLWNK5DfCWlkL0EW3oGbk845VdKlPeia28l751VpfesaB/+7pI6rnbx1I6rqoZ2fZxptOJLxILA==}
    peerDependencies:
      svelte: ^5.7.0

  sade@1.8.1:
    resolution: {integrity: sha512-xal3CZX1Xlo/k4ApwCFrHVACi9fBqJ7V+mwhBsuf/1IOKbBy098Fex+Wa/5QMubw09pSZ/u8EY8PWgevJsXp1A==}
    engines: {node: '>=6'}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  sanitize.css@11.0.0:
    resolution: {integrity: sha512-Ox0X2lk0kOGeODJgT9S9HFv0j5Cz89ir9ILylj62/vejHPdMmahmetfocoQwyiAnseeXyDa+KIbO6ZQJe5n2Lg==}

  sass@1.77.6:
    resolution: {integrity: sha512-ByXE1oLD79GVq9Ht1PeHWCPMPB8XHpBuz1r85oByKHjZY6qV6rWnQovQzXJXuQ/XyE1Oj3iPk3lo28uzaRA2/Q==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  sax@1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==}

  saxes@6.0.0:
    resolution: {integrity: sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==}
    engines: {node: '>=v12.22.7'}

  scheduler@0.19.1:
    resolution: {integrity: sha512-n/zwRWRYSUj0/3g/otKDRPMh6qv2SYMWNq85IEa8iZyAv8od9zDYpGSnpBEjNgcMNq6Scbu5KfIPxNF72R/2EA==}

  schema-utils@2.7.1:
    resolution: {integrity: sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg==}
    engines: {node: '>= 8.9.0'}

  scroll-into-view-if-needed@2.2.31:
    resolution: {integrity: sha512-dGCXy99wZQivjmjIqihaBQNjryrz5rueJY7eHfTdyWEiR4ttYpsajb14rn9s5d4DY4EcY6+4+U/maARBXJedkA==}

  semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.5.4:
    resolution: {integrity: sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==}
    engines: {node: '>=10'}
    hasBin: true

  semver@7.6.3:
    resolution: {integrity: sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==}
    engines: {node: '>=10'}
    hasBin: true

  set-cookie-parser@2.6.0:
    resolution: {integrity: sha512-RVnVQxTXuerk653XfuliOxBP81Sf0+qfQE73LIYKcyMYHG94AuH0kgrQpRDuTZnSmjpysHmzxJXKNfa6PjFhyQ==}

  shallowequal@1.1.0:
    resolution: {integrity: sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@1.0.0:
    resolution: {integrity: sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==}
    engines: {node: '>=0.10.0'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  siginfo@2.0.0:
    resolution: {integrity: sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g==}

  sigmund@1.0.1:
    resolution: {integrity: sha512-fCvEXfh6NWpm+YSuY2bpXb/VIihqWA6hLsgboC+0nl71Q7N7o2eaCW8mJa/NLvQhs6jpd3VZV4UiUQlV6+lc8g==}

  signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  sirv@3.0.1:
    resolution: {integrity: sha512-FoqMu0NCGBLCcAkS1qA+XJIQTR6/JHfQXl+uGteNCQ76T91DMUjPa9xfmeqMY3z80nLSg9yQmNjK0Px6RWsH/A==}
    engines: {node: '>=18'}

  slash@1.0.0:
    resolution: {integrity: sha512-3TYDR7xWt4dIqV2JauJr+EJeW356RXijHeUlO+8djJ+uBXPn8/2dpzBc8yQhh583sVvc9CvFAeQVgijsH+PNNg==}
    engines: {node: '>=0.10.0'}

  slice-ansi@5.0.0:
    resolution: {integrity: sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==}
    engines: {node: '>=12'}

  slice-ansi@7.1.0:
    resolution: {integrity: sha512-bSiSngZ/jWeX93BqeIAbImyTbEihizcwNjFoRUIY/T1wWQsfsm2Vw1agPKylXvQTU7iASGdHhyqRlqQzfz+Htg==}
    engines: {node: '>=18'}

  smol-toml@1.3.1:
    resolution: {integrity: sha512-tEYNll18pPKHroYSmLLrksq233j021G0giwW7P3D24jC54pQ5W5BXMsQ/Mvw1OJCmEYDgY+lrzT+3nNUtoNfXQ==}
    engines: {node: '>= 18'}

  sort-keys@4.2.0:
    resolution: {integrity: sha512-aUYIEU/UviqPgc8mHR6IW1EGxkAXpeRETYcrzg8cLAvUPZcpAlleSXHV2mY7G12GphSH6Gzv+4MMVSSkbdteHg==}
    engines: {node: '>=8'}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  source-map-support@0.2.10:
    resolution: {integrity: sha512-gGKOSat73z0V8wBKo9AGxZZyekczBireh1hHktbt+kb9acsCB5OfVCF2DCWlztcQ3r5oNN7f2BL0B2xOcoJ/DQ==}

  source-map-to-comment@1.1.0:
    resolution: {integrity: sha512-kHe3acPFQtLZTfMw/FohyTKA7wyYTLAE5X9S2ynVZzRNHiHR+cxk6vft/DxrJo9v4R002h6gh4Pptb3T8Q9Axw==}
    engines: {node: '>=0.10.0'}

  source-map@0.1.32:
    resolution: {integrity: sha512-htQyLrrRLkQ87Zfrir4/yN+vAUd6DNjVayEjTSHXu29AYQJw57I4/xEL/M6p6E/woPNJwvZt6rVlzc7gFEJccQ==}
    engines: {node: '>=0.8.0'}

  source-map@0.1.43:
    resolution: {integrity: sha512-VtCvB9SIQhk3aF6h+N85EaqIaBFIAfZ9Cu+NJHHVvc8BbEcnvDcFw6sqQ2dQrT6SlOrZq3tIvyD9+EGq/lJryQ==}
    engines: {node: '>=0.8.0'}

  source-map@0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==}
    engines: {node: '>=0.10.0'}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  spare-bones@0.2.0:
    resolution: {integrity: sha512-Y4Jilgqqj/D6JD4j3ts7w1Ackc3CvvctCdTQGwR4C6WggqAw3MWSgcSFUyICTVBeiXmhJa4ZmhoYt/8Zp0D9eA==}
    peerDependencies:
      svelte: ^5.0.0

  spdx-correct@3.2.0:
    resolution: {integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==}

  spdx-exceptions@2.5.0:
    resolution: {integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==}

  spdx-expression-parse@3.0.1:
    resolution: {integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==}

  spdx-license-ids@3.0.18:
    resolution: {integrity: sha512-xxRs31BqRYHwiMzudOrpSiHtZ8i/GeionCBDSilhYRj+9gIcI8wCZTlXZKu9vZIVqViP3dcp9qE5G6AlIaD+TQ==}

  split.js@1.6.5:
    resolution: {integrity: sha512-mPTnGCiS/RiuTNsVhCm9De9cCAUsrNFFviRbADdKiiV+Kk8HKp/0fWu7Kr8pi3/yBmsqLFHuXGT9UUZ+CNLwFw==}

  sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}

  stackback@0.0.2:
    resolution: {integrity: sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw==}

  stackframe@0.3.1:
    resolution: {integrity: sha512-XmoiF4T5nuWEp2x2w92WdGjdHGY/cZa6LIbRsDRQR/Xlk4uW0PAUlH1zJYVffocwKpCdwyuypIp25xsSXEtZHw==}

  std-env@3.8.0:
    resolution: {integrity: sha512-Bc3YwwCB+OzldMxOXJIIvC6cPRWr/LxOp48CdQTOkPyk/t4JWWJbrilwBd7RJzKV8QW7tJkcgAmeuLLJugl5/w==}

  string-argv@0.3.2:
    resolution: {integrity: sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q==}
    engines: {node: '>=0.6.19'}

  string-convert@0.2.1:
    resolution: {integrity: sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A==}

  string-ts@2.2.1:
    resolution: {integrity: sha512-Q2u0gko67PLLhbte5HmPfdOjNvUKbKQM+mCNQae6jE91DmoFHY6HH9GcdqCeNx87DZ2KKjiFxmA0R/42OneGWw==}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  string-width@7.2.0:
    resolution: {integrity: sha512-tsaTIkKW9b4N+AEj+SVA+WhJzV7/zMhcSu78mLKWSk7cXMOSHsBKFWUs0fWwq8QyK3MgJBQRX6Gbi4kYbdvGkQ==}
    engines: {node: '>=18'}

  string_decoder@0.10.31:
    resolution: {integrity: sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==}

  string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}

  strip-ansi@0.3.0:
    resolution: {integrity: sha512-DerhZL7j6i6/nEnVG0qViKXI0OKouvvpsAiaj7c+LfqZZZxdwZtv8+UiA/w4VUJpT8UzX0pR1dcHOii1GbmruQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  strip-ansi@3.0.1:
    resolution: {integrity: sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==}
    engines: {node: '>=0.10.0'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}

  strip-bom@4.0.0:
    resolution: {integrity: sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==}
    engines: {node: '>=8'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}

  strip-final-newline@3.0.0:
    resolution: {integrity: sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==}
    engines: {node: '>=12'}

  strip-indent@3.0.0:
    resolution: {integrity: sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==}
    engines: {node: '>=8'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  strip-json-comments@5.0.1:
    resolution: {integrity: sha512-0fk9zBqO67Nq5M/m45qHCJxylV/DhBlIOVExqgOMiCCrzrhU6tCibRXNqE3jwJLftzE9SNuZtYbpzcO+i9FiKw==}
    engines: {node: '>=14.16'}

  style-to-object@1.0.8:
    resolution: {integrity: sha512-xT47I/Eo0rwJmaXC4oilDGDWLohVhR6o/xAQcPQN8q6QBuZVL8qMYL85kLmST5cPjAorwvqIA4qXTRQoYHaL6g==}

  supports-color@0.2.0:
    resolution: {integrity: sha512-tdCZ28MnM7k7cJDJc7Eq80A9CsRFAAOZUy41npOZCs++qSjfIy7o5Rh46CBk+Dk5FbKJ33X3Tqg4YrV07N5RaA==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  supports-color@2.0.0:
    resolution: {integrity: sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==}
    engines: {node: '>=0.8.0'}

  supports-color@3.2.3:
    resolution: {integrity: sha512-Jds2VIYDrlp5ui7t8abHN2bjAu4LV/q4N2KivFPpGH0lrka0BMq/33AmECUXlKPcHigkNaqfXRENFju+rlcy+A==}
    engines: {node: '>=0.8.0'}

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  svelte-check@4.2.1:
    resolution: {integrity: sha512-e49SU1RStvQhoipkQ/aonDhHnG3qxHSBtNfBRb9pxVXoa+N7qybAo32KgA9wEb2PCYFNaDg7bZCdhLD1vHpdYA==}
    engines: {node: '>= 18.0.0'}
    hasBin: true
    peerDependencies:
      svelte: ^4.0.0 || ^5.0.0-next.0
      typescript: '>=5.0.0'

  svelte-eslint-parser@1.2.0:
    resolution: {integrity: sha512-mbPtajIeuiyU80BEyGvwAktBeTX7KCr5/0l+uRGLq1dafwRNrjfM5kHGJScEBlPG3ipu6dJqfW/k0/fujvIEVw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      svelte: ^3.37.0 || ^4.0.0 || ^5.0.0
    peerDependenciesMeta:
      svelte:
        optional: true

  svelte-toolbelt@0.7.1:
    resolution: {integrity: sha512-HcBOcR17Vx9bjaOceUvxkY3nGmbBmCBBbuWLLEWO6jtmWH8f/QoWmbyUfQZrpDINH39en1b8mptfPQT9VKQ1xQ==}
    engines: {node: '>=18', pnpm: '>=8.7.0'}
    peerDependencies:
      svelte: ^5.0.0

  svelte@5.32.0:
    resolution: {integrity: sha512-2WXcm+mx4D99pb5gvGYiEC6EkPOfzCcgXxcVrxMkAythwzYH5Frr29i3C431U4B8LxXRh9WnFPAz+OzIcFdM7g==}
    engines: {node: '>=18'}

  symbol-tree@3.2.4:
    resolution: {integrity: sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==}

  synchronous-promise@2.0.17:
    resolution: {integrity: sha512-AsS729u2RHUfEra9xJrE39peJcc2stq2+poBXX8bcM08Y6g9j/i/PUzwNQqkaJde7Ntg1TO7bSREbR5sdosQ+g==}

  tabbable@6.2.0:
    resolution: {integrity: sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==}

  tailwindcss@4.0.9:
    resolution: {integrity: sha512-12laZu+fv1ONDRoNR9ipTOpUD7RN9essRVkX36sjxuRUInpN7hIiHN4lBd/SIFjbISvnXzp8h/hXzmU8SQQYhw==}

  tapable@2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==}
    engines: {node: '>=6'}

  test-exclude@7.0.1:
    resolution: {integrity: sha512-pFYqmTw68LXVjeWJMST4+borgQP2AyMNbg1BpZh9LbyhUeNkeaPF9gzfPGUAnSMV3qPYdWUwDIjjCLiSDOl7vg==}
    engines: {node: '>=18'}

  through2@0.6.5:
    resolution: {integrity: sha512-RkK/CCESdTKQZHdmKICijdKKsCRVHs5KsLZ6pACAmF/1GPUQhonHSXWNERctxEp7RmvjdNbZTL5z9V7nSCXKcg==}

  through@2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==}

  tiny-warning@1.0.3:
    resolution: {integrity: sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==}

  tinybench@2.9.0:
    resolution: {integrity: sha512-0+DUvqWMValLmha6lr4kD8iAMK1HzV0/aKnCtWb9v9641TnP/MFb7Pc2bxoxQjTXAErryXVgUOfv2YqNllqGeg==}

  tinycolor2@1.6.0:
    resolution: {integrity: sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==}

  tinyexec@0.3.2:
    resolution: {integrity: sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA==}

  tinypool@1.0.2:
    resolution: {integrity: sha512-al6n+QEANGFOMf/dmUMsuS5/r9B06uwlyNjZZql/zv8J7ybHCgoihBNORZCY2mzUuAnomQa2JdhyHKzZxPCrFA==}
    engines: {node: ^18.0.0 || >=20.0.0}

  tinyrainbow@2.0.0:
    resolution: {integrity: sha512-op4nsTR47R6p0vMUUoYl/a+ljLFVtlfaXkLQmqfLR1qHma1h/ysYk4hEXZ880bf2CYgTskvTa/e196Vd5dDQXw==}
    engines: {node: '>=14.0.0'}

  tinyspy@3.0.2:
    resolution: {integrity: sha512-n1cw8k1k0x4pgA2+9XrOkFydTerNcJ1zWCO5Nn9scWHTD+5tp8dghT2x1uduQePZTZgd3Tupf+x9BxJjeJi77Q==}
    engines: {node: '>=14.0.0'}

  tldts-core@6.1.76:
    resolution: {integrity: sha512-uzhJ02RaMzgQR3yPoeE65DrcHI6LoM4saUqXOt/b5hmb3+mc4YWpdSeAQqVqRUlQ14q8ZuLRWyBR1ictK1dzzg==}

  tldts@6.1.76:
    resolution: {integrity: sha512-6U2ti64/nppsDxQs9hw8ephA3nO6nSQvVVfxwRw8wLQPFtLI1cFI1a1eP22g+LUP+1TA2pKKjUTwWB+K2coqmQ==}
    hasBin: true

  to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toggle-selection@1.0.6:
    resolution: {integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==}

  toposort@2.0.2:
    resolution: {integrity: sha512-0a5EOkAUp8D4moMi2W8ZF8jcga7BgZd91O/yabJCFY8az+XSzeGyTKs0Aoo897iV1Nj6guFq8orWDS96z91oGg==}

  totalist@3.0.1:
    resolution: {integrity: sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==}
    engines: {node: '>=6'}

  tough-cookie@4.1.4:
    resolution: {integrity: sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==}
    engines: {node: '>=6'}

  tough-cookie@5.1.0:
    resolution: {integrity: sha512-rvZUv+7MoBYTiDmFPBrhL7Ujx9Sk+q9wwm22x8c8T5IJaR+Wsyc7TNxbVxo84kZoRJZZMazowFLqpankBEQrGg==}
    engines: {node: '>=16'}

  tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  tr46@5.0.0:
    resolution: {integrity: sha512-tk2G5R2KRwBd+ZN0zaEXpmzdKyOYksXwywulIX95MBODjSzMIuQnQ3m8JxgbhnL1LeVo7lqQKsYa1O3Htl7K5g==}
    engines: {node: '>=18'}

  trim-newlines@3.0.1:
    resolution: {integrity: sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw==}
    engines: {node: '>=8'}

  trim-right@1.0.1:
    resolution: {integrity: sha512-WZGXGstmCWgeevgTL54hrCuw1dyMQIzWy7ZfqRJfSmJZBwklI15egmQytFP6bPidmw3M8d5yEowl1niq4vmqZw==}
    engines: {node: '>=0.10.0'}

  ts-api-utils@2.0.1:
    resolution: {integrity: sha512-dnlgjFSVetynI8nzgJ+qF62efpglpWRk8isUEWZGWlJYySCTD6aKvbUDu+zbPeDakk3bg5H4XpitHukgfL1m9w==}
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'

  ts-declaration-location@1.0.5:
    resolution: {integrity: sha512-WqmlO9IoeYwCqJ2E9kHMcY9GZhhfLYItC3VnHDlPOrg6nNdUWS4wn4hhDZUPt60m1EvtjPIZyprTjpI992Bgzw==}
    peerDependencies:
      typescript: '>=4.0.0'

  ts-pattern@5.6.2:
    resolution: {integrity: sha512-d4IxJUXROL5NCa3amvMg6VQW2HVtZYmUTPfvVtO7zJWGYLJ+mry9v2OmYm+z67aniQoQ8/yFNadiEwtNS9qQiw==}

  tsconfck@3.1.3:
    resolution: {integrity: sha512-ulNZP1SVpRDesxeMLON/LtWM8HIgAJEIVpVVhBM6gsmvQ8+Rh+ZG7FWGvHh7Ah3pRABwVJWklWCr/BTZSv0xnQ==}
    engines: {node: ^18 || >=20}
    hasBin: true
    peerDependencies:
      typescript: ^5.0.0
    peerDependenciesMeta:
      typescript:
        optional: true

  tslib@2.6.3:
    resolution: {integrity: sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  type-fest@0.13.1:
    resolution: {integrity: sha512-34R7HTnG0XIJcBSn5XhDd7nNFPRcXYRZrBB2O2jdKqYODldSzBAqzsWoZYYvduky73toYS/ESqxPvkDf/F0XMg==}
    engines: {node: '>=10'}

  type-fest@0.6.0:
    resolution: {integrity: sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==}
    engines: {node: '>=8'}

  type-fest@0.8.1:
    resolution: {integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==}
    engines: {node: '>=8'}

  typed-redux-saga@1.5.0:
    resolution: {integrity: sha512-XHKliNtRNUegYAAztbVDb5Q+FMqYNQPaed6Xq2N8kz8AOmiOCVxW3uIj7TEptR1/ms6M9u3HEDfJr4qqz/PYrw==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      redux-saga: ^1.1.3

  typedarray-to-buffer@3.1.5:
    resolution: {integrity: sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==}

  typescript-compare@0.0.2:
    resolution: {integrity: sha512-8ja4j7pMHkfLJQO2/8tut7ub+J3Lw2S3061eJLFQcvs3tsmJKp8KG5NtpLn7KcY2w08edF74BSVN7qJS0U6oHA==}

  typescript-eslint@8.23.0:
    resolution: {integrity: sha512-/LBRo3HrXr5LxmrdYSOCvoAMm7p2jNizNfbIpCgvG4HMsnoprRUOce/+8VJ9BDYWW68rqIENE/haVLWPeFZBVQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.8.0'

  typescript-logic@0.0.0:
    resolution: {integrity: sha512-zXFars5LUkI3zP492ls0VskH3TtdeHCqu0i7/duGt60i5IGPIpAHE/DWo5FqJ6EjQ15YKXrt+AETjv60Dat34Q==}

  typescript-strict-plugin@2.4.4:
    resolution: {integrity: sha512-OXcWHQk+pW9gqEL/Mb1eTgj/Yiqk1oHBERr9v4VInPOYN++p+cXejmQK/h/VlUPGD++FXQ8pgiqVMyEtxU4T6A==}
    hasBin: true

  typescript-tuple@2.2.1:
    resolution: {integrity: sha512-Zcr0lbt8z5ZdEzERHAMAniTiIKerFCMgd7yjq1fPnDJ43et/k9twIFQMUYff9k5oXcsQ0WpvFcgzK2ZKASoW6Q==}

  typescript@4.9.5:
    resolution: {integrity: sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==}
    engines: {node: '>=4.2.0'}
    hasBin: true

  typescript@5.5.2:
    resolution: {integrity: sha512-NcRtPEOsPFFWjobJEtfihkLCZCXZt/os3zf8nTxjVH3RvTSxjrCamJpbExGvYOF+tFHc3pA65qpdwPbzjohhew==}
    engines: {node: '>=14.17'}
    hasBin: true

  typescript@5.8.2:
    resolution: {integrity: sha512-aJn6wq13/afZp/jT9QZmwEjDqqvSGp1VT5GVg+f/t6/oVyrgXM6BY1h9BRh/O5p3PlUPAe+WuiEZOmb/49RqoQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  ufo@1.5.4:
    resolution: {integrity: sha512-UsUk3byDzKd04EyoZ7U4DOlxQaD14JUKQl6/P7wiX4FNvUfm3XL246n9W5AmqwW5RSFJ27NAuM0iLscAOYUiGQ==}

  undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}

  universalify@0.2.0:
    resolution: {integrity: sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==}
    engines: {node: '>= 4.0.0'}

  universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}

  unplugin@1.0.1:
    resolution: {integrity: sha512-aqrHaVBWW1JVKBHmGo33T5TxeL0qWzfvjWokObHA9bYmN7eNDkwOxmLjhioHl9878qDFMAaT51XNroRyuz7WxA==}

  update-browserslist-db@1.1.0:
    resolution: {integrity: sha512-EdRAaAyk2cUE1wOf2DkEhzxqOQvFOoRJFNS6NeyJ01Gp2beMRpBAINjM2iDXE3KCuKhwnvHIQCJm6ThL2Z+HzQ==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  url-parse@1.5.10:
    resolution: {integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==}

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  uuid@11.1.0:
    resolution: {integrity: sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==}
    hasBin: true

  uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true

  validate-npm-package-license@3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==}

  vite-node@3.0.5:
    resolution: {integrity: sha512-02JEJl7SbtwSDJdYS537nU6l+ktdvcREfLksk/NDAqtdKWGqHl+joXzEubHROmS3E6pip+Xgu2tFezMu75jH7A==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true

  vite-plugin-devtools-json@0.1.0:
    resolution: {integrity: sha512-KvdgPBUAAhwnpOgXmJhs6KI4/IPn6xUppLGm20D0Uvp/doZ9TpTYYfzSHX0TgvdsSlTAwzZfzDse+ujGskp68g==}
    peerDependencies:
      vite: ^2.7.0 || ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0

  vite-plugin-dts@4.5.3:
    resolution: {integrity: sha512-P64VnD00dR+e8S26ESoFELqc17+w7pKkwlBpgXteOljFyT0zDwD8hH4zXp49M/kciy//7ZbVXIwQCekBJjfWzA==}
    peerDependencies:
      typescript: '*'
      vite: '*'
    peerDependenciesMeta:
      vite:
        optional: true

  vite-tsconfig-paths@5.1.4:
    resolution: {integrity: sha512-cYj0LRuLV2c2sMqhqhGpaO3LretdtMn/BVX4cPLanIZuwwrkVl+lK84E/miEXkCHWXuq65rhNN4rXsBcOB3S4w==}
    peerDependencies:
      vite: '*'
    peerDependenciesMeta:
      vite:
        optional: true

  vite@6.1.0:
    resolution: {integrity: sha512-RjjMipCKVoR4hVfPY6GQTgveinjNuyLw+qruksLDvA5ktI1150VmcMBKmQaEWJhg/j6Uaf6dNCNA0AfdzUb/hQ==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  vitefu@1.0.6:
    resolution: {integrity: sha512-+Rex1GlappUyNN6UfwbVZne/9cYC4+R2XDk9xkNXBKMw6HQagdX9PgZ8V2v1WUSK1wfBLp7qbI1+XSNIlB1xmA==}
    peerDependencies:
      vite: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0
    peerDependenciesMeta:
      vite:
        optional: true

  vitest@3.0.5:
    resolution: {integrity: sha512-4dof+HvqONw9bvsYxtkfUp2uHsTN9bV2CZIi1pWgoFpL1Lld8LA1ka9q/ONSsoScAKG7NVGf2stJTI7XRkXb2Q==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@edge-runtime/vm': '*'
      '@types/debug': ^4.1.12
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      '@vitest/browser': 3.0.5
      '@vitest/ui': 3.0.5
      happy-dom: '*'
      jsdom: '*'
    peerDependenciesMeta:
      '@edge-runtime/vm':
        optional: true
      '@types/debug':
        optional: true
      '@types/node':
        optional: true
      '@vitest/browser':
        optional: true
      '@vitest/ui':
        optional: true
      happy-dom:
        optional: true
      jsdom:
        optional: true

  void-elements@2.0.1:
    resolution: {integrity: sha512-qZKX4RnBzH2ugr8Lxa7x+0V6XD9Sb/ouARtiasEQCHB1EVU4NXtmHsDDrx1dO4ne5fc3J6EW05BP1Dl0z0iung==}
    engines: {node: '>=0.10.0'}

  vscode-uri@3.1.0:
    resolution: {integrity: sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==}

  w3c-xmlserializer@5.0.0:
    resolution: {integrity: sha512-o8qghlI8NZHU1lLPrpi2+Uq7abh4GGPpYANlalzWxyWteJOCsr/P+oPBA49TOLu5FTZO4d3F9MnWJfiMo4BkmA==}
    engines: {node: '>=18'}

  warning@4.0.3:
    resolution: {integrity: sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==}

  wcwidth@1.0.1:
    resolution: {integrity: sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==}

  web-vitals@4.2.4:
    resolution: {integrity: sha512-r4DIlprAGwJ7YM11VZp4R884m0Vmgr6EAKe3P+kO0PPj3Unqyvv59rczf6UiGcb9Z8QxZVcqKNwv/g0WNdWwsw==}

  webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  webidl-conversions@7.0.0:
    resolution: {integrity: sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==}
    engines: {node: '>=12'}

  webpack-sources@3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==}
    engines: {node: '>=10.13.0'}

  webpack-virtual-modules@0.5.0:
    resolution: {integrity: sha512-kyDivFZ7ZM0BVOUteVbDFhlRt7Ah/CSPwJdi8hBpkK7QLumUqdLtVfm/PX/hkcnrvr0i77fO5+TjZ94Pe+C9iw==}

  whatwg-encoding@3.1.1:
    resolution: {integrity: sha512-6qN4hJdMwfYBtE3YBTTHhoeuUrDBPZmbQaxWAqSALV/MeEnR5z1xd8UKud2RAkFoPkmB+hli1TZSnyi84xz1vQ==}
    engines: {node: '>=18'}

  whatwg-mimetype@4.0.0:
    resolution: {integrity: sha512-QaKxh0eNIi2mE9p2vEdzfagOKHCcj1pJ56EEHGQOVxp8r9/iszLUUV7v89x9O1p/T+NlTM5W7jW6+cz4Fq1YVg==}
    engines: {node: '>=18'}

  whatwg-url@14.1.0:
    resolution: {integrity: sha512-jlf/foYIKywAt3x/XWKZ/3rz8OSJPiWktjmk891alJUEjiVxKX9LEO92qH3hv4aJ0mN3MWPvGMCy8jQi95xK4w==}
    engines: {node: '>=18'}

  whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  why-is-node-running@2.3.0:
    resolution: {integrity: sha512-hUrmaWBdVDcxvYqnyh09zunKzROWjbZTiNy8dBEjkS7ehEDQibXJ7XvlmtbwuTclUiIyN+CyXQD4Vmko8fNm8w==}
    engines: {node: '>=8'}
    hasBin: true

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}

  wrap-ansi@9.0.0:
    resolution: {integrity: sha512-G8ura3S+3Z2G+mkgNRq8dqaFZAuxfsxpBB8OCTGRTCtp+l/v9nbFNmCUP1BZMts3G1142MsZfn6eeUKrr4PD1Q==}
    engines: {node: '>=18'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  write-file-atomic@3.0.3:
    resolution: {integrity: sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==}

  write-json-file@4.3.0:
    resolution: {integrity: sha512-PxiShnxf0IlnQuMYOPPhPkhExoCQuTUNPOa/2JWCYTmBquU9njyyDuwRKN26IZBlp4yn1nt+Agh2HOOBl+55HQ==}
    engines: {node: '>=8.3'}

  ws@7.5.10:
    resolution: {integrity: sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@8.18.0:
    resolution: {integrity: sha512-8VbfWfHLbbwu3+N6OKsOMpBdT4kXPDDB9cJk2bJ6mh9ucxdlnNvH1e+roYkKmN9Nxw2yjz7VzeO9oOz2zJ04Pw==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xml-name-validator@5.0.0:
    resolution: {integrity: sha512-EvGK8EJ3DhaHfbRlETOWAS5pO9MZITeauHKJyb8wyajUfQUenkIg2MvLDTZ4T/TgIcm3HU0TFBgWWboAZ30UHg==}
    engines: {node: '>=18'}

  xmlchars@2.2.0:
    resolution: {integrity: sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==}

  xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}

  y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

  yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}

  yaml@2.7.0:
    resolution: {integrity: sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==}
    engines: {node: '>= 14'}
    hasBin: true

  yargs-parser@18.1.3:
    resolution: {integrity: sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==}
    engines: {node: '>=6'}

  yargs-parser@20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==}
    engines: {node: '>=10'}

  yargs@16.2.0:
    resolution: {integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==}
    engines: {node: '>=10'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

  yup@0.29.1:
    resolution: {integrity: sha512-U7mPIbgfQWI6M3hZCJdGFrr+U0laG28FxMAKIgNvgl7OtyYuUoc4uy9qCWYHZjh49b8T7Ug8NNDdiMIEytcXrQ==}

  zimmerframe@1.1.2:
    resolution: {integrity: sha512-rAbqEGa8ovJy4pyBxZM70hg4pE6gDgaQ0Sl9M3enG3I0d6H4XSAM3GeNGLKnsBpuijUow064sf7ww1nutC5/3w==}

  zod-fixture@2.5.2:
    resolution: {integrity: sha512-JipX1OVrKA3QSdx/k29sK9zocFM8mvdFHu8Wt0htrJ5ZlE8vcmnNfT9iKXMjjc3Hqy8btWtPyP4e03D0IxpVxg==}
    peerDependencies:
      zod: '>=3.0.0'

  zod-validation-error@3.3.0:
    resolution: {integrity: sha512-Syib9oumw1NTqEv4LT0e6U83Td9aVRk9iTXPUQr1otyV1PuXQKOvOwhMNqZIq5hluzHP2pMgnOmHEo7kPdI2mw==}
    engines: {node: '>=18.0.0'}
    peerDependencies:
      zod: ^3.18.0

  zod@3.24.2:
    resolution: {integrity: sha512-lY7CDW43ECgW9u1TcT3IoXHflywfVqDYze4waEz812jR/bZ8FHDsl7pFQoSZTz5N+2NqRXs8GBwnAwo3ZNxqhQ==}

snapshots:

  '@adobe/css-tools@4.4.0': {}

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25

  '@ant-design/colors@4.0.5':
    dependencies:
      tinycolor2: 1.6.0

  '@ant-design/colors@6.0.0':
    dependencies:
      '@ctrl/tinycolor': 3.6.1

  '@ant-design/css-animation@1.7.3': {}

  '@ant-design/icons-svg@4.4.2': {}

  '@ant-design/icons@4.8.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@ant-design/colors': 6.0.0
      '@ant-design/icons-svg': 4.4.2
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      lodash: 4.17.21
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  '@ant-design/react-slick@0.27.14(react-dom@16.14.0(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      json2mq: 0.2.0
      lodash: 4.17.21
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      resize-observer-polyfill: 1.5.1

  '@asamuzakjp/css-color@2.8.3':
    dependencies:
      '@csstools/css-calc': 2.1.1(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-color-parser': 3.0.7(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-parser-algorithms': 3.0.4(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-tokenizer': 3.0.3
      lru-cache: 10.4.3

  '@babel/code-frame@7.24.7':
    dependencies:
      '@babel/highlight': 7.24.7
      picocolors: 1.1.1

  '@babel/compat-data@7.25.4': {}

  '@babel/core@7.24.7':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.24.7
      '@babel/generator': 7.25.6
      '@babel/helper-compilation-targets': 7.25.2
      '@babel/helper-module-transforms': 7.25.2(@babel/core@7.24.7)
      '@babel/helpers': 7.24.7
      '@babel/parser': 7.25.6
      '@babel/template': 7.25.0
      '@babel/traverse': 7.25.6
      '@babel/types': 7.25.6
      convert-source-map: 2.0.0
      debug: 4.4.0
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.25.6':
    dependencies:
      '@babel/types': 7.25.6
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 2.5.2

  '@babel/helper-compilation-targets@7.25.2':
    dependencies:
      '@babel/compat-data': 7.25.4
      '@babel/helper-validator-option': 7.24.8
      browserslist: 4.23.3
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-module-imports@7.24.7':
    dependencies:
      '@babel/traverse': 7.25.6
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.25.2(@babel/core@7.24.7)':
    dependencies:
      '@babel/core': 7.24.7
      '@babel/helper-module-imports': 7.24.7
      '@babel/helper-simple-access': 7.24.7
      '@babel/helper-validator-identifier': 7.24.7
      '@babel/traverse': 7.25.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-plugin-utils@7.24.8': {}

  '@babel/helper-simple-access@7.24.7':
    dependencies:
      '@babel/traverse': 7.25.6
      '@babel/types': 7.25.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.24.8': {}

  '@babel/helper-validator-identifier@7.24.7': {}

  '@babel/helper-validator-option@7.24.8': {}

  '@babel/helpers@7.24.7':
    dependencies:
      '@babel/template': 7.25.0
      '@babel/types': 7.25.6

  '@babel/highlight@7.24.7':
    dependencies:
      '@babel/helper-validator-identifier': 7.24.7
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/parser@7.25.6':
    dependencies:
      '@babel/types': 7.25.6

  '@babel/runtime-corejs3@7.24.7':
    dependencies:
      core-js-pure: 3.37.1
      regenerator-runtime: 0.14.1

  '@babel/runtime@7.24.7':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/template@7.25.0':
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/parser': 7.25.6
      '@babel/types': 7.25.6

  '@babel/traverse@7.25.6':
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/generator': 7.25.6
      '@babel/parser': 7.25.6
      '@babel/template': 7.25.0
      '@babel/types': 7.25.6
      debug: 4.4.0
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.25.6':
    dependencies:
      '@babel/helper-string-parser': 7.24.8
      '@babel/helper-validator-identifier': 7.24.7
      to-fast-properties: 2.0.0

  '@bcoe/v8-coverage@1.0.2': {}

  '@csstools/color-helpers@5.0.1': {}

  '@csstools/css-calc@2.1.1(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    dependencies:
      '@csstools/css-parser-algorithms': 3.0.4(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-tokenizer': 3.0.3

  '@csstools/css-color-parser@3.0.7(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    dependencies:
      '@csstools/color-helpers': 5.0.1
      '@csstools/css-calc': 2.1.1(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-parser-algorithms': 3.0.4(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-tokenizer': 3.0.3

  '@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3)':
    dependencies:
      '@csstools/css-tokenizer': 3.0.3

  '@csstools/css-tokenizer@3.0.3': {}

  '@ctrl/tinycolor@3.6.1': {}

  '@elastic/apm-rum-core@5.21.1':
    dependencies:
      error-stack-parser: 1.3.6
      opentracing: 0.14.7
      promise-polyfill: 8.3.0

  '@elastic/apm-rum@5.16.0':
    dependencies:
      '@elastic/apm-rum-core': 5.21.1

  '@emotion/cache@10.0.29':
    dependencies:
      '@emotion/sheet': 0.9.4
      '@emotion/stylis': 0.8.5
      '@emotion/utils': 0.11.3
      '@emotion/weak-memoize': 0.2.5

  '@emotion/core@10.3.1(react@16.14.0)':
    dependencies:
      '@babel/runtime': 7.24.7
      '@emotion/cache': 10.0.29
      '@emotion/css': 10.0.27
      '@emotion/serialize': 0.11.16
      '@emotion/sheet': 0.9.4
      '@emotion/utils': 0.11.3
      react: 16.14.0
    transitivePeerDependencies:
      - supports-color

  '@emotion/css@10.0.27':
    dependencies:
      '@emotion/serialize': 0.11.16
      '@emotion/utils': 0.11.3
      babel-plugin-emotion: 10.2.2
    transitivePeerDependencies:
      - supports-color

  '@emotion/hash@0.8.0': {}

  '@emotion/is-prop-valid@0.8.8':
    dependencies:
      '@emotion/memoize': 0.7.4

  '@emotion/memoize@0.7.4': {}

  '@emotion/serialize@0.11.16':
    dependencies:
      '@emotion/hash': 0.8.0
      '@emotion/memoize': 0.7.4
      '@emotion/unitless': 0.7.5
      '@emotion/utils': 0.11.3
      csstype: 2.6.21

  '@emotion/sheet@0.9.4': {}

  '@emotion/styled-base@10.3.0(@emotion/core@10.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@babel/runtime': 7.24.7
      '@emotion/core': 10.3.1(react@16.14.0)
      '@emotion/is-prop-valid': 0.8.8
      '@emotion/serialize': 0.11.16
      '@emotion/utils': 0.11.3
      react: 16.14.0

  '@emotion/styled@10.3.0(@emotion/core@10.3.1(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@emotion/core': 10.3.1(react@16.14.0)
      '@emotion/styled-base': 10.3.0(@emotion/core@10.3.1(react@16.14.0))(react@16.14.0)
      babel-plugin-emotion: 10.2.2
      react: 16.14.0
    transitivePeerDependencies:
      - supports-color

  '@emotion/stylis@0.8.5': {}

  '@emotion/unitless@0.7.5': {}

  '@emotion/utils@0.11.3': {}

  '@emotion/weak-memoize@0.2.5': {}

  '@esbuild/aix-ppc64@0.24.2':
    optional: true

  '@esbuild/android-arm64@0.24.2':
    optional: true

  '@esbuild/android-arm@0.24.2':
    optional: true

  '@esbuild/android-x64@0.24.2':
    optional: true

  '@esbuild/darwin-arm64@0.24.2':
    optional: true

  '@esbuild/darwin-x64@0.24.2':
    optional: true

  '@esbuild/freebsd-arm64@0.24.2':
    optional: true

  '@esbuild/freebsd-x64@0.24.2':
    optional: true

  '@esbuild/linux-arm64@0.24.2':
    optional: true

  '@esbuild/linux-arm@0.24.2':
    optional: true

  '@esbuild/linux-ia32@0.24.2':
    optional: true

  '@esbuild/linux-loong64@0.24.2':
    optional: true

  '@esbuild/linux-mips64el@0.24.2':
    optional: true

  '@esbuild/linux-ppc64@0.24.2':
    optional: true

  '@esbuild/linux-riscv64@0.24.2':
    optional: true

  '@esbuild/linux-s390x@0.24.2':
    optional: true

  '@esbuild/linux-x64@0.24.2':
    optional: true

  '@esbuild/netbsd-arm64@0.24.2':
    optional: true

  '@esbuild/netbsd-x64@0.24.2':
    optional: true

  '@esbuild/openbsd-arm64@0.24.2':
    optional: true

  '@esbuild/openbsd-x64@0.24.2':
    optional: true

  '@esbuild/sunos-x64@0.24.2':
    optional: true

  '@esbuild/win32-arm64@0.24.2':
    optional: true

  '@esbuild/win32-ia32@0.24.2':
    optional: true

  '@esbuild/win32-x64@0.24.2':
    optional: true

  '@eslint-community/eslint-plugin-eslint-comments@4.4.1(eslint@9.19.0(jiti@2.4.2))':
    dependencies:
      escape-string-regexp: 4.0.0
      eslint: 9.19.0(jiti@2.4.2)
      ignore: 5.3.1

  '@eslint-community/eslint-utils@4.4.1(eslint@9.19.0(jiti@2.4.2))':
    dependencies:
      eslint: 9.19.0(jiti@2.4.2)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/eslint-utils@4.7.0(eslint@9.19.0(jiti@2.4.2))':
    dependencies:
      eslint: 9.19.0(jiti@2.4.2)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint-react/ast@1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)':
    dependencies:
      '@eslint-react/eff': 1.26.2
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/typescript-estree': 8.23.0(typescript@5.5.2)
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      string-ts: 2.2.1
      ts-pattern: 5.6.2
    transitivePeerDependencies:
      - eslint
      - supports-color
      - typescript

  '@eslint-react/core@1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)':
    dependencies:
      '@eslint-react/ast': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/eff': 1.26.2
      '@eslint-react/jsx': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/shared': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/var': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/type-utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      birecord: 0.1.1
      ts-pattern: 5.6.2
    transitivePeerDependencies:
      - eslint
      - supports-color
      - typescript

  '@eslint-react/eff@1.26.2': {}

  '@eslint-react/eslint-plugin@1.26.2(eslint@9.19.0(jiti@2.4.2))(ts-api-utils@2.0.1(typescript@5.5.2))(typescript@5.5.2)':
    dependencies:
      '@eslint-react/eff': 1.26.2
      '@eslint-react/shared': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/type-utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      eslint: 9.19.0(jiti@2.4.2)
      eslint-plugin-react-debug: 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      eslint-plugin-react-dom: 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      eslint-plugin-react-hooks-extra: 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      eslint-plugin-react-naming-convention: 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      eslint-plugin-react-web-api: 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      eslint-plugin-react-x: 1.26.2(eslint@9.19.0(jiti@2.4.2))(ts-api-utils@2.0.1(typescript@5.5.2))(typescript@5.5.2)
    optionalDependencies:
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color
      - ts-api-utils

  '@eslint-react/jsx@1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)':
    dependencies:
      '@eslint-react/ast': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/eff': 1.26.2
      '@eslint-react/var': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      ts-pattern: 5.6.2
    transitivePeerDependencies:
      - eslint
      - supports-color
      - typescript

  '@eslint-react/shared@1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)':
    dependencies:
      '@eslint-react/eff': 1.26.2
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      picomatch: 4.0.2
      ts-pattern: 5.6.2
    transitivePeerDependencies:
      - eslint
      - supports-color
      - typescript

  '@eslint-react/var@1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)':
    dependencies:
      '@eslint-react/ast': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/eff': 1.26.2
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      string-ts: 2.2.1
      ts-pattern: 5.6.2
    transitivePeerDependencies:
      - eslint
      - supports-color
      - typescript

  '@eslint/compat@1.2.6(eslint@9.19.0(jiti@2.4.2))':
    optionalDependencies:
      eslint: 9.19.0(jiti@2.4.2)

  '@eslint/config-array@0.19.1':
    dependencies:
      '@eslint/object-schema': 2.1.5
      debug: 4.4.0
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/core@0.10.0':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.2.0':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.0
      espree: 10.3.0
      globals: 14.0.0
      ignore: 5.3.1
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.19.0': {}

  '@eslint/object-schema@2.1.5': {}

  '@eslint/plugin-kit@0.2.5':
    dependencies:
      '@eslint/core': 0.10.0
      levn: 0.4.1

  '@floating-ui/core@1.6.9':
    dependencies:
      '@floating-ui/utils': 0.2.9

  '@floating-ui/core@1.7.0':
    dependencies:
      '@floating-ui/utils': 0.2.9

  '@floating-ui/dom@1.6.13':
    dependencies:
      '@floating-ui/core': 1.6.9
      '@floating-ui/utils': 0.2.9

  '@floating-ui/dom@1.7.0':
    dependencies:
      '@floating-ui/core': 1.7.0
      '@floating-ui/utils': 0.2.9

  '@floating-ui/utils@0.2.9': {}

  '@formatjs/ecma402-abstract@1.4.0':
    dependencies:
      tslib: 2.6.3

  '@formatjs/ecma402-abstract@1.5.0':
    dependencies:
      tslib: 2.6.3

  '@formatjs/ecma402-abstract@2.3.3':
    dependencies:
      '@formatjs/fast-memoize': 2.2.6
      '@formatjs/intl-localematcher': 0.6.0
      decimal.js: 10.5.0
      tslib: 2.6.3

  '@formatjs/fast-memoize@2.2.6':
    dependencies:
      tslib: 2.6.3

  '@formatjs/icu-messageformat-parser@2.11.1':
    dependencies:
      '@formatjs/ecma402-abstract': 2.3.3
      '@formatjs/icu-skeleton-parser': 1.8.13
      tslib: 2.6.3

  '@formatjs/icu-skeleton-parser@1.8.13':
    dependencies:
      '@formatjs/ecma402-abstract': 2.3.3
      tslib: 2.6.3

  '@formatjs/intl-localematcher@0.6.0':
    dependencies:
      tslib: 2.6.3

  '@formatjs/intl-numberformat@5.7.6':
    dependencies:
      '@formatjs/ecma402-abstract': 1.4.0
      tslib: 2.6.3

  '@formatjs/ts-transformer@2.13.0':
    dependencies:
      intl-messageformat-parser: 6.1.2
      tslib: 2.6.3
      typescript: 4.9.5

  '@hookform/devtools@2.2.1(react-dom@16.14.0(react@16.14.0))(react-hook-form@6.15.8(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@emotion/core': 10.3.1(react@16.14.0)
      '@emotion/styled': 10.3.0(@emotion/core@10.3.1(react@16.14.0))(react@16.14.0)
      '@types/lodash': 4.17.15
      little-state-machine: 3.1.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      lodash: 4.17.21
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-hook-form: 6.15.8(react@16.14.0)
      react-simple-animate: 3.5.2(react-dom@16.14.0(react@16.14.0))
    transitivePeerDependencies:
      - supports-color

  '@hookform/resolvers@1.3.8(react-hook-form@6.15.8(react@16.14.0))':
    dependencies:
      react-hook-form: 6.15.8(react@16.14.0)

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.1': {}

  '@internationalized/date@3.8.1':
    dependencies:
      '@swc/helpers': 0.5.17

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@istanbuljs/schema@0.1.3': {}

  '@jambit/eslint-plugin-typed-redux-saga@0.4.0': {}

  '@jest/schemas@29.6.3':
    dependencies:
      '@sinclair/typebox': 0.27.8

  '@jest/types@25.5.0':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 1.1.2
      '@types/yargs': 15.0.19
      chalk: 3.0.0

  '@jest/types@26.6.2':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 3.0.4
      '@types/node': 20.14.8
      '@types/yargs': 15.0.19
      chalk: 4.1.2

  '@jridgewell/gen-mapping@0.3.5':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@lucide/svelte@0.486.0(svelte@5.32.0)':
    dependencies:
      svelte: 5.32.0

  '@microsoft/api-extractor-model@7.30.4(@types/node@20.14.8)':
    dependencies:
      '@microsoft/tsdoc': 0.15.1
      '@microsoft/tsdoc-config': 0.17.1
      '@rushstack/node-core-library': 5.12.0(@types/node@20.14.8)
    transitivePeerDependencies:
      - '@types/node'

  '@microsoft/api-extractor@7.52.1(@types/node@20.14.8)':
    dependencies:
      '@microsoft/api-extractor-model': 7.30.4(@types/node@20.14.8)
      '@microsoft/tsdoc': 0.15.1
      '@microsoft/tsdoc-config': 0.17.1
      '@rushstack/node-core-library': 5.12.0(@types/node@20.14.8)
      '@rushstack/rig-package': 0.5.3
      '@rushstack/terminal': 0.15.1(@types/node@20.14.8)
      '@rushstack/ts-command-line': 4.23.6(@types/node@20.14.8)
      lodash: 4.17.21
      minimatch: 3.0.8
      resolve: 1.22.8
      semver: 7.5.4
      source-map: 0.6.1
      typescript: 5.8.2
    transitivePeerDependencies:
      - '@types/node'

  '@microsoft/signalr@7.0.14':
    dependencies:
      abort-controller: 3.0.0
      eventsource: 2.0.2
      fetch-cookie: 2.2.0
      node-fetch: 2.7.0
      ws: 7.5.10
    transitivePeerDependencies:
      - bufferutil
      - encoding
      - utf-8-validate

  '@microsoft/tsdoc-config@0.17.1':
    dependencies:
      '@microsoft/tsdoc': 0.15.1
      ajv: 8.12.0
      jju: 1.4.0
      resolve: 1.22.8

  '@microsoft/tsdoc@0.15.1': {}

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.17.1

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@polka/url@1.0.0-next.28': {}

  '@redux-saga/core@1.3.0':
    dependencies:
      '@babel/runtime': 7.24.7
      '@redux-saga/deferred': 1.2.1
      '@redux-saga/delay-p': 1.2.1
      '@redux-saga/is': 1.1.3
      '@redux-saga/symbols': 1.1.3
      '@redux-saga/types': 1.2.1
      typescript-tuple: 2.2.1

  '@redux-saga/deferred@1.2.1': {}

  '@redux-saga/delay-p@1.2.1':
    dependencies:
      '@redux-saga/symbols': 1.1.3

  '@redux-saga/is@1.1.3':
    dependencies:
      '@redux-saga/symbols': 1.1.3
      '@redux-saga/types': 1.2.1

  '@redux-saga/symbols@1.1.3': {}

  '@redux-saga/types@1.2.1': {}

  '@reduxjs/toolkit@1.3.2':
    dependencies:
      immer: 6.0.9
      redux: 4.2.1
      redux-thunk: 2.4.2(redux@4.2.1)
      reselect: 4.1.8

  '@rollup/plugin-commonjs@28.0.5(rollup@4.34.4)':
    dependencies:
      '@rollup/pluginutils': 5.1.4(rollup@4.34.4)
      commondir: 1.0.1
      estree-walker: 2.0.2
      fdir: 6.4.3(picomatch@4.0.2)
      is-reference: 1.2.1
      magic-string: 0.30.17
      picomatch: 4.0.2
    optionalDependencies:
      rollup: 4.34.4

  '@rollup/plugin-json@6.1.0(rollup@4.34.4)':
    dependencies:
      '@rollup/pluginutils': 5.1.4(rollup@4.34.4)
    optionalDependencies:
      rollup: 4.34.4

  '@rollup/plugin-node-resolve@16.0.1(rollup@4.34.4)':
    dependencies:
      '@rollup/pluginutils': 5.1.4(rollup@4.34.4)
      '@types/resolve': 1.20.2
      deepmerge: 4.3.1
      is-module: 1.0.0
      resolve: 1.22.8
    optionalDependencies:
      rollup: 4.34.4

  '@rollup/pluginutils@5.1.4(rollup@4.34.4)':
    dependencies:
      '@types/estree': 1.0.6
      estree-walker: 2.0.2
      picomatch: 4.0.2
    optionalDependencies:
      rollup: 4.34.4

  '@rollup/rollup-android-arm-eabi@4.34.4':
    optional: true

  '@rollup/rollup-android-arm64@4.34.4':
    optional: true

  '@rollup/rollup-darwin-arm64@4.34.4':
    optional: true

  '@rollup/rollup-darwin-x64@4.34.4':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.34.4':
    optional: true

  '@rollup/rollup-freebsd-x64@4.34.4':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.34.4':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.34.4':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.34.4':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.34.4':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.34.4':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.34.4':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.34.4':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.34.4':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.34.4':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.34.4':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.34.4':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.34.4':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.34.4':
    optional: true

  '@rushstack/node-core-library@5.12.0(@types/node@20.14.8)':
    dependencies:
      ajv: 8.13.0
      ajv-draft-04: 1.0.0(ajv@8.13.0)
      ajv-formats: 3.0.1(ajv@8.13.0)
      fs-extra: 11.3.0
      import-lazy: 4.0.0
      jju: 1.4.0
      resolve: 1.22.8
      semver: 7.5.4
    optionalDependencies:
      '@types/node': 20.14.8

  '@rushstack/rig-package@0.5.3':
    dependencies:
      resolve: 1.22.8
      strip-json-comments: 3.1.1

  '@rushstack/terminal@0.15.1(@types/node@20.14.8)':
    dependencies:
      '@rushstack/node-core-library': 5.12.0(@types/node@20.14.8)
      supports-color: 8.1.1
    optionalDependencies:
      '@types/node': 20.14.8

  '@rushstack/ts-command-line@4.23.6(@types/node@20.14.8)':
    dependencies:
      '@rushstack/terminal': 0.15.1(@types/node@20.14.8)
      '@types/argparse': 1.0.38
      argparse: 1.0.10
      string-argv: 0.3.2
    transitivePeerDependencies:
      - '@types/node'

  '@sentry-internal/browser-utils@9.9.0':
    dependencies:
      '@sentry/core': 9.9.0

  '@sentry-internal/feedback@9.9.0':
    dependencies:
      '@sentry/core': 9.9.0

  '@sentry-internal/replay-canvas@9.9.0':
    dependencies:
      '@sentry-internal/replay': 9.9.0
      '@sentry/core': 9.9.0

  '@sentry-internal/replay@9.9.0':
    dependencies:
      '@sentry-internal/browser-utils': 9.9.0
      '@sentry/core': 9.9.0

  '@sentry/babel-plugin-component-annotate@3.2.2': {}

  '@sentry/browser@9.9.0':
    dependencies:
      '@sentry-internal/browser-utils': 9.9.0
      '@sentry-internal/feedback': 9.9.0
      '@sentry-internal/replay': 9.9.0
      '@sentry-internal/replay-canvas': 9.9.0
      '@sentry/core': 9.9.0

  '@sentry/bundler-plugin-core@3.2.2':
    dependencies:
      '@babel/core': 7.24.7
      '@sentry/babel-plugin-component-annotate': 3.2.2
      '@sentry/cli': 2.42.2
      dotenv: 16.4.7
      find-up: 5.0.0
      glob: 9.3.5
      magic-string: 0.30.8
      unplugin: 1.0.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@sentry/cli-darwin@2.42.2':
    optional: true

  '@sentry/cli-linux-arm64@2.42.2':
    optional: true

  '@sentry/cli-linux-arm@2.42.2':
    optional: true

  '@sentry/cli-linux-i686@2.42.2':
    optional: true

  '@sentry/cli-linux-x64@2.42.2':
    optional: true

  '@sentry/cli-win32-i686@2.42.2':
    optional: true

  '@sentry/cli-win32-x64@2.42.2':
    optional: true

  '@sentry/cli@2.42.2':
    dependencies:
      https-proxy-agent: 5.0.1
      node-fetch: 2.7.0
      progress: 2.0.3
      proxy-from-env: 1.1.0
      which: 2.0.2
    optionalDependencies:
      '@sentry/cli-darwin': 2.42.2
      '@sentry/cli-linux-arm': 2.42.2
      '@sentry/cli-linux-arm64': 2.42.2
      '@sentry/cli-linux-i686': 2.42.2
      '@sentry/cli-linux-x64': 2.42.2
      '@sentry/cli-win32-i686': 2.42.2
      '@sentry/cli-win32-x64': 2.42.2
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@sentry/core@9.9.0': {}

  '@sentry/vite-plugin@3.2.2':
    dependencies:
      '@sentry/bundler-plugin-core': 3.2.2
      unplugin: 1.0.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@sinclair/typebox@0.27.8': {}

  '@skeletonlabs/skeleton-svelte@1.0.0(@skeletonlabs/skeleton@3.1.1(tailwindcss@4.0.9))(svelte@5.32.0)':
    dependencies:
      '@skeletonlabs/skeleton': 3.1.1(tailwindcss@4.0.9)
      '@zag-js/accordion': 1.7.0
      '@zag-js/avatar': 1.7.0
      '@zag-js/combobox': 1.7.0
      '@zag-js/dialog': 1.7.0
      '@zag-js/file-upload': 1.7.0
      '@zag-js/pagination': 1.7.0
      '@zag-js/popover': 1.7.0
      '@zag-js/progress': 1.7.0
      '@zag-js/radio-group': 1.7.0
      '@zag-js/rating-group': 1.7.0
      '@zag-js/slider': 1.7.0
      '@zag-js/svelte': 1.7.0(svelte@5.32.0)
      '@zag-js/switch': 1.7.0
      '@zag-js/tabs': 1.7.0
      '@zag-js/tags-input': 1.7.0
      '@zag-js/tooltip': 1.7.0
      svelte: 5.32.0

  '@skeletonlabs/skeleton@3.1.1(tailwindcss@4.0.9)':
    dependencies:
      tailwindcss: 4.0.9

  '@sveltejs/acorn-typescript@1.0.5(acorn@8.14.0)':
    dependencies:
      acorn: 8.14.0

  '@sveltejs/acorn-typescript@1.0.5(acorn@8.14.1)':
    dependencies:
      acorn: 8.14.1

  '@sveltejs/adapter-node@5.2.12(@sveltejs/kit@2.21.1(@sveltejs/vite-plugin-svelte@5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))':
    dependencies:
      '@rollup/plugin-commonjs': 28.0.5(rollup@4.34.4)
      '@rollup/plugin-json': 6.1.0(rollup@4.34.4)
      '@rollup/plugin-node-resolve': 16.0.1(rollup@4.34.4)
      '@sveltejs/kit': 2.21.1(@sveltejs/vite-plugin-svelte@5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      rollup: 4.34.4

  '@sveltejs/adapter-static@3.0.8(@sveltejs/kit@2.21.1(@sveltejs/vite-plugin-svelte@5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))':
    dependencies:
      '@sveltejs/kit': 2.21.1(@sveltejs/vite-plugin-svelte@5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))

  '@sveltejs/kit@2.21.1(@sveltejs/vite-plugin-svelte@5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))':
    dependencies:
      '@sveltejs/acorn-typescript': 1.0.5(acorn@8.14.1)
      '@sveltejs/vite-plugin-svelte': 5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      '@types/cookie': 0.6.0
      acorn: 8.14.1
      cookie: 0.6.0
      devalue: 5.1.1
      esm-env: 1.2.2
      kleur: 4.1.5
      magic-string: 0.30.17
      mrmime: 2.0.1
      sade: 1.8.1
      set-cookie-parser: 2.6.0
      sirv: 3.0.1
      svelte: 5.32.0
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)

  '@sveltejs/vite-plugin-svelte-inspector@4.0.1(@sveltejs/vite-plugin-svelte@5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))':
    dependencies:
      '@sveltejs/vite-plugin-svelte': 5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      debug: 4.4.0
      svelte: 5.32.0
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
    transitivePeerDependencies:
      - supports-color

  '@sveltejs/vite-plugin-svelte@5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))':
    dependencies:
      '@sveltejs/vite-plugin-svelte-inspector': 4.0.1(@sveltejs/vite-plugin-svelte@5.0.3(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)))(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      debug: 4.4.0
      deepmerge: 4.3.1
      kleur: 4.1.5
      magic-string: 0.30.17
      svelte: 5.32.0
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
      vitefu: 1.0.6(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
    transitivePeerDependencies:
      - supports-color

  '@swc/core-darwin-arm64@1.7.26':
    optional: true

  '@swc/core-darwin-x64@1.7.26':
    optional: true

  '@swc/core-linux-arm-gnueabihf@1.7.26':
    optional: true

  '@swc/core-linux-arm64-gnu@1.7.26':
    optional: true

  '@swc/core-linux-arm64-musl@1.7.26':
    optional: true

  '@swc/core-linux-x64-gnu@1.7.26':
    optional: true

  '@swc/core-linux-x64-musl@1.7.26':
    optional: true

  '@swc/core-win32-arm64-msvc@1.7.26':
    optional: true

  '@swc/core-win32-ia32-msvc@1.7.26':
    optional: true

  '@swc/core-win32-x64-msvc@1.7.26':
    optional: true

  '@swc/core@1.7.26(@swc/helpers@0.5.17)':
    dependencies:
      '@swc/counter': 0.1.3
      '@swc/types': 0.1.12
    optionalDependencies:
      '@swc/core-darwin-arm64': 1.7.26
      '@swc/core-darwin-x64': 1.7.26
      '@swc/core-linux-arm-gnueabihf': 1.7.26
      '@swc/core-linux-arm64-gnu': 1.7.26
      '@swc/core-linux-arm64-musl': 1.7.26
      '@swc/core-linux-x64-gnu': 1.7.26
      '@swc/core-linux-x64-musl': 1.7.26
      '@swc/core-win32-arm64-msvc': 1.7.26
      '@swc/core-win32-ia32-msvc': 1.7.26
      '@swc/core-win32-x64-msvc': 1.7.26
      '@swc/helpers': 0.5.17

  '@swc/counter@0.1.3': {}

  '@swc/helpers@0.5.17':
    dependencies:
      tslib: 2.8.1

  '@swc/types@0.1.12':
    dependencies:
      '@swc/counter': 0.1.3

  '@tailwindcss/forms@0.5.10(tailwindcss@4.0.9)':
    dependencies:
      mini-svg-data-uri: 1.4.4
      tailwindcss: 4.0.9

  '@tailwindcss/node@4.0.9':
    dependencies:
      enhanced-resolve: 5.18.1
      jiti: 2.4.2
      tailwindcss: 4.0.9

  '@tailwindcss/oxide-android-arm64@4.0.9':
    optional: true

  '@tailwindcss/oxide-darwin-arm64@4.0.9':
    optional: true

  '@tailwindcss/oxide-darwin-x64@4.0.9':
    optional: true

  '@tailwindcss/oxide-freebsd-x64@4.0.9':
    optional: true

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.0.9':
    optional: true

  '@tailwindcss/oxide-linux-arm64-gnu@4.0.9':
    optional: true

  '@tailwindcss/oxide-linux-arm64-musl@4.0.9':
    optional: true

  '@tailwindcss/oxide-linux-x64-gnu@4.0.9':
    optional: true

  '@tailwindcss/oxide-linux-x64-musl@4.0.9':
    optional: true

  '@tailwindcss/oxide-win32-arm64-msvc@4.0.9':
    optional: true

  '@tailwindcss/oxide-win32-x64-msvc@4.0.9':
    optional: true

  '@tailwindcss/oxide@4.0.9':
    optionalDependencies:
      '@tailwindcss/oxide-android-arm64': 4.0.9
      '@tailwindcss/oxide-darwin-arm64': 4.0.9
      '@tailwindcss/oxide-darwin-x64': 4.0.9
      '@tailwindcss/oxide-freebsd-x64': 4.0.9
      '@tailwindcss/oxide-linux-arm-gnueabihf': 4.0.9
      '@tailwindcss/oxide-linux-arm64-gnu': 4.0.9
      '@tailwindcss/oxide-linux-arm64-musl': 4.0.9
      '@tailwindcss/oxide-linux-x64-gnu': 4.0.9
      '@tailwindcss/oxide-linux-x64-musl': 4.0.9
      '@tailwindcss/oxide-win32-arm64-msvc': 4.0.9
      '@tailwindcss/oxide-win32-x64-msvc': 4.0.9

  '@tailwindcss/typography@0.5.16(tailwindcss@4.0.9)':
    dependencies:
      lodash.castarray: 4.4.0
      lodash.isplainobject: 4.0.6
      lodash.merge: 4.6.2
      postcss-selector-parser: 6.0.10
      tailwindcss: 4.0.9

  '@tailwindcss/vite@4.0.9(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))':
    dependencies:
      '@tailwindcss/node': 4.0.9
      '@tailwindcss/oxide': 4.0.9
      lightningcss: 1.29.1
      tailwindcss: 4.0.9
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)

  '@testing-library/dom@10.4.0':
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/runtime': 7.24.7
      '@types/aria-query': 5.0.4
      aria-query: 5.3.0
      chalk: 4.1.2
      dom-accessibility-api: 0.5.16
      lz-string: 1.5.0
      pretty-format: 27.5.1

  '@testing-library/dom@7.26.7':
    dependencies:
      '@babel/code-frame': 7.24.7
      '@babel/runtime': 7.24.7
      '@types/aria-query': 4.2.2
      aria-query: 4.2.2
      chalk: 4.1.2
      dom-accessibility-api: 0.5.16
      lz-string: 1.5.0
      pretty-format: 26.6.2

  '@testing-library/jest-dom@6.6.3':
    dependencies:
      '@adobe/css-tools': 4.4.0
      aria-query: 5.3.1
      chalk: 3.0.0
      css.escape: 1.5.1
      dom-accessibility-api: 0.6.3
      lodash: 4.17.21
      redent: 3.0.0

  '@testing-library/react-hooks@3.4.2(react-test-renderer@16.13.0(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@babel/runtime': 7.24.7
      '@types/testing-library__react-hooks': 3.4.1
      react: 16.14.0
      react-test-renderer: 16.13.0(react@16.14.0)

  '@testing-library/react@10.0.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@babel/runtime': 7.24.7
      '@testing-library/dom': 7.26.7
      '@types/testing-library__react': 9.1.3
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  '@testing-library/svelte@5.2.7(svelte@5.32.0)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))(vitest@3.0.5(@types/node@20.14.8)(jiti@2.4.2)(jsdom@26.0.0)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))':
    dependencies:
      '@testing-library/dom': 10.4.0
      svelte: 5.32.0
    optionalDependencies:
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
      vitest: 3.0.5(@types/node@20.14.8)(jiti@2.4.2)(jsdom@26.0.0)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)

  '@testing-library/user-event@12.2.2(@testing-library/dom@7.26.7)':
    dependencies:
      '@babel/runtime': 7.24.7
      '@testing-library/dom': 7.26.7

  '@types/argparse@1.0.38': {}

  '@types/aria-query@4.2.2': {}

  '@types/aria-query@5.0.4': {}

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.25.6
      '@babel/types': 7.25.6
      '@types/babel__generator': 7.6.8
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.6

  '@types/babel__generator@7.6.8':
    dependencies:
      '@babel/types': 7.25.6

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.25.6
      '@babel/types': 7.25.6

  '@types/babel__traverse@7.20.6':
    dependencies:
      '@babel/types': 7.25.6

  '@types/cookie@0.6.0': {}

  '@types/estree@1.0.6': {}

  '@types/fs-extra@9.0.13':
    dependencies:
      '@types/node': 20.14.8

  '@types/hoist-non-react-statics@3.3.5':
    dependencies:
      '@types/react': 16.9.25
      hoist-non-react-statics: 3.3.2

  '@types/istanbul-lib-coverage@2.0.6': {}

  '@types/istanbul-lib-report@3.0.3':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6

  '@types/istanbul-reports@1.1.2':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-lib-report': 3.0.3

  '@types/istanbul-reports@3.0.4':
    dependencies:
      '@types/istanbul-lib-report': 3.0.3

  '@types/jest@25.1.4':
    dependencies:
      jest-diff: 25.5.0
      pretty-format: 25.5.0

  '@types/json-schema@7.0.15': {}

  '@types/lodash@4.17.15': {}

  '@types/minimist@1.2.5': {}

  '@types/node@20.14.8':
    dependencies:
      undici-types: 5.26.5

  '@types/normalize-package-data@2.4.4': {}

  '@types/parse-json@4.0.2': {}

  '@types/prop-types@15.7.12': {}

  '@types/react-dom@16.9.3':
    dependencies:
      '@types/react': 16.9.25

  '@types/react-redux@7.1.7':
    dependencies:
      '@types/hoist-non-react-statics': 3.3.5
      '@types/react': 16.9.25
      hoist-non-react-statics: 3.3.2
      redux: 4.2.1

  '@types/react-test-renderer@16.9.2':
    dependencies:
      '@types/react': 16.9.25

  '@types/react@16.9.25':
    dependencies:
      '@types/prop-types': 15.7.12
      csstype: 2.6.21

  '@types/resolve@1.20.2': {}

  '@types/schema-utils@2.4.0':
    dependencies:
      schema-utils: 2.7.1

  '@types/testing-library__dom@7.5.0':
    dependencies:
      '@testing-library/dom': 7.26.7

  '@types/testing-library__jest-dom@5.14.0':
    dependencies:
      '@types/jest': 25.1.4

  '@types/testing-library__react-hooks@3.4.1':
    dependencies:
      '@types/react-test-renderer': 16.9.2

  '@types/testing-library__react@10.0.1':
    dependencies:
      '@types/react-dom': 16.9.3
      '@types/testing-library__dom': 7.5.0
      pretty-format: 25.5.0

  '@types/testing-library__react@9.1.3':
    dependencies:
      '@types/react-dom': 16.9.3
      '@types/testing-library__dom': 7.5.0
      pretty-format: 25.5.0

  '@types/yargs-parser@21.0.3': {}

  '@types/yargs@15.0.19':
    dependencies:
      '@types/yargs-parser': 21.0.3

  '@types/yup@0.29.1': {}

  '@typescript-eslint/eslint-plugin@8.23.0(@typescript-eslint/parser@8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2))(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/type-utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/visitor-keys': 8.23.0
      eslint: 9.19.0(jiti@2.4.2)
      graphemer: 1.4.0
      ignore: 5.3.1
      natural-compare: 1.4.0
      ts-api-utils: 2.0.1(typescript@5.5.2)
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/typescript-estree': 8.23.0(typescript@5.5.2)
      '@typescript-eslint/visitor-keys': 8.23.0
      debug: 4.4.0
      eslint: 9.19.0(jiti@2.4.2)
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@8.23.0':
    dependencies:
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/visitor-keys': 8.23.0

  '@typescript-eslint/type-utils@8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)':
    dependencies:
      '@typescript-eslint/typescript-estree': 8.23.0(typescript@5.5.2)
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      debug: 4.4.0
      eslint: 9.19.0(jiti@2.4.2)
      ts-api-utils: 2.0.1(typescript@5.5.2)
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@8.23.0': {}

  '@typescript-eslint/typescript-estree@8.23.0(typescript@5.5.2)':
    dependencies:
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/visitor-keys': 8.23.0
      debug: 4.4.0
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.6.3
      ts-api-utils: 2.0.1(typescript@5.5.2)
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)':
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.19.0(jiti@2.4.2))
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/typescript-estree': 8.23.0(typescript@5.5.2)
      eslint: 9.19.0(jiti@2.4.2)
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@8.23.0':
    dependencies:
      '@typescript-eslint/types': 8.23.0
      eslint-visitor-keys: 4.2.0

  '@vitejs/plugin-react-swc@3.7.2(@swc/helpers@0.5.17)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))':
    dependencies:
      '@swc/core': 1.7.26(@swc/helpers@0.5.17)
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
    transitivePeerDependencies:
      - '@swc/helpers'

  '@vitest/coverage-v8@3.0.5(vitest@3.0.5(@types/node@20.14.8)(jiti@2.4.2)(jsdom@26.0.0)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@bcoe/v8-coverage': 1.0.2
      debug: 4.4.0
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 5.0.6
      istanbul-reports: 3.1.7
      magic-string: 0.30.17
      magicast: 0.3.5
      std-env: 3.8.0
      test-exclude: 7.0.1
      tinyrainbow: 2.0.0
      vitest: 3.0.5(@types/node@20.14.8)(jiti@2.4.2)(jsdom@26.0.0)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
    transitivePeerDependencies:
      - supports-color

  '@vitest/expect@3.0.5':
    dependencies:
      '@vitest/spy': 3.0.5
      '@vitest/utils': 3.0.5
      chai: 5.1.2
      tinyrainbow: 2.0.0

  '@vitest/mocker@3.0.5(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))':
    dependencies:
      '@vitest/spy': 3.0.5
      estree-walker: 3.0.3
      magic-string: 0.30.17
    optionalDependencies:
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)

  '@vitest/pretty-format@3.0.5':
    dependencies:
      tinyrainbow: 2.0.0

  '@vitest/runner@3.0.5':
    dependencies:
      '@vitest/utils': 3.0.5
      pathe: 2.0.2

  '@vitest/snapshot@3.0.5':
    dependencies:
      '@vitest/pretty-format': 3.0.5
      magic-string: 0.30.17
      pathe: 2.0.2

  '@vitest/spy@3.0.5':
    dependencies:
      tinyspy: 3.0.2

  '@vitest/utils@3.0.5':
    dependencies:
      '@vitest/pretty-format': 3.0.5
      loupe: 3.1.3
      tinyrainbow: 2.0.0

  '@volar/language-core@2.4.12':
    dependencies:
      '@volar/source-map': 2.4.12

  '@volar/source-map@2.4.12': {}

  '@volar/typescript@2.4.12':
    dependencies:
      '@volar/language-core': 2.4.12
      path-browserify: 1.0.1
      vscode-uri: 3.1.0

  '@vue/compiler-core@3.5.13':
    dependencies:
      '@babel/parser': 7.25.6
      '@vue/shared': 3.5.13
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.2.1

  '@vue/compiler-dom@3.5.13':
    dependencies:
      '@vue/compiler-core': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/compiler-vue2@2.7.16':
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0

  '@vue/language-core@2.2.0(typescript@5.5.2)':
    dependencies:
      '@volar/language-core': 2.4.12
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-vue2': 2.7.16
      '@vue/shared': 3.5.13
      alien-signals: 0.4.14
      minimatch: 9.0.5
      muggle-string: 0.4.1
      path-browserify: 1.0.1
    optionalDependencies:
      typescript: 5.5.2

  '@vue/shared@3.5.13': {}

  '@zag-js/accordion@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/anatomy@1.7.0': {}

  '@zag-js/aria-hidden@1.7.0': {}

  '@zag-js/auto-resize@1.7.0':
    dependencies:
      '@zag-js/dom-query': 1.7.0

  '@zag-js/avatar@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/collection@1.7.0':
    dependencies:
      '@zag-js/utils': 1.7.0

  '@zag-js/combobox@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/aria-hidden': 1.7.0
      '@zag-js/collection': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dismissable': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/popper': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/core@1.7.0':
    dependencies:
      '@zag-js/dom-query': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/dialog@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/aria-hidden': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dismissable': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/focus-trap': 1.7.0
      '@zag-js/remove-scroll': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/dismissable@1.7.0':
    dependencies:
      '@zag-js/dom-query': 1.7.0
      '@zag-js/interact-outside': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/dom-query@1.7.0':
    dependencies:
      '@zag-js/types': 1.7.0

  '@zag-js/element-rect@1.7.0': {}

  '@zag-js/element-size@1.7.0': {}

  '@zag-js/file-upload@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/file-utils': 1.7.0
      '@zag-js/i18n-utils': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/file-utils@1.7.0':
    dependencies:
      '@zag-js/i18n-utils': 1.7.0

  '@zag-js/focus-trap@1.7.0':
    dependencies:
      '@zag-js/dom-query': 1.7.0

  '@zag-js/focus-visible@1.7.0':
    dependencies:
      '@zag-js/dom-query': 1.7.0

  '@zag-js/i18n-utils@1.7.0':
    dependencies:
      '@zag-js/dom-query': 1.7.0

  '@zag-js/interact-outside@1.7.0':
    dependencies:
      '@zag-js/dom-query': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/live-region@1.7.0': {}

  '@zag-js/menu@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dismissable': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/popper': 1.7.0
      '@zag-js/rect-utils': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/pagination@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/popover@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/aria-hidden': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dismissable': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/focus-trap': 1.7.0
      '@zag-js/popper': 1.7.0
      '@zag-js/remove-scroll': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/popper@1.7.0':
    dependencies:
      '@floating-ui/dom': 1.6.13
      '@zag-js/dom-query': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/progress@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/radio-group@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/element-rect': 1.7.0
      '@zag-js/focus-visible': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/rating-group@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/rect-utils@1.7.0': {}

  '@zag-js/remove-scroll@1.7.0':
    dependencies:
      '@zag-js/dom-query': 1.7.0

  '@zag-js/slider@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/element-size': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/store@1.7.0':
    dependencies:
      proxy-compare: 3.0.1

  '@zag-js/svelte@1.7.0(svelte@5.32.0)':
    dependencies:
      '@zag-js/core': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0
      svelte: 5.32.0

  '@zag-js/switch@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/focus-visible': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/tabs@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/element-rect': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/tags-input@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/auto-resize': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/interact-outside': 1.7.0
      '@zag-js/live-region': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/tooltip@1.7.0':
    dependencies:
      '@zag-js/anatomy': 1.7.0
      '@zag-js/core': 1.7.0
      '@zag-js/dom-query': 1.7.0
      '@zag-js/focus-visible': 1.7.0
      '@zag-js/popper': 1.7.0
      '@zag-js/store': 1.7.0
      '@zag-js/types': 1.7.0
      '@zag-js/utils': 1.7.0

  '@zag-js/types@1.7.0':
    dependencies:
      csstype: 3.1.3

  '@zag-js/utils@1.7.0': {}

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1

  acorn-babel@0.11.1-32: {}

  acorn-jsx@5.3.2(acorn@8.14.1):
    dependencies:
      acorn: 8.14.1

  acorn@5.7.4: {}

  acorn@8.14.0: {}

  acorn@8.14.1: {}

  add-dom-event-listener@1.1.0:
    dependencies:
      object-assign: 4.1.1

  agent-base@6.0.2:
    dependencies:
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color

  agent-base@7.1.3: {}

  ajv-draft-04@1.0.0(ajv@8.13.0):
    optionalDependencies:
      ajv: 8.13.0

  ajv-formats@3.0.1(ajv@8.13.0):
    optionalDependencies:
      ajv: 8.13.0

  ajv-keywords@3.5.2(ajv@6.12.6):
    dependencies:
      ajv: 6.12.6

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.12.0:
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1

  ajv@8.13.0:
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1

  alien-signals@0.4.14: {}

  amdefine@1.0.1: {}

  ansi-escapes@7.0.0:
    dependencies:
      environment: 1.1.0

  ansi-regex@0.2.1: {}

  ansi-regex@2.1.1: {}

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@1.1.0: {}

  ansi-styles@2.2.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  antd@4.6.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@ant-design/colors': 4.0.5
      '@ant-design/css-animation': 1.7.3
      '@ant-design/icons': 4.8.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@ant-design/react-slick': 0.27.14(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@babel/runtime': 7.24.7
      array-tree-filter: 2.1.0
      classnames: 2.2.6
      copy-to-clipboard: 3.3.3
      lodash: 4.17.21
      moment: 2.30.1
      omit.js: 2.0.2
      raf: 3.4.1
      rc-animate: 3.1.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-cascader: 1.4.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-checkbox: 2.3.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-collapse: 2.0.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-dialog: 8.2.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-drawer: 4.1.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-dropdown: 3.2.5(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-field-form: 1.10.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-image: 3.0.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-input-number: 6.0.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-mentions: 1.5.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-menu: 8.7.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-motion: 2.9.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-notification: 4.4.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-pagination: 3.0.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-picker: 2.1.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-progress: 3.1.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-rate: 2.8.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-resize-observer: 0.2.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-select: 11.3.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-slider: 9.5.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-steps: 4.1.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-switch: 3.2.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-table: 7.9.10(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-tabs: 11.6.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-textarea: 0.3.7(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-tooltip: 5.0.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-tree: 3.10.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-tree-select: 4.1.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-trigger: 5.0.9(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-upload: 3.3.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      scroll-into-view-if-needed: 2.2.31
      warning: 4.0.3

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  aria-query@4.2.2:
    dependencies:
      '@babel/runtime': 7.24.7
      '@babel/runtime-corejs3': 7.24.7

  aria-query@5.3.0:
    dependencies:
      dequal: 2.0.3

  aria-query@5.3.1: {}

  array-tree-filter@2.1.0: {}

  arrify@1.0.1: {}

  assertion-error@2.0.1: {}

  ast-types@0.6.16: {}

  ast-types@0.8.15: {}

  ast-types@0.9.6: {}

  async-each@0.1.6: {}

  async-validator@3.5.2: {}

  asynckit@0.4.0: {}

  at-least-node@1.0.0: {}

  autoprefixer@10.4.21(postcss@8.5.1):
    dependencies:
      browserslist: 4.24.4
      caniuse-lite: 1.0.30001707
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.1
      postcss: 8.5.1
      postcss-value-parser: 4.2.0

  axios@1.7.2:
    dependencies:
      follow-redirects: 1.15.6
      form-data: 4.0.1
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  axobject-query@4.1.0: {}

  babel-plugin-emotion@10.2.2:
    dependencies:
      '@babel/helper-module-imports': 7.24.7
      '@emotion/hash': 0.8.0
      '@emotion/memoize': 0.7.4
      '@emotion/serialize': 0.11.16
      babel-plugin-macros: 2.8.0
      babel-plugin-syntax-jsx: 6.18.0
      convert-source-map: 1.9.0
      escape-string-regexp: 1.0.5
      find-root: 1.1.0
      source-map: 0.5.7
    transitivePeerDependencies:
      - supports-color

  babel-plugin-macros@2.8.0:
    dependencies:
      '@babel/runtime': 7.24.7
      cosmiconfig: 6.0.0
      resolve: 1.22.8

  babel-plugin-macros@3.1.0:
    dependencies:
      '@babel/runtime': 7.24.7
      cosmiconfig: 7.1.0
      resolve: 1.22.8
    optional: true

  babel-plugin-react-intl@7.9.4:
    dependencies:
      '@babel/core': 7.24.7
      '@babel/helper-plugin-utils': 7.24.8
      '@babel/types': 7.25.6
      '@formatjs/ts-transformer': 2.13.0
      '@types/babel__core': 7.20.5
      '@types/fs-extra': 9.0.13
      '@types/schema-utils': 2.4.0
      fs-extra: 9.1.0
      intl-messageformat-parser: 5.5.1
      schema-utils: 2.7.1
    transitivePeerDependencies:
      - supports-color
      - ts-jest

  babel-plugin-syntax-jsx@6.18.0: {}

  babel@4.1.1:
    dependencies:
      acorn-babel: 0.11.1-32
      ast-types: 0.6.16
      chalk: 0.5.1
      chokidar: 0.12.6
      commander: 2.20.3
      core-js: 0.5.4
      debug: 2.6.9
      detect-indent: 3.0.1
      estraverse: 1.9.3
      esutils: 1.1.6
      fs-readdir-recursive: 0.1.2
      globals: 6.4.1
      is-integer: 1.0.7
      js-tokenizer: 1.3.3
      leven: 1.0.2
      lodash: 3.10.1
      output-file-sync: 1.1.2
      path-is-absolute: 1.0.1
      private: 0.1.8
      regenerator-babel: 0.8.10-2
      regexpu: 1.3.0
      repeating: 1.1.3
      shebang-regex: 1.0.0
      slash: 1.0.0
      source-map: 0.1.43
      source-map-support: 0.2.10
      source-map-to-comment: 1.1.0
      trim-right: 1.0.1
    transitivePeerDependencies:
      - supports-color

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  bignumber.js@8.1.1: {}

  binary-extensions@2.3.0: {}

  birecord@0.1.1: {}

  bits-ui@1.5.3(svelte@5.32.0):
    dependencies:
      '@floating-ui/core': 1.6.9
      '@floating-ui/dom': 1.7.0
      '@internationalized/date': 3.8.1
      esm-env: 1.2.2
      runed: 0.23.4(svelte@5.32.0)
      svelte: 5.32.0
      svelte-toolbelt: 0.7.1(svelte@5.32.0)
      tabbable: 6.2.0

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.23.3:
    dependencies:
      caniuse-lite: 1.0.30001660
      electron-to-chromium: 1.5.25
      node-releases: 2.0.18
      update-browserslist-db: 1.1.0(browserslist@4.23.3)

  browserslist@4.24.4:
    dependencies:
      caniuse-lite: 1.0.30001707
      electron-to-chromium: 1.5.123
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.24.4)

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  cac@6.7.14: {}

  callsites@3.1.0: {}

  camelcase-keys@6.2.2:
    dependencies:
      camelcase: 5.3.1
      map-obj: 4.3.0
      quick-lru: 4.0.1

  camelcase@5.3.1: {}

  caniuse-lite@1.0.30001660: {}

  caniuse-lite@1.0.30001707: {}

  chai@5.1.2:
    dependencies:
      assertion-error: 2.0.1
      check-error: 2.1.1
      deep-eql: 5.0.2
      loupe: 3.1.3
      pathval: 2.0.0

  chalk@0.5.1:
    dependencies:
      ansi-styles: 1.1.0
      escape-string-regexp: 1.0.5
      has-ansi: 0.1.0
      strip-ansi: 0.3.0
      supports-color: 0.2.0

  chalk@1.1.3:
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@3.0.0:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@5.4.1: {}

  check-error@2.1.1: {}

  chokidar@0.12.6:
    dependencies:
      async-each: 0.1.6
      readdirp: 1.3.0
    optionalDependencies:
      fsevents: 0.3.8

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.2

  classnames@2.2.6: {}

  cli-cursor@3.1.0:
    dependencies:
      restore-cursor: 3.1.0

  cli-cursor@5.0.0:
    dependencies:
      restore-cursor: 5.1.0

  cli-spinners@2.9.2: {}

  cli-truncate@4.0.0:
    dependencies:
      slice-ansi: 5.0.0
      string-width: 7.2.0

  cliui@7.0.4:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone@1.0.4: {}

  clsx@2.1.1: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  colorette@2.0.20: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@13.1.0: {}

  commander@2.20.3: {}

  commondir@1.0.1: {}

  commoner@0.10.8:
    dependencies:
      commander: 2.20.3
      detective: 4.7.1
      glob: 5.0.15
      graceful-fs: 4.2.11
      iconv-lite: 0.4.24
      mkdirp: 0.5.6
      private: 0.1.8
      q: 1.5.1
      recast: 0.11.23

  compare-versions@6.1.1: {}

  compute-scroll-into-view@1.0.20: {}

  concat-map@0.0.1: {}

  confbox@0.1.8: {}

  confbox@0.2.1: {}

  convert-source-map@1.9.0: {}

  convert-source-map@2.0.0: {}

  cookie@0.6.0: {}

  copy-anything@2.0.6:
    dependencies:
      is-what: 3.14.1

  copy-to-clipboard@3.3.3:
    dependencies:
      toggle-selection: 1.0.6

  core-js-pure@3.37.1: {}

  core-js@0.5.4: {}

  core-js@3.42.0: {}

  core-util-is@1.0.3: {}

  cosmiconfig@6.0.0:
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2

  cosmiconfig@7.1.0:
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2
    optional: true

  cross-env@7.0.3:
    dependencies:
      cross-spawn: 7.0.6

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css.escape@1.5.1: {}

  cssesc@3.0.0: {}

  cssstyle@4.2.1:
    dependencies:
      '@asamuzakjp/css-color': 2.8.3
      rrweb-cssom: 0.8.0

  csstype@2.6.21: {}

  csstype@3.1.3: {}

  data-urls@5.0.0:
    dependencies:
      whatwg-mimetype: 4.0.0
      whatwg-url: 14.1.0

  date-fns@2.30.0:
    dependencies:
      '@babel/runtime': 7.24.7

  dayjs@1.11.11: {}

  de-indent@1.0.2: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@4.4.0:
    dependencies:
      ms: 2.1.3

  decamelize-keys@1.1.1:
    dependencies:
      decamelize: 1.2.0
      map-obj: 1.0.1

  decamelize@1.2.0: {}

  decimal.js@10.5.0: {}

  deep-eql@5.0.2: {}

  deep-is@0.1.4: {}

  deepmerge@2.2.1: {}

  deepmerge@4.3.1: {}

  defaults@1.0.4:
    dependencies:
      clone: 1.0.4

  defined@1.0.1: {}

  delayed-stream@1.0.0: {}

  dequal@2.0.3: {}

  detect-indent@3.0.1:
    dependencies:
      get-stdin: 4.0.1
      minimist: 1.2.8
      repeating: 1.1.3

  detect-indent@6.1.0: {}

  detect-libc@1.0.3: {}

  detective@4.7.1:
    dependencies:
      acorn: 5.7.4
      defined: 1.0.1

  devalue@5.1.1: {}

  diff-sequences@25.2.6: {}

  diff-sequences@29.6.3: {}

  dom-accessibility-api@0.5.16: {}

  dom-accessibility-api@0.6.3: {}

  dom-align@1.12.4: {}

  dotenv@16.4.7: {}

  drange@1.1.1: {}

  eastasianwidth@0.2.0: {}

  easy-table@1.2.0:
    dependencies:
      ansi-regex: 5.0.1
    optionalDependencies:
      wcwidth: 1.0.1

  electron-to-chromium@1.5.123: {}

  electron-to-chromium@1.5.25: {}

  emoji-regex@10.4.0: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  end-of-stream@1.4.4:
    dependencies:
      once: 1.4.0

  enhanced-resolve@5.18.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1

  entities@4.5.0: {}

  environment@1.1.0: {}

  errno@0.1.8:
    dependencies:
      prr: 1.0.1
    optional: true

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  error-stack-parser@1.3.6:
    dependencies:
      stackframe: 0.3.1

  es-module-lexer@1.6.0: {}

  esbuild@0.24.2:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.24.2
      '@esbuild/android-arm': 0.24.2
      '@esbuild/android-arm64': 0.24.2
      '@esbuild/android-x64': 0.24.2
      '@esbuild/darwin-arm64': 0.24.2
      '@esbuild/darwin-x64': 0.24.2
      '@esbuild/freebsd-arm64': 0.24.2
      '@esbuild/freebsd-x64': 0.24.2
      '@esbuild/linux-arm': 0.24.2
      '@esbuild/linux-arm64': 0.24.2
      '@esbuild/linux-ia32': 0.24.2
      '@esbuild/linux-loong64': 0.24.2
      '@esbuild/linux-mips64el': 0.24.2
      '@esbuild/linux-ppc64': 0.24.2
      '@esbuild/linux-riscv64': 0.24.2
      '@esbuild/linux-s390x': 0.24.2
      '@esbuild/linux-x64': 0.24.2
      '@esbuild/netbsd-arm64': 0.24.2
      '@esbuild/netbsd-x64': 0.24.2
      '@esbuild/openbsd-arm64': 0.24.2
      '@esbuild/openbsd-x64': 0.24.2
      '@esbuild/sunos-x64': 0.24.2
      '@esbuild/win32-arm64': 0.24.2
      '@esbuild/win32-ia32': 0.24.2
      '@esbuild/win32-x64': 0.24.2

  escalade@3.1.2: {}

  escalade@3.2.0: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@4.0.0: {}

  eslint-config-prettier@10.0.1(eslint@9.19.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.19.0(jiti@2.4.2)

  eslint-plugin-react-debug@1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2):
    dependencies:
      '@eslint-react/ast': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/core': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/eff': 1.26.2
      '@eslint-react/jsx': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/shared': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/var': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/type-utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      eslint: 9.19.0(jiti@2.4.2)
      string-ts: 2.2.1
      ts-pattern: 5.6.2
    optionalDependencies:
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-react-dom@1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2):
    dependencies:
      '@eslint-react/ast': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/core': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/eff': 1.26.2
      '@eslint-react/jsx': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/shared': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/var': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      compare-versions: 6.1.1
      eslint: 9.19.0(jiti@2.4.2)
      string-ts: 2.2.1
      ts-pattern: 5.6.2
    optionalDependencies:
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-react-hooks-extra@1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2):
    dependencies:
      '@eslint-react/ast': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/core': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/eff': 1.26.2
      '@eslint-react/jsx': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/shared': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/var': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/type-utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      eslint: 9.19.0(jiti@2.4.2)
      string-ts: 2.2.1
      ts-pattern: 5.6.2
    optionalDependencies:
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-react-hooks@5.1.0(eslint@9.19.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.19.0(jiti@2.4.2)

  eslint-plugin-react-naming-convention@1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2):
    dependencies:
      '@eslint-react/ast': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/core': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/eff': 1.26.2
      '@eslint-react/jsx': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/shared': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/type-utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      eslint: 9.19.0(jiti@2.4.2)
      string-ts: 2.2.1
      ts-pattern: 5.6.2
    optionalDependencies:
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-react-web-api@1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2):
    dependencies:
      '@eslint-react/ast': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/core': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/eff': 1.26.2
      '@eslint-react/jsx': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/shared': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/var': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      eslint: 9.19.0(jiti@2.4.2)
      string-ts: 2.2.1
      ts-pattern: 5.6.2
    optionalDependencies:
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-react-x@1.26.2(eslint@9.19.0(jiti@2.4.2))(ts-api-utils@2.0.1(typescript@5.5.2))(typescript@5.5.2):
    dependencies:
      '@eslint-react/ast': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/core': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/eff': 1.26.2
      '@eslint-react/jsx': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/shared': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@eslint-react/var': 1.26.2(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/scope-manager': 8.23.0
      '@typescript-eslint/type-utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/types': 8.23.0
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      compare-versions: 6.1.1
      eslint: 9.19.0(jiti@2.4.2)
      is-immutable-type: 5.0.1(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      string-ts: 2.2.1
      ts-pattern: 5.6.2
    optionalDependencies:
      ts-api-utils: 2.0.1(typescript@5.5.2)
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-simple-import-sort@12.1.1(eslint@9.19.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.19.0(jiti@2.4.2)

  eslint-plugin-svelte@3.8.2(eslint@9.19.0(jiti@2.4.2))(svelte@5.32.0):
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.19.0(jiti@2.4.2))
      '@jridgewell/sourcemap-codec': 1.5.0
      eslint: 9.19.0(jiti@2.4.2)
      esutils: 2.0.3
      globals: 16.0.0
      known-css-properties: 0.36.0
      postcss: 8.5.1
      postcss-load-config: 3.1.4(postcss@8.5.1)
      postcss-safe-parser: 7.0.1(postcss@8.5.1)
      semver: 7.6.3
      svelte-eslint-parser: 1.2.0(svelte@5.32.0)
    optionalDependencies:
      svelte: 5.32.0
    transitivePeerDependencies:
      - ts-node

  eslint-scope@8.2.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-scope@8.3.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.0: {}

  eslint@9.19.0(jiti@2.4.2):
    dependencies:
      '@eslint-community/eslint-utils': 4.4.1(eslint@9.19.0(jiti@2.4.2))
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.19.1
      '@eslint/core': 0.10.0
      '@eslint/eslintrc': 3.2.0
      '@eslint/js': 9.19.0
      '@eslint/plugin-kit': 0.2.5
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.1
      '@types/estree': 1.0.6
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.0
      escape-string-regexp: 4.0.0
      eslint-scope: 8.2.0
      eslint-visitor-keys: 4.2.0
      espree: 10.3.0
      esquery: 1.5.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.1
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    optionalDependencies:
      jiti: 2.4.2
    transitivePeerDependencies:
      - supports-color

  esm-env@1.2.2: {}

  espree@10.3.0:
    dependencies:
      acorn: 8.14.1
      acorn-jsx: 5.3.2(acorn@8.14.1)
      eslint-visitor-keys: 4.2.0

  esprima-fb@15001.1001.0-dev-harmony-fb: {}

  esprima@2.7.3: {}

  esprima@3.1.3: {}

  esprima@4.0.1: {}

  esquery@1.5.0:
    dependencies:
      estraverse: 5.3.0

  esrap@1.4.6:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@1.9.3: {}

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  estree-walker@3.0.3:
    dependencies:
      '@types/estree': 1.0.6

  esutils@1.1.6: {}

  esutils@2.0.3: {}

  event-target-shim@5.0.1: {}

  eventemitter3@5.0.1: {}

  eventsource@2.0.2: {}

  execa@4.1.0:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 5.2.0
      human-signals: 1.1.1
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  execa@8.0.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 8.0.1
      human-signals: 5.0.0
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 4.1.0
      strip-final-newline: 3.0.0

  expect-type@1.1.0: {}

  exsolve@1.0.4: {}

  extract-react-intl-messages@4.1.1:
    dependencies:
      '@babel/core': 7.24.7
      babel-plugin-react-intl: 7.9.4
      flat: 5.0.2
      glob: 7.2.3
      js-yaml: 3.14.1
      load-json-file: 6.2.0
      lodash.merge: 4.6.2
      lodash.mergewith: 4.6.2
      lodash.pick: 4.4.0
      meow: 6.1.1
      mkdirp: 1.0.4
      pify: 5.0.0
      read-babelrc-up: 1.1.0
      sort-keys: 4.2.0
      write-json-file: 4.3.0
    transitivePeerDependencies:
      - supports-color
      - ts-jest

  fast-deep-equal@2.0.1: {}

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fastq@1.17.1:
    dependencies:
      reusify: 1.0.4

  fdir@6.4.3(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  fetch-cookie@2.2.0:
    dependencies:
      set-cookie-parser: 2.6.0
      tough-cookie: 4.1.4

  fflate@0.4.8: {}

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-root@1.1.0: {}

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.2
      keyv: 4.5.4

  flat@5.0.2: {}

  flatted@3.3.2: {}

  fn-name@3.0.0: {}

  follow-redirects@1.15.6: {}

  foreground-child@3.3.0:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  form-data@4.0.1:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  formik-antd@2.0.4(antd@4.6.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(formik@2.4.6(react@16.14.0))(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      antd: 4.6.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      formik: 2.4.6(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  formik@2.4.6(react@16.14.0):
    dependencies:
      '@types/hoist-non-react-statics': 3.3.5
      deepmerge: 2.2.1
      hoist-non-react-statics: 3.3.2
      lodash: 4.17.21
      lodash-es: 4.17.21
      react: 16.14.0
      react-fast-compare: 2.0.4
      tiny-warning: 1.0.3
      tslib: 2.6.3

  fraction.js@4.3.7: {}

  fs-extra@11.3.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs-extra@9.1.0:
    dependencies:
      at-least-node: 1.0.0
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs-readdir-recursive@0.1.2: {}

  fs.realpath@1.0.0: {}

  fsevents@0.3.8:
    dependencies:
      nan: 2.20.0
    optional: true

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-east-asian-width@1.3.0: {}

  get-stdin@4.0.1: {}

  get-stream@5.2.0:
    dependencies:
      pump: 3.0.0

  get-stream@8.0.1: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.0
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.0
      path-scurry: 1.11.1

  glob@5.0.15:
    dependencies:
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  glob@9.3.5:
    dependencies:
      fs.realpath: 1.0.0
      minimatch: 8.0.4
      minipass: 4.2.8
      path-scurry: 1.11.1

  globals@11.12.0: {}

  globals@14.0.0: {}

  globals@16.0.0: {}

  globals@6.4.1: {}

  globrex@0.1.2: {}

  graceful-fs@2.0.3: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  hard-rejection@2.1.0: {}

  has-ansi@0.1.0:
    dependencies:
      ansi-regex: 0.2.1

  has-ansi@2.0.0:
    dependencies:
      ansi-regex: 2.1.1

  has-flag@1.0.0: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  hosted-git-info@2.8.9: {}

  html-encoding-sniffer@4.0.0:
    dependencies:
      whatwg-encoding: 3.1.1

  html-escaper@2.0.2: {}

  html-parse-stringify2@2.0.1:
    dependencies:
      void-elements: 2.0.1

  http-proxy-agent@7.0.2:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color

  human-signals@1.1.1: {}

  human-signals@5.0.0: {}

  husky@9.1.7: {}

  i18next-browser-languagedetector@4.0.2:
    dependencies:
      '@babel/runtime': 7.24.7

  i18next@19.3.4:
    dependencies:
      '@babel/runtime': 7.24.7

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  ieee754@1.2.1: {}

  ignore@5.3.1: {}

  image-size@0.5.5:
    optional: true

  immer@6.0.9: {}

  immutable@4.3.6: {}

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-lazy@4.0.0: {}

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  inline-style-parser@0.2.4: {}

  intl-messageformat-parser@5.5.1:
    dependencies:
      '@formatjs/intl-numberformat': 5.7.6

  intl-messageformat-parser@6.1.2:
    dependencies:
      '@formatjs/ecma402-abstract': 1.5.0
      tslib: 2.6.3

  intl-messageformat@10.7.15:
    dependencies:
      '@formatjs/ecma402-abstract': 2.3.3
      '@formatjs/fast-memoize': 2.2.6
      '@formatjs/icu-messageformat-parser': 2.11.1
      tslib: 2.6.3

  invariant@2.2.4:
    dependencies:
      loose-envify: 1.4.0

  is-arrayish@0.2.1: {}

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-core-module@2.15.1:
    dependencies:
      hasown: 2.0.2

  is-extglob@2.1.1: {}

  is-finite@1.1.0: {}

  is-fullwidth-code-point@3.0.0: {}

  is-fullwidth-code-point@4.0.0: {}

  is-fullwidth-code-point@5.0.0:
    dependencies:
      get-east-asian-width: 1.3.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-immutable-type@5.0.1(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2):
    dependencies:
      '@typescript-eslint/type-utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      eslint: 9.19.0(jiti@2.4.2)
      ts-api-utils: 2.0.1(typescript@5.5.2)
      ts-declaration-location: 1.0.5(typescript@5.5.2)
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  is-integer@1.0.7:
    dependencies:
      is-finite: 1.1.0

  is-interactive@1.0.0: {}

  is-module@1.0.0: {}

  is-number@7.0.0: {}

  is-plain-obj@1.1.0: {}

  is-plain-obj@2.1.0: {}

  is-potential-custom-element-name@1.0.1: {}

  is-reference@1.2.1:
    dependencies:
      '@types/estree': 1.0.6

  is-reference@3.0.3:
    dependencies:
      '@types/estree': 1.0.6

  is-stream@2.0.1: {}

  is-stream@3.0.0: {}

  is-typedarray@1.0.0: {}

  is-unicode-supported@0.1.0: {}

  is-what@3.14.1: {}

  isarray@0.0.1: {}

  isexe@2.0.0: {}

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@5.0.6:
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      debug: 4.4.0
      istanbul-lib-coverage: 3.2.2
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.1.7:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jest-diff@25.5.0:
    dependencies:
      chalk: 3.0.0
      diff-sequences: 25.2.6
      jest-get-type: 25.2.6
      pretty-format: 25.5.0

  jest-diff@29.7.0:
    dependencies:
      chalk: 4.1.2
      diff-sequences: 29.6.3
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-extended@4.0.2:
    dependencies:
      jest-diff: 29.7.0
      jest-get-type: 29.6.3

  jest-get-type@25.2.6: {}

  jest-get-type@29.6.3: {}

  jiti@2.4.2: {}

  jju@1.4.0: {}

  js-base64@2.6.4: {}

  js-tokenizer@1.3.3: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsdom@26.0.0:
    dependencies:
      cssstyle: 4.2.1
      data-urls: 5.0.0
      decimal.js: 10.5.0
      form-data: 4.0.1
      html-encoding-sniffer: 4.0.0
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.16
      parse5: 7.2.1
      rrweb-cssom: 0.8.0
      saxes: 6.0.0
      symbol-tree: 3.2.4
      tough-cookie: 5.1.0
      w3c-xmlserializer: 5.0.0
      webidl-conversions: 7.0.0
      whatwg-encoding: 3.1.1
      whatwg-mimetype: 4.0.0
      whatwg-url: 14.1.0
      ws: 8.18.0
      xml-name-validator: 5.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsesc@0.5.0: {}

  jsesc@2.5.2: {}

  json-buffer@3.0.1: {}

  json-parse-even-better-errors@2.3.1: {}

  json-sass@1.3.5:
    dependencies:
      babel: 4.1.1
      lodash: 2.4.2
      lodash-node: 2.4.1
      minimist: 1.1.3
      object-assign: 2.0.0
      through2: 0.6.5
    transitivePeerDependencies:
      - supports-color

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json2mq@0.2.0:
    dependencies:
      string-convert: 0.2.1

  json5@2.2.3: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jwt-decode@4.0.0: {}

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kind-of@6.0.3: {}

  kleur@4.1.5: {}

  knip@5.50.2(@types/node@20.14.8)(typescript@5.5.2):
    dependencies:
      '@nodelib/fs.walk': 1.2.8
      '@types/node': 20.14.8
      easy-table: 1.2.0
      enhanced-resolve: 5.18.1
      fast-glob: 3.3.3
      jiti: 2.4.2
      js-yaml: 4.1.0
      minimist: 1.2.8
      picocolors: 1.1.1
      picomatch: 4.0.2
      pretty-ms: 9.0.0
      smol-toml: 1.3.1
      strip-json-comments: 5.0.1
      typescript: 5.5.2
      zod: 3.24.2
      zod-validation-error: 3.3.0(zod@3.24.2)

  known-css-properties@0.36.0: {}

  kolorist@1.8.0: {}

  launchdarkly-js-client-sdk@3.5.0:
    dependencies:
      escape-string-regexp: 4.0.0
      launchdarkly-js-sdk-common: 5.4.0

  launchdarkly-js-sdk-common@5.4.0:
    dependencies:
      base64-js: 1.5.1
      fast-deep-equal: 2.0.1
      uuid: 8.3.2

  less@4.2.0:
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.6.3
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      needle: 3.3.1
      source-map: 0.6.1

  leven@1.0.2: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lightningcss-darwin-arm64@1.29.1:
    optional: true

  lightningcss-darwin-x64@1.29.1:
    optional: true

  lightningcss-freebsd-x64@1.29.1:
    optional: true

  lightningcss-linux-arm-gnueabihf@1.29.1:
    optional: true

  lightningcss-linux-arm64-gnu@1.29.1:
    optional: true

  lightningcss-linux-arm64-musl@1.29.1:
    optional: true

  lightningcss-linux-x64-gnu@1.29.1:
    optional: true

  lightningcss-linux-x64-musl@1.29.1:
    optional: true

  lightningcss-win32-arm64-msvc@1.29.1:
    optional: true

  lightningcss-win32-x64-msvc@1.29.1:
    optional: true

  lightningcss@1.29.1:
    dependencies:
      detect-libc: 1.0.3
    optionalDependencies:
      lightningcss-darwin-arm64: 1.29.1
      lightningcss-darwin-x64: 1.29.1
      lightningcss-freebsd-x64: 1.29.1
      lightningcss-linux-arm-gnueabihf: 1.29.1
      lightningcss-linux-arm64-gnu: 1.29.1
      lightningcss-linux-arm64-musl: 1.29.1
      lightningcss-linux-x64-gnu: 1.29.1
      lightningcss-linux-x64-musl: 1.29.1
      lightningcss-win32-arm64-msvc: 1.29.1
      lightningcss-win32-x64-msvc: 1.29.1

  lilconfig@2.1.0: {}

  lilconfig@3.1.3: {}

  lines-and-columns@1.2.4: {}

  lint-staged@15.4.3:
    dependencies:
      chalk: 5.4.1
      commander: 13.1.0
      debug: 4.4.0
      execa: 8.0.1
      lilconfig: 3.1.3
      listr2: 8.2.5
      micromatch: 4.0.8
      pidtree: 0.6.0
      string-argv: 0.3.2
      yaml: 2.7.0
    transitivePeerDependencies:
      - supports-color

  listr2@8.2.5:
    dependencies:
      cli-truncate: 4.0.0
      colorette: 2.0.20
      eventemitter3: 5.0.1
      log-update: 6.1.0
      rfdc: 1.4.1
      wrap-ansi: 9.0.0

  little-state-machine@3.1.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  load-json-file@6.2.0:
    dependencies:
      graceful-fs: 4.2.11
      parse-json: 5.2.0
      strip-bom: 4.0.0
      type-fest: 0.6.0

  local-pkg@1.1.1:
    dependencies:
      mlly: 1.7.4
      pkg-types: 2.1.0
      quansync: 0.2.10

  locate-character@3.0.0: {}

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash-es@4.17.21: {}

  lodash-node@2.4.1: {}

  lodash.castarray@4.4.0: {}

  lodash.isplainobject@4.0.6: {}

  lodash.merge@4.6.2: {}

  lodash.mergewith@4.6.2: {}

  lodash.pick@4.4.0: {}

  lodash@2.4.2: {}

  lodash@3.10.1: {}

  lodash@4.17.21: {}

  log-symbols@4.1.0:
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0

  log-update@6.1.0:
    dependencies:
      ansi-escapes: 7.0.0
      cli-cursor: 5.0.0
      slice-ansi: 7.1.0
      strip-ansi: 7.1.0
      wrap-ansi: 9.0.0

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  loupe@3.1.3: {}

  lru-cache@10.4.3: {}

  lru-cache@2.7.3: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0

  lz-string@1.5.0: {}

  magic-string@0.30.17:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  magic-string@0.30.8:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  magicast@0.3.5:
    dependencies:
      '@babel/parser': 7.25.6
      '@babel/types': 7.25.6
      source-map-js: 1.2.1

  make-dir@2.1.0:
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    optional: true

  make-dir@3.1.0:
    dependencies:
      semver: 6.3.1

  make-dir@4.0.0:
    dependencies:
      semver: 7.6.3

  map-obj@1.0.1: {}

  map-obj@4.3.0: {}

  meow@6.1.1:
    dependencies:
      '@types/minimist': 1.2.5
      camelcase-keys: 6.2.2
      decamelize-keys: 1.1.1
      hard-rejection: 2.1.0
      minimist-options: 4.1.0
      normalize-package-data: 2.5.0
      read-pkg-up: 7.0.1
      redent: 3.0.0
      trim-newlines: 3.0.1
      type-fest: 0.13.1
      yargs-parser: 18.1.3

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0:
    optional: true

  mimic-fn@2.1.0: {}

  mimic-fn@4.0.0: {}

  mimic-function@5.0.1: {}

  min-indent@1.0.1: {}

  mini-store@3.0.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      hoist-non-react-statics: 3.3.2
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      shallowequal: 1.1.0

  mini-svg-data-uri@1.4.4: {}

  minimatch@0.2.14:
    dependencies:
      lru-cache: 2.7.3
      sigmund: 1.0.1

  minimatch@10.0.1:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@3.0.8:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@8.0.4:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist-options@4.1.0:
    dependencies:
      arrify: 1.0.1
      is-plain-obj: 1.1.0
      kind-of: 6.0.3

  minimist@1.1.3: {}

  minimist@1.2.8: {}

  minipass@4.2.8: {}

  minipass@7.1.2: {}

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  mkdirp@1.0.4: {}

  mlly@1.7.4:
    dependencies:
      acorn: 8.14.1
      pathe: 2.0.2
      pkg-types: 1.3.1
      ufo: 1.5.4

  moment-timezone@0.5.45:
    dependencies:
      moment: 2.30.1

  moment@2.30.1: {}

  mri@1.2.0: {}

  mrmime@2.0.1: {}

  ms@2.0.0: {}

  ms@2.1.3: {}

  muggle-string@0.4.1: {}

  nan@2.20.0:
    optional: true

  nanoid@3.3.8: {}

  natural-compare@1.4.0: {}

  needle@3.3.1:
    dependencies:
      iconv-lite: 0.6.3
      sax: 1.4.1
    optional: true

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 5.0.0

  node-releases@2.0.18: {}

  node-releases@2.0.19: {}

  normalize-package-data@2.5.0:
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.8
      semver: 5.7.2
      validate-npm-package-license: 3.0.4

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  npm-run-path@5.3.0:
    dependencies:
      path-key: 4.0.0

  numbro@2.3.1:
    dependencies:
      bignumber.js: 8.1.1

  nwsapi@2.2.16: {}

  object-assign@2.0.0: {}

  object-assign@4.1.1: {}

  omit.js@2.0.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  onetime@6.0.0:
    dependencies:
      mimic-fn: 4.0.0

  onetime@7.0.0:
    dependencies:
      mimic-function: 5.0.1

  opentracing@0.14.7: {}

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  ora@5.4.1:
    dependencies:
      bl: 4.1.0
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-spinners: 2.9.2
      is-interactive: 1.0.0
      is-unicode-supported: 0.1.0
      log-symbols: 4.1.0
      strip-ansi: 6.0.1
      wcwidth: 1.0.1

  output-file-sync@1.1.2:
    dependencies:
      graceful-fs: 4.2.11
      mkdirp: 0.5.6
      object-assign: 4.1.1

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-try@2.2.0: {}

  package-json-from-dist@1.0.0: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.24.7
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse-ms@4.0.0: {}

  parse-node-version@1.0.1: {}

  parse5@7.2.1:
    dependencies:
      entities: 4.5.0

  path-browserify@1.0.1: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-key@4.0.0: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-type@4.0.0: {}

  pathe@2.0.2: {}

  pathe@2.0.3: {}

  pathval@2.0.0: {}

  performance-now@2.1.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  pidtree@0.6.0: {}

  pify@4.0.1:
    optional: true

  pify@5.0.0: {}

  pkg-types@1.3.1:
    dependencies:
      confbox: 0.1.8
      mlly: 1.7.4
      pathe: 2.0.2

  pkg-types@2.1.0:
    dependencies:
      confbox: 0.2.1
      exsolve: 1.0.4
      pathe: 2.0.3

  postcss-load-config@3.1.4(postcss@8.5.1):
    dependencies:
      lilconfig: 2.1.0
      yaml: 1.10.2
    optionalDependencies:
      postcss: 8.5.1

  postcss-rem-to-pixel@4.1.2:
    dependencies:
      object-assign: 4.1.1
      postcss: 5.2.18

  postcss-safe-parser@7.0.1(postcss@8.5.1):
    dependencies:
      postcss: 8.5.1

  postcss-scss@4.0.9(postcss@8.5.1):
    dependencies:
      postcss: 8.5.1

  postcss-selector-parser@6.0.10:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-selector-parser@7.1.0:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@5.2.18:
    dependencies:
      chalk: 1.1.3
      js-base64: 2.6.4
      source-map: 0.5.7
      supports-color: 3.2.3

  postcss@8.5.1:
    dependencies:
      nanoid: 3.3.8
      picocolors: 1.1.1
      source-map-js: 1.2.1

  posthog-js@1.245.1:
    dependencies:
      core-js: 3.42.0
      fflate: 0.4.8
      preact: 10.26.6
      web-vitals: 4.2.4

  preact@10.26.6: {}

  prelude-ls@1.2.1: {}

  prettier-plugin-svelte@3.4.0(prettier@3.5.1)(svelte@5.32.0):
    dependencies:
      prettier: 3.5.1
      svelte: 5.32.0

  prettier-plugin-tailwindcss@0.6.11(prettier-plugin-svelte@3.4.0(prettier@3.5.1)(svelte@5.32.0))(prettier@3.5.1):
    dependencies:
      prettier: 3.5.1
    optionalDependencies:
      prettier-plugin-svelte: 3.4.0(prettier@3.5.1)(svelte@5.32.0)

  prettier@3.5.1: {}

  pretty-format@25.5.0:
    dependencies:
      '@jest/types': 25.5.0
      ansi-regex: 5.0.1
      ansi-styles: 4.3.0
      react-is: 16.13.1

  pretty-format@26.6.2:
    dependencies:
      '@jest/types': 26.6.2
      ansi-regex: 5.0.1
      ansi-styles: 4.3.0
      react-is: 17.0.2

  pretty-format@27.5.1:
    dependencies:
      ansi-regex: 5.0.1
      ansi-styles: 5.2.0
      react-is: 17.0.2

  pretty-format@29.7.0:
    dependencies:
      '@jest/schemas': 29.6.3
      ansi-styles: 5.2.0
      react-is: 18.3.1

  pretty-ms@9.0.0:
    dependencies:
      parse-ms: 4.0.0

  private@0.1.8: {}

  progress@2.0.3: {}

  promise-polyfill@8.3.0: {}

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  property-expr@2.0.6: {}

  proxy-compare@3.0.1: {}

  proxy-from-env@1.1.0: {}

  prr@1.0.1:
    optional: true

  psl@1.9.0: {}

  pump@3.0.0:
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  punycode@2.3.1: {}

  q@1.5.1: {}

  quansync@0.2.10: {}

  querystringify@2.2.0: {}

  queue-microtask@1.2.3: {}

  quick-lru@4.0.1: {}

  raf@3.4.1:
    dependencies:
      performance-now: 2.1.0

  randexp@0.5.3:
    dependencies:
      drange: 1.1.1
      ret: 0.2.2

  rc-align@4.0.15(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      dom-align: 1.12.4
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      resize-observer-polyfill: 1.5.1

  rc-animate@3.1.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@ant-design/css-animation': 1.7.3
      classnames: 2.2.6
      raf: 3.4.1
      rc-util: 4.21.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-cascader@1.4.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      array-tree-filter: 2.1.0
      rc-trigger: 5.3.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      warning: 4.0.3

  rc-checkbox@2.3.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-collapse@2.0.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@ant-design/css-animation': 1.7.3
      classnames: 2.2.6
      rc-animate: 3.1.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      shallowequal: 1.1.0
    transitivePeerDependencies:
      - react
      - react-dom

  rc-dialog@8.2.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      rc-animate: 3.1.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-drawer@4.1.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
    transitivePeerDependencies:
      - react-dom

  rc-dropdown@3.2.5(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-trigger: 5.3.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-field-form@1.10.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      async-validator: 3.5.2
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
    transitivePeerDependencies:
      - react-dom

  rc-image@3.0.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@ant-design/icons': 4.8.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-dialog: 8.2.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-input-number@6.0.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
    transitivePeerDependencies:
      - react
      - react-dom

  rc-mentions@1.5.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-menu: 8.7.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-textarea: 0.3.7(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-trigger: 5.3.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-menu@8.7.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      mini-store: 3.0.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      omit.js: 2.0.2
      rc-motion: 2.9.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-trigger: 5.3.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      resize-observer-polyfill: 1.5.1
      shallowequal: 1.1.0
    transitivePeerDependencies:
      - react
      - react-dom

  rc-motion@2.9.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-notification@4.4.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-animate: 3.1.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-pagination@3.0.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-picker@2.1.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      date-fns: 2.30.0
      dayjs: 1.11.11
      moment: 2.30.1
      rc-trigger: 5.3.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      shallowequal: 1.1.0
    transitivePeerDependencies:
      - react
      - react-dom

  rc-progress@3.1.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-rate@2.8.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
    transitivePeerDependencies:
      - react
      - react-dom

  rc-resize-observer@0.2.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      resize-observer-polyfill: 1.5.1

  rc-resize-observer@1.4.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      resize-observer-polyfill: 1.5.1

  rc-select@11.3.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-motion: 2.9.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-trigger: 5.3.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-virtual-list: 3.14.5(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      warning: 4.0.3

  rc-slider@9.5.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-tooltip: 5.0.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      shallowequal: 1.1.0

  rc-steps@4.1.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-switch@3.2.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-table@7.9.10(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      raf: 3.4.1
      rc-resize-observer: 0.2.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      shallowequal: 1.1.0

  rc-tabs@11.6.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      raf: 3.4.1
      rc-dropdown: 3.2.5(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-menu: 8.7.1(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-resize-observer: 0.2.6(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
    transitivePeerDependencies:
      - react
      - react-dom

  rc-textarea@0.3.7(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-resize-observer: 1.4.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      shallowequal: 1.1.0

  rc-tooltip@5.0.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      rc-trigger: 5.3.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-tree-select@4.1.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-select: 11.3.3(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-tree: 3.10.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-tree@3.10.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-motion: 2.9.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-virtual-list: 3.14.5(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-trigger@5.0.9(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-align: 4.0.15(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-motion: 2.9.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
    transitivePeerDependencies:
      - react
      - react-dom

  rc-trigger@5.3.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-align: 4.0.15(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-motion: 2.9.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-upload@3.3.4(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  rc-util@4.21.1:
    dependencies:
      add-dom-event-listener: 1.1.0
      prop-types: 15.8.1
      react-is: 16.13.1
      react-lifecycles-compat: 3.0.4
      shallowequal: 1.1.0

  rc-util@5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-is: 18.3.1

  rc-virtual-list@3.14.5(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.2.6
      rc-resize-observer: 1.4.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  react-abac@0.0.7(react@16.14.0):
    dependencies:
      react: 16.14.0

  react-dom@16.14.0(react@16.14.0):
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1
      react: 16.14.0
      scheduler: 0.19.1

  react-fast-compare@2.0.4: {}

  react-hook-form@6.15.8(react@16.14.0):
    dependencies:
      react: 16.14.0

  react-i18next@11.6.0(i18next@19.3.4)(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.7
      html-parse-stringify2: 2.0.1
      i18next: 19.3.4
      react: 16.14.0

  react-is@16.13.1: {}

  react-is@17.0.2: {}

  react-is@18.3.1: {}

  react-lifecycles-compat@3.0.4: {}

  react-redux@7.2.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)(redux@4.2.1):
    dependencies:
      '@babel/runtime': 7.24.7
      hoist-non-react-statics: 3.3.2
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 16.14.0
      react-is: 16.13.1
      redux: 4.2.1
    optionalDependencies:
      react-dom: 16.14.0(react@16.14.0)

  react-simple-animate@3.5.2(react-dom@16.14.0(react@16.14.0)):
    dependencies:
      react-dom: 16.14.0(react@16.14.0)

  react-split@2.0.14(react@16.14.0):
    dependencies:
      prop-types: 15.8.1
      react: 16.14.0
      split.js: 1.6.5

  react-test-renderer@16.13.0(react@16.14.0):
    dependencies:
      object-assign: 4.1.1
      prop-types: 15.8.1
      react: 16.14.0
      react-is: 16.13.1
      scheduler: 0.19.1

  react@16.14.0:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1

  read-babelrc-up@1.1.0:
    dependencies:
      find-up: 4.1.0
      json5: 2.2.3

  read-pkg-up@7.0.1:
    dependencies:
      find-up: 4.1.0
      read-pkg: 5.2.0
      type-fest: 0.8.1

  read-pkg@5.2.0:
    dependencies:
      '@types/normalize-package-data': 2.4.4
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0

  readable-stream@1.0.34:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 0.0.1
      string_decoder: 0.10.31

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdirp@1.3.0:
    dependencies:
      graceful-fs: 2.0.3
      minimatch: 0.2.14
      readable-stream: 1.0.34

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  readdirp@4.1.2: {}

  recast@0.10.43:
    dependencies:
      ast-types: 0.8.15
      esprima-fb: 15001.1001.0-dev-harmony-fb
      private: 0.1.8
      source-map: 0.5.7

  recast@0.11.23:
    dependencies:
      ast-types: 0.9.6
      esprima: 3.1.3
      private: 0.1.8
      source-map: 0.5.7

  redent@3.0.0:
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0

  redux-injectors@1.3.0(react-dom@16.14.0(react@16.14.0))(react-redux@7.2.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)(redux@4.2.1))(react@16.14.0):
    dependencies:
      hoist-non-react-statics: 3.3.2
      invariant: 2.2.4
      lodash: 4.17.21
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-redux: 7.2.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)(redux@4.2.1)
      redux: 4.2.1

  redux-saga@1.1.3:
    dependencies:
      '@redux-saga/core': 1.3.0

  redux-thunk@2.4.2(redux@4.2.1):
    dependencies:
      redux: 4.2.1

  redux@4.2.1:
    dependencies:
      '@babel/runtime': 7.24.7

  regenerate@1.4.2: {}

  regenerator-babel@0.8.10-2:
    dependencies:
      ast-types: 0.6.16
      commoner: 0.10.8
      private: 0.1.8
      through: 2.3.8

  regenerator-runtime@0.14.1: {}

  regexpu@1.3.0:
    dependencies:
      esprima: 2.7.3
      recast: 0.10.43
      regenerate: 1.4.2
      regjsgen: 0.2.0
      regjsparser: 0.1.5

  regjsgen@0.2.0: {}

  regjsparser@0.1.5:
    dependencies:
      jsesc: 0.5.0

  repeating@1.1.3:
    dependencies:
      is-finite: 1.1.0

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  requires-port@1.0.0: {}

  reselect@4.1.8: {}

  resize-observer-polyfill@1.5.1: {}

  resolve-from@4.0.0: {}

  resolve@1.22.8:
    dependencies:
      is-core-module: 2.15.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@3.1.0:
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  restore-cursor@5.1.0:
    dependencies:
      onetime: 7.0.0
      signal-exit: 4.1.0

  ret@0.2.2: {}

  reusify@1.0.4: {}

  rfdc@1.4.1: {}

  rollup@4.34.4:
    dependencies:
      '@types/estree': 1.0.6
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.34.4
      '@rollup/rollup-android-arm64': 4.34.4
      '@rollup/rollup-darwin-arm64': 4.34.4
      '@rollup/rollup-darwin-x64': 4.34.4
      '@rollup/rollup-freebsd-arm64': 4.34.4
      '@rollup/rollup-freebsd-x64': 4.34.4
      '@rollup/rollup-linux-arm-gnueabihf': 4.34.4
      '@rollup/rollup-linux-arm-musleabihf': 4.34.4
      '@rollup/rollup-linux-arm64-gnu': 4.34.4
      '@rollup/rollup-linux-arm64-musl': 4.34.4
      '@rollup/rollup-linux-loongarch64-gnu': 4.34.4
      '@rollup/rollup-linux-powerpc64le-gnu': 4.34.4
      '@rollup/rollup-linux-riscv64-gnu': 4.34.4
      '@rollup/rollup-linux-s390x-gnu': 4.34.4
      '@rollup/rollup-linux-x64-gnu': 4.34.4
      '@rollup/rollup-linux-x64-musl': 4.34.4
      '@rollup/rollup-win32-arm64-msvc': 4.34.4
      '@rollup/rollup-win32-ia32-msvc': 4.34.4
      '@rollup/rollup-win32-x64-msvc': 4.34.4
      fsevents: 2.3.3

  rrweb-cssom@0.8.0: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  runed@0.23.4(svelte@5.32.0):
    dependencies:
      esm-env: 1.2.2
      svelte: 5.32.0

  sade@1.8.1:
    dependencies:
      mri: 1.2.0

  safe-buffer@5.2.1: {}

  safer-buffer@2.1.2: {}

  sanitize.css@11.0.0: {}

  sass@1.77.6:
    dependencies:
      chokidar: 3.6.0
      immutable: 4.3.6
      source-map-js: 1.2.1

  sax@1.4.1:
    optional: true

  saxes@6.0.0:
    dependencies:
      xmlchars: 2.2.0

  scheduler@0.19.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1

  schema-utils@2.7.1:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  scroll-into-view-if-needed@2.2.31:
    dependencies:
      compute-scroll-into-view: 1.0.20

  semver@5.7.2: {}

  semver@6.3.1: {}

  semver@7.5.4:
    dependencies:
      lru-cache: 6.0.0

  semver@7.6.3: {}

  set-cookie-parser@2.6.0: {}

  shallowequal@1.1.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@1.0.0: {}

  shebang-regex@3.0.0: {}

  siginfo@2.0.0: {}

  sigmund@1.0.1: {}

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  sirv@3.0.1:
    dependencies:
      '@polka/url': 1.0.0-next.28
      mrmime: 2.0.1
      totalist: 3.0.1

  slash@1.0.0: {}

  slice-ansi@5.0.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 4.0.0

  slice-ansi@7.1.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 5.0.0

  smol-toml@1.3.1: {}

  sort-keys@4.2.0:
    dependencies:
      is-plain-obj: 2.1.0

  source-map-js@1.2.1: {}

  source-map-support@0.2.10:
    dependencies:
      source-map: 0.1.32

  source-map-to-comment@1.1.0: {}

  source-map@0.1.32:
    dependencies:
      amdefine: 1.0.1

  source-map@0.1.43:
    dependencies:
      amdefine: 1.0.1

  source-map@0.5.7: {}

  source-map@0.6.1: {}

  spare-bones@0.2.0(svelte@5.32.0):
    dependencies:
      '@zag-js/menu': 1.7.0
      '@zag-js/svelte': 1.7.0(svelte@5.32.0)
      svelte: 5.32.0

  spdx-correct@3.2.0:
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.18

  spdx-exceptions@2.5.0: {}

  spdx-expression-parse@3.0.1:
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.18

  spdx-license-ids@3.0.18: {}

  split.js@1.6.5: {}

  sprintf-js@1.0.3: {}

  stackback@0.0.2: {}

  stackframe@0.3.1: {}

  std-env@3.8.0: {}

  string-argv@0.3.2: {}

  string-convert@0.2.1: {}

  string-ts@2.2.1: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string-width@7.2.0:
    dependencies:
      emoji-regex: 10.4.0
      get-east-asian-width: 1.3.0
      strip-ansi: 7.1.0

  string_decoder@0.10.31: {}

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@0.3.0:
    dependencies:
      ansi-regex: 0.2.1

  strip-ansi@3.0.1:
    dependencies:
      ansi-regex: 2.1.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-bom@4.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-final-newline@3.0.0: {}

  strip-indent@3.0.0:
    dependencies:
      min-indent: 1.0.1

  strip-json-comments@3.1.1: {}

  strip-json-comments@5.0.1: {}

  style-to-object@1.0.8:
    dependencies:
      inline-style-parser: 0.2.4

  supports-color@0.2.0: {}

  supports-color@2.0.0: {}

  supports-color@3.2.3:
    dependencies:
      has-flag: 1.0.0

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  svelte-check@4.2.1(picomatch@4.0.2)(svelte@5.32.0)(typescript@5.5.2):
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      chokidar: 4.0.3
      fdir: 6.4.3(picomatch@4.0.2)
      picocolors: 1.1.1
      sade: 1.8.1
      svelte: 5.32.0
      typescript: 5.5.2
    transitivePeerDependencies:
      - picomatch

  svelte-eslint-parser@1.2.0(svelte@5.32.0):
    dependencies:
      eslint-scope: 8.3.0
      eslint-visitor-keys: 4.2.0
      espree: 10.3.0
      postcss: 8.5.1
      postcss-scss: 4.0.9(postcss@8.5.1)
      postcss-selector-parser: 7.1.0
    optionalDependencies:
      svelte: 5.32.0

  svelte-toolbelt@0.7.1(svelte@5.32.0):
    dependencies:
      clsx: 2.1.1
      runed: 0.23.4(svelte@5.32.0)
      style-to-object: 1.0.8
      svelte: 5.32.0

  svelte@5.32.0:
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@jridgewell/sourcemap-codec': 1.5.0
      '@sveltejs/acorn-typescript': 1.0.5(acorn@8.14.0)
      '@types/estree': 1.0.6
      acorn: 8.14.0
      aria-query: 5.3.1
      axobject-query: 4.1.0
      clsx: 2.1.1
      esm-env: 1.2.2
      esrap: 1.4.6
      is-reference: 3.0.3
      locate-character: 3.0.0
      magic-string: 0.30.17
      zimmerframe: 1.1.2

  symbol-tree@3.2.4: {}

  synchronous-promise@2.0.17: {}

  tabbable@6.2.0: {}

  tailwindcss@4.0.9: {}

  tapable@2.2.1: {}

  test-exclude@7.0.1:
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 10.4.5
      minimatch: 9.0.5

  through2@0.6.5:
    dependencies:
      readable-stream: 1.0.34
      xtend: 4.0.2

  through@2.3.8: {}

  tiny-warning@1.0.3: {}

  tinybench@2.9.0: {}

  tinycolor2@1.6.0: {}

  tinyexec@0.3.2: {}

  tinypool@1.0.2: {}

  tinyrainbow@2.0.0: {}

  tinyspy@3.0.2: {}

  tldts-core@6.1.76: {}

  tldts@6.1.76:
    dependencies:
      tldts-core: 6.1.76

  to-fast-properties@2.0.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toggle-selection@1.0.6: {}

  toposort@2.0.2: {}

  totalist@3.0.1: {}

  tough-cookie@4.1.4:
    dependencies:
      psl: 1.9.0
      punycode: 2.3.1
      universalify: 0.2.0
      url-parse: 1.5.10

  tough-cookie@5.1.0:
    dependencies:
      tldts: 6.1.76

  tr46@0.0.3: {}

  tr46@5.0.0:
    dependencies:
      punycode: 2.3.1

  trim-newlines@3.0.1: {}

  trim-right@1.0.1: {}

  ts-api-utils@2.0.1(typescript@5.5.2):
    dependencies:
      typescript: 5.5.2

  ts-declaration-location@1.0.5(typescript@5.5.2):
    dependencies:
      minimatch: 10.0.1
      typescript: 5.5.2

  ts-pattern@5.6.2: {}

  tsconfck@3.1.3(typescript@5.5.2):
    optionalDependencies:
      typescript: 5.5.2

  tslib@2.6.3: {}

  tslib@2.8.1: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.13.1: {}

  type-fest@0.6.0: {}

  type-fest@0.8.1: {}

  typed-redux-saga@1.5.0(redux-saga@1.1.3):
    dependencies:
      redux-saga: 1.1.3
    optionalDependencies:
      '@babel/helper-module-imports': 7.24.7
      babel-plugin-macros: 3.1.0
    transitivePeerDependencies:
      - supports-color

  typedarray-to-buffer@3.1.5:
    dependencies:
      is-typedarray: 1.0.0

  typescript-compare@0.0.2:
    dependencies:
      typescript-logic: 0.0.0

  typescript-eslint@8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2):
    dependencies:
      '@typescript-eslint/eslint-plugin': 8.23.0(@typescript-eslint/parser@8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2))(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/parser': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      '@typescript-eslint/utils': 8.23.0(eslint@9.19.0(jiti@2.4.2))(typescript@5.5.2)
      eslint: 9.19.0(jiti@2.4.2)
      typescript: 5.5.2
    transitivePeerDependencies:
      - supports-color

  typescript-logic@0.0.0: {}

  typescript-strict-plugin@2.4.4:
    dependencies:
      chalk: 3.0.0
      execa: 4.1.0
      minimatch: 9.0.5
      ora: 5.4.1
      yargs: 16.2.0

  typescript-tuple@2.2.1:
    dependencies:
      typescript-compare: 0.0.2

  typescript@4.9.5: {}

  typescript@5.5.2: {}

  typescript@5.8.2: {}

  ufo@1.5.4: {}

  undici-types@5.26.5: {}

  universalify@0.2.0: {}

  universalify@2.0.1: {}

  unplugin@1.0.1:
    dependencies:
      acorn: 8.14.0
      chokidar: 3.6.0
      webpack-sources: 3.2.3
      webpack-virtual-modules: 0.5.0

  update-browserslist-db@1.1.0(browserslist@4.23.3):
    dependencies:
      browserslist: 4.23.3
      escalade: 3.1.2
      picocolors: 1.1.1

  update-browserslist-db@1.1.3(browserslist@4.24.4):
    dependencies:
      browserslist: 4.24.4
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  util-deprecate@1.0.2: {}

  uuid@11.1.0: {}

  uuid@8.3.2: {}

  validate-npm-package-license@3.0.4:
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1

  vite-node@3.0.5(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0):
    dependencies:
      cac: 6.7.14
      debug: 4.4.0
      es-module-lexer: 1.6.0
      pathe: 2.0.2
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
    transitivePeerDependencies:
      - '@types/node'
      - jiti
      - less
      - lightningcss
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  vite-plugin-devtools-json@0.1.0(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)):
    dependencies:
      uuid: 11.1.0
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)

  vite-plugin-dts@4.5.3(@types/node@20.14.8)(rollup@4.34.4)(typescript@5.5.2)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)):
    dependencies:
      '@microsoft/api-extractor': 7.52.1(@types/node@20.14.8)
      '@rollup/pluginutils': 5.1.4(rollup@4.34.4)
      '@volar/typescript': 2.4.12
      '@vue/language-core': 2.2.0(typescript@5.5.2)
      compare-versions: 6.1.1
      debug: 4.4.0
      kolorist: 1.8.0
      local-pkg: 1.1.1
      magic-string: 0.30.17
      typescript: 5.5.2
    optionalDependencies:
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
    transitivePeerDependencies:
      - '@types/node'
      - rollup
      - supports-color

  vite-tsconfig-paths@5.1.4(typescript@5.5.2)(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)):
    dependencies:
      debug: 4.4.0
      globrex: 0.1.2
      tsconfck: 3.1.3(typescript@5.5.2)
    optionalDependencies:
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
    transitivePeerDependencies:
      - supports-color
      - typescript

  vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0):
    dependencies:
      esbuild: 0.24.2
      postcss: 8.5.1
      rollup: 4.34.4
    optionalDependencies:
      '@types/node': 20.14.8
      fsevents: 2.3.3
      jiti: 2.4.2
      less: 4.2.0
      lightningcss: 1.29.1
      sass: 1.77.6
      yaml: 2.7.0

  vitefu@1.0.6(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)):
    optionalDependencies:
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)

  vitest@3.0.5(@types/node@20.14.8)(jiti@2.4.2)(jsdom@26.0.0)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0):
    dependencies:
      '@vitest/expect': 3.0.5
      '@vitest/mocker': 3.0.5(vite@6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0))
      '@vitest/pretty-format': 3.0.5
      '@vitest/runner': 3.0.5
      '@vitest/snapshot': 3.0.5
      '@vitest/spy': 3.0.5
      '@vitest/utils': 3.0.5
      chai: 5.1.2
      debug: 4.4.0
      expect-type: 1.1.0
      magic-string: 0.30.17
      pathe: 2.0.2
      std-env: 3.8.0
      tinybench: 2.9.0
      tinyexec: 0.3.2
      tinypool: 1.0.2
      tinyrainbow: 2.0.0
      vite: 6.1.0(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
      vite-node: 3.0.5(@types/node@20.14.8)(jiti@2.4.2)(less@4.2.0)(lightningcss@1.29.1)(sass@1.77.6)(yaml@2.7.0)
      why-is-node-running: 2.3.0
    optionalDependencies:
      '@types/node': 20.14.8
      jsdom: 26.0.0
    transitivePeerDependencies:
      - jiti
      - less
      - lightningcss
      - msw
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  void-elements@2.0.1: {}

  vscode-uri@3.1.0: {}

  w3c-xmlserializer@5.0.0:
    dependencies:
      xml-name-validator: 5.0.0

  warning@4.0.3:
    dependencies:
      loose-envify: 1.4.0

  wcwidth@1.0.1:
    dependencies:
      defaults: 1.0.4

  web-vitals@4.2.4: {}

  webidl-conversions@3.0.1: {}

  webidl-conversions@7.0.0: {}

  webpack-sources@3.2.3: {}

  webpack-virtual-modules@0.5.0: {}

  whatwg-encoding@3.1.1:
    dependencies:
      iconv-lite: 0.6.3

  whatwg-mimetype@4.0.0: {}

  whatwg-url@14.1.0:
    dependencies:
      tr46: 5.0.0
      webidl-conversions: 7.0.0

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  why-is-node-running@2.3.0:
    dependencies:
      siginfo: 2.0.0
      stackback: 0.0.2

  word-wrap@1.2.5: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrap-ansi@9.0.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 7.2.0
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  write-file-atomic@3.0.3:
    dependencies:
      imurmurhash: 0.1.4
      is-typedarray: 1.0.0
      signal-exit: 3.0.7
      typedarray-to-buffer: 3.1.5

  write-json-file@4.3.0:
    dependencies:
      detect-indent: 6.1.0
      graceful-fs: 4.2.11
      is-plain-obj: 2.1.0
      make-dir: 3.1.0
      sort-keys: 4.2.0
      write-file-atomic: 3.0.3

  ws@7.5.10: {}

  ws@8.18.0: {}

  xml-name-validator@5.0.0: {}

  xmlchars@2.2.0: {}

  xtend@4.0.2: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@4.0.0: {}

  yaml@1.10.2: {}

  yaml@2.7.0: {}

  yargs-parser@18.1.3:
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  yargs-parser@20.2.9: {}

  yargs@16.2.0:
    dependencies:
      cliui: 7.0.4
      escalade: 3.1.2
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9

  yocto-queue@0.1.0: {}

  yup@0.29.1:
    dependencies:
      '@babel/runtime': 7.24.7
      fn-name: 3.0.0
      lodash: 4.17.21
      lodash-es: 4.17.21
      property-expr: 2.0.6
      synchronous-promise: 2.0.17
      toposort: 2.0.2

  zimmerframe@1.1.2: {}

  zod-fixture@2.5.2(zod@3.24.2):
    dependencies:
      randexp: 0.5.3
      zod: 3.24.2

  zod-validation-error@3.3.0(zod@3.24.2):
    dependencies:
      zod: 3.24.2

  zod@3.24.2: {}
