# HrvystEdge-FE

Front End Repository RJO HrvystHedge

## First Steps

- Clone this repo
- If not already installed, run `npm install -g pnpm`
  - If `pnpm` is not a recognized command, you need to add its install location to your PATH environment variable
  - Run `npm list -g pnpm` to see the install location that needs to be added to PATH
  - If using VSCode's integrated terminal, restart VSCode for the new PATH to take effect
- Enter to "HrvystEdge-FE" folder and run `pnpm install`
- Start the development server with the command `pnpm dev`

## Main Scripts

- `pnpm dev` to build the react lib and start the svelte dev server
  - if you already have a build of hrvyst-react, it will prompt if you want to rebuild or use the old build
  - note: changes to the hrvyst-react are NOT hot reloaded
  - if you edit hrvyst-react, you must restart this command (ie rebuild hrvyst-react and restart the svelte dev server)
- `pnpm lint` to run all linting and typechecking
- `pnpm test` to run all the unit tests
- `pnpm build` to generate a production version

This project follows ESLint script naming conventions: https://eslint.org/docs/latest/contribute/package-json-conventions

## Config for Running Locally

The app loads environment config (backend URL, etc) from `packages/hrvyst-app/static/runtime-config.js`.
To change this (eg run against local backend) either:

- edit the `runtime-config.js` file, or
- edit the `<script>` tag in `packages/hrvyst-app/src/app.html` to load a different file (eg `/runtime-config.Development.LocalOrderService.js`)

See "Notes: Environment Variables and Config" below for mo

## Packages

This is a multi-package repo managed by `pnpm`. All packages are in `./packages`.

- `hryvst-app`: the main sveltekit app
- `hrvyst-react`: library of legacy react components
- `hrvyst-gen`: autogenerated types and schemas
- `react-intl`: minimal, custom react-intl implementation

## Notes: Environment Variables and Config

Variables and config come from a few different places. First we need to disinguish if this is something that we need access at runtime, or just in scripts used in the pipelines.

### Runtime

Vite inlines environment variables at build time. However, we currently build only once for all environments, so the approach is different for environment variables that are static across deploy envs (but we don't want the values hard-coded for security or convenience purposes) vs variables that are different based on deploy env.

- **Static**: These can use [Vite's built-in method](https://vite.dev/guide/env-and-mode) of inlining variables at build time. Note that variables must be prefixed with `VITE_` (if needed, a .env file can be used to map a non-prefixed variable to a prefixed one).
- **Deploy-env-specific**: These are loaded via `packages/hrvyst-app/static/runtime-config.js` via `packages/hrvyst-app/src/app.html`. At deploy time, this file is swapped out with the appropriate `runtime-config.ENV.js` file, and some environment variables (defined in the rjo.<dev/qas/st/pr> [Variables Groups](https://dev.azure.com/rjobrien/Hrvyst-DevOps/_library?itemType=VariableGroups)) are inserted at deploy time via a powershell script in deployment-template.yml.

### Pipeline

- **Static**: these are loaded via the [rjo.global Variable Group](https://dev.azure.com/rjobrien/Hrvyst-DevOps/_library?itemType=VariableGroups&view=VariableGroupView&variableGroupId=1&path=rjo.global)
- **Deploy-env-specific**: these are loaded via the rjo.<dev/qas/st/pr> [Variable Gruops](https://dev.azure.com/rjobrien/Hrvyst-DevOps/_library?itemType=VariableGroups), but are only available during the deploy job, not build

### Other Notes

- **Plain Text vs Secret**: if a variable in a variable group has been marked secret, it must be explicitly mapped in the pipeline. [See the Auzre DevOps documentation.](https://learn.microsoft.com/en-us/azure/devops/pipelines/process/variables?view=azure-devops&tabs=yaml%2Cbatch#reference-secret-variables-in-variable-groups)
- **App Services Environment Variables**: variables can be set in the App Services ([dev, for example](https://portal.azure.com/#@hrvyst-azure.com/resource/subscriptions/b8773b71-de29-4da9-a34e-ca0fd3ad441e/resourceGroups/dv-eastus-rjohrvystdemoapp-rg/providers/Microsoft.Web/sites/dv-rjohrvystdemo-app/environmentVariablesAppSettings)). These are available for the deployed app only, and are not used by the frontend.
- **Pipeline Variables**: variables can be set for the pipeline in the Azure Devops UI, by pressing the "Variables" button when editing a pipeline. This are not currently used.
