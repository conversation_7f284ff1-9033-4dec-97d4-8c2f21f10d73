/**
 * this script collects all the shards into the expected directory
 * azure pipelines downloads each artifact to its own subdirectory
 * vitest expects all the report shards to be in a single flat directory
 * (the folder vitest checks can be configured, but regardless it expects a flat structure)
 */

import fs from 'fs';
import path from 'path';

const sourecDir = process.env.PIPELINE_WORKSPACE;
const targetDir = path.join(
	process.env.SYSTEM_DEFAULTWORKINGDIRECTORY,
	'packages/hrvyst-react/.vitest-reports'
);
fs.mkdirSync(targetDir);

for (const relativePath of fs.readdirSync(sourecDir, {
	recursive: true,
})) {
	if (relativePath.match(/coverage-shard-\d+\.json$/)) {
		const sourcePath = path.join(sourecDir, relativePath);
		const filename = relativePath.split(path.sep).at(-1);
		const targetPath = path.join(targetDir, filename);
		console.log(`Moving ${sourcePath} to ${targetPath}`);
		fs.renameSync(sourcePath, targetPath);
	}
}
