// pnpm's built in patching functionality seems to be having issues in the Azure pipelines
// using this simple patch script instead

import * as fs from 'fs/promises';
import * as path from 'path';

// resolve symlinks
const packageJsonPath = await fs.realpath(
	path.resolve(
		'./packages/hrvyst-react/node_modules/react-hook-form/package.json'
	)
);
// read/parse package.json
const packageJson = JSON.parse(
	await fs.readFile(packageJsonPath, { encoding: 'utf-8' })
);
// add types entry exports
packageJson.exports['.'].types = './dist/index.d.ts';
// write changes
await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, undefined, 2), {
	encoding: 'utf-8',
});
