// note: this uploads sourcemaps to elastic only; sentry is handled by @sentry/vite-plugin
import fs from 'fs';
import path from 'path';

const directory = path.join(process.env.PIPELINE_WORKSPACE, 'frontend');

let uploadFailed = false;
async function handleUploadResponse(resp) {
	if (resp.ok) {
		console.log(resp.status, resp.statusText);
	} else {
		console.error(resp.status, resp.statusText);
		try {
			console.error(await resp.text());
		} catch (e) {
			console.error('No response text');
		}
		uploadFailed = true;
	}
}

for (const relativePath of fs.readdirSync(directory, {
	recursive: true,
})) {
	if (relativePath.endsWith('.map')) {
		const bundleFilepath = relativePath
			.split(path.sep)
			.map((segment) => `/${segment}`)
			.join('')
			.slice(0, -4); // drop the final `.map`
		const formData = new FormData();
		formData.append('service_name', 'hrvyst');
		formData.append('service_version', 'bundle'); // our filenames contain hashes, so no need to version this
		formData.append('bundle_filepath', bundleFilepath);
		formData.append(
			'sourcemap',
			fs.readFileSync(path.join(directory, relativePath), {
				encoding: 'utf-8',
			}),
		);
		console.log('uploading sourcemap', relativePath, bundleFilepath);
		await fetch(`${process.env.ELASTIC_API_URL}/apm/sourcemaps`, {
			headers: {
				'kbn-xsrf': 'true',
				Authorization: `ApiKey ${process.env.ELASTIC_API_KEY}`,
			},
			method: 'POST',
			body: formData,
		}).then(handleUploadResponse);
	}
}

process.exit(uploadFailed ? 1 : 0);
