# if react-hrvyst is already built, prompt for rebuild
if (Test-Path -Path "./packages/hrvyst-react/dist/lib.js") {
  while ($true) {
    # display the last build date
    $lastBuild = (Get-Item "./packages/hrvyst-react/dist/lib.js").LastWriteTime
    Write-Host "Rebuild hrvyst-react?" -nonewline
    Write-Host " (last built " -ForegroundColor DarkGray -nonewline
    Write-Host $lastBuild -ForegroundColor Yellow -nonewline
    Write-Host ", Y/N, default Y): " -ForegroundColor DarkGray -nonewline
    $response = Read-Host
    if ($response -eq "y" -or $response -eq "Y" -or $response -eq "") {
      $buildReact = $true
      break
    } elseif ($response -eq "n" -or $response -eq "N") {
      $buildReact = $false
      break
    } else {
      Write-Host "Please enter Y/N" -ForegroundColor Red
    }
  }
} else {
  # build doesn't exist, so set $buildReact to true without prompting
  $buildReact = $true
}

if ($buildReact) {
  pnpm --filter hrvyst-react build
}
pnpm --filter hrvyst-app dev