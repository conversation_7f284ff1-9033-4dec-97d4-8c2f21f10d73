import { writeFileSync } from 'fs';

// Our UI is built as a Single Page Application (SPA)
// Any route that doesn't match a file should be redirected to the route /index.hmtl
// Files with hashes in their name should be cached, and are stored in _app/immunitable
// Other files should not be hashed (except some font and audio assets)
const content = `<?xml version="1.0"?>
<configuration>
    <system.web>
        <httpRuntime maxUrlLength="10999" maxQueryStringLength="2097151" />
    </system.web>
    <system.webServer>
        <security>
            <requestFiltering>
                <requestLimits maxUrl="10999" maxQueryString="2097151" />
            </requestFiltering>
        </security>

        <staticContent>
            <!-- by default, .json files are not served; sveltekit needs uses /_app/version.json -->
            <mimeMap fileExtension=".json" mimeType="application/json" />
        </staticContent>

        <rewrite>
            <rules>
                <!-- SPA Routes - handles client-side routing -->
                <rule name="SPA Routes" stopProcessing="true">
                    <match url=".*" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="/index.html" />
                </rule>
            </rules>

            <!-- Use outbound rules for response headers -->
            <outboundRules>
                <rule name="Cache Immunitable Assets">
                    <match serverVariable="RESPONSE_Cache-Control" pattern=".*" />
                    <conditions>
                        <add input="{REQUEST_URI}" pattern="^/_app/immutable/|^/fonts/|^/audio/" />
                    </conditions>
                    <action type="Rewrite" value="public, max-age=2678400" />
                </rule>
                <rule name="No-Cache All Other Assets">
                    <match serverVariable="RESPONSE_Cache-Control" pattern=".*" />
                    <conditions>
                        <add input="{REQUEST_URI}" pattern="^/_app/immutable/|^/fonts/|^/audio/" negate="true" />
                    </conditions>
                    <action type="Rewrite" value="no-cache, no-store, must-revalidate" />
                </rule>
            </outboundRules>
        </rewrite>
    </system.webServer>
</configuration>`;

writeFileSync('./packages/hrvyst-app/build/web.config', content, {
	encoding: 'utf-8',
});
