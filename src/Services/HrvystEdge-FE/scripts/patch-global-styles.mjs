// pnpm's built in patching functionality seems to be having issues in the Azure pipelines
// using this simple patch script instead

import * as fs from 'fs/promises';
import * as path from 'path';

// apply global styles only to these classes
const permittedClasses = [
	'ant-modal-root',
	'ant-popover',
	'ant-select-dropdown',
	'ant-tooltip',
	'hrvyst-react',
];
// :where doesn't increase specifity, so avoids creating new conflicts
const wrapperSelector = `:where(${permittedClasses
	// apply to both the class itself (.c) and its contents (.c *)
	.map((c) => `.${c}, .${c} *`)
	.join(', ')})`;

const filesToWrap = [
	'./packages/hrvyst-react/node_modules/antd/lib/style/core/global.less',
	'./packages/hrvyst-react/node_modules/sanitize.css/sanitize.css',
];
for (const filePath of filesToWrap) {
	// resolve symlinks
	const realFilePath = await fs.realpath(path.resolve(filePath));

	// load original content
	const fileContent = await fs.readFile(realFilePath, { encoding: 'utf-8' });
	if (!fileContent.startsWith(wrapperSelector)) {
		await fs.writeFile(realFilePath, `${wrapperSelector} { ${fileContent} }`, {
			encoding: 'utf-8',
		});
	}
}
