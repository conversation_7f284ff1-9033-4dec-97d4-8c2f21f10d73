namespace RJO.BuildingBlocks.Common;

public class StartupLogger
{
	public static StartupLogger Current { get; set; } = new(x => Console.Out.WriteLine(x), x => Console.Error.WriteLine(x));

	readonly Action<string> _info;
	readonly Action<string> _error;

	public StartupLogger(Action<string> info, Action<string> error)
	{
		_info = info;
		_error = error;
	}

	public void Info(string message) => _info(Formatted(message));
	public void Error(string message) => _error(Formatted(message));

	static string Formatted(string message) => $"[{DateTime.Now:yyyy-MM-dd HH:mm:ssZ}] {message}";
}
