using Hangfire;
using Hangfire.Server;
using MediatR;
using RJO.CQGMarketData.WebApi.Application.Handlers;

namespace RJO.CQGMarketData.WebApi.Application.Jobs;

[Queue("default")]
public class RestartSessionJob
{
	readonly IMediator _mediator;

	public RestartSessionJob(IMediator mediator) => _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));

	public async Task Perform(RestartSessionJobOptions jobOptions, PerformContext performContext)
	{
		var restartSessionCommand = new RestartSessionCommand();
		var result = await _mediator.Send(restartSessionCommand);
	}
}
