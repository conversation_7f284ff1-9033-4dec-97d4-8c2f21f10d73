{"Sentry": {"Environment": "QA"}, "Azure": {"AppConfiguration": {"Endpoint": "https://qas-eastus-appconfiguration.azconfig.io"}, "KeyVault": {"Endpoint": "https://qas-eastus-rjohrvyst-kv.vault.azure.net/"}}, "Settings": {"Cqg": {"AllowedSubscriptions": "F.US.ZCE;F.US.ZSE;F.US.ZRE;F.US.KWE;F.US.ZWA", "ConversionFactor": "ZCE:0.01;ZSE:0.01;ZRE:1;KWE:0.01;ZWA:0.01", "HostName": "wss://demoapi.cqg.com:443", "PreloadSpreadSymbols": "F.US.ZCES1H22;F.US.ZCES2H22;F.US.ZCES1K22;F.US.ZCES2K22;F.US.ZSES1F22;F.US.ZSES2F22;F.US.ZSES1H22;F.US.ZSES2H22"}, "ApplicationFlags": {"RealTimeMarketDataLogEnabled": true, "TimeBarSubscriptionsLogEnabled": true}}, "HangfireAuth": {"ClientId": "99bd6cff-2e03-43bd-8f6d-03481288f3ba", "TenantId": "fa6d831d-dc2b-41a9-b180-55610cc31345", "ObjectId": "a8dc9d12-fa24-4579-922e-62451105a23e", "Instance": "https://login.microsoftonline.com/", "Domain": "https://qas-eastus-cqg-app.azurewebsites.net", "CallbackPath": "/signin-oidc"}}